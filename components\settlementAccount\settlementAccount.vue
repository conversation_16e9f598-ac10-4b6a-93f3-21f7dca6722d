<template>
    <view>
        <u-popup :show="accountSelectShow" mode="bottom" :mask-close-able="true" @close="closePopup" @open="getPaymethodsList">
            <view class="popup-content">
                <!-- 头部区域 -->
                <view class="header">
                    <text class="cancel" @click="closePopup">取消</text>
                    <!-- <text class="title">选择结算账户</text> -->
                    <text class="confirm" @click="handleConfirm">确定</text>
                </view>

                <!-- 自定义滑动选择器 -->
                <view class="custom-picker">
                    <view class="picker-mask"></view>
                    <view class="picker-indicator"></view>
                    <scroll-view scroll-y class="picker-content" :scroll-top="scrollTop" @scroll="handleScroll"
                        @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
                        <!-- 顶部空白占位 -->
                        <view class="picker-item placeholder"></view>
                        <view class="picker-item" v-for="(item, index) in accountList" :key="index"
                            :class="{ active: currentIndex === index }" :data-index="index">
                            {{ item.account_name }}
                        </view>
                        <!-- 底部空白占位 -->
                        <view class="picker-item placeholder"></view>
                    </scroll-view>
                </view>

				<!-- 新建图标 -->
                <view class="add_icon">
					<view class="new-icon" @click="newlyAdded">
						<i-add-one theme="outline" size="24" fill="#23a8f2" />
					</view>
				</view>
            </view>
        </u-popup>

        <!-- 新建弹出层 -->
        <addAccount :addShow="addShow" @refresh="getPaymethodsList" @close="closeAddPopup" />
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { getPaymethods } from '@/api/settlementAccount';
import addAccount from './components/addSettlementAccount.vue';

const ITEM_HEIGHT = 100; // 每项高度（rpx）

// 定义接口
interface AccountItem {
  id: number;
  account_name: string;
  [key: string]: any;
}

interface ScrollEvent {
  detail: {
    scrollTop: number;
  };
}

interface TouchEvent {
  touches: {
    pageY: number;
  }[];
}

// 定义 Props
const props = defineProps({
  accountSelectShow: {
    type: Boolean,
    default: false,
  },
});

// 定义 Emits
const emit = defineEmits(['close', 'confirm', 'update:accountSelectShow']);

// 响应式数据
const addShow = ref(false); // 新增
const scrollTop = ref(0); // 滚动位置
const currentIndex = ref(0); // 当前选中项索引
const startY = ref(0); // 触摸起始位置
const accountList = ref<AccountItem[]>([]);
const selectedItem = ref<AccountItem | null>(null); // 选中的项

// 生命周期钩子
onMounted(() => {
  // 初始化选中第一项
  if (accountList.value.length > 0) {
    selectedItem.value = accountList.value[0];
  }
});

// Watcher for accountSelectShow prop
watch(() => props.accountSelectShow, (newVal) => {
  if (newVal) {
    getPaymethodsList();
  }
});

// 方法
/**
 * 获取支付方式列表
 */
const getPaymethodsList = async () => {
  console.log('开始获取结算账户列表');
  try {
    const res: any = await getPaymethods();
    console.log('结算账户API响应:', res);
    if (res.code === 0) {
      accountList.value = res.data.results || res.data || [];
      if (accountList.value.length > 0) {
        selectedItem.value = accountList.value[0];
        console.log('已设置默认选中项:', selectedItem.value);
      } else {
        console.log('结算账户列表为空');
        uni.showToast({
          title: '暂无可用的结算账户',
          icon: 'none'
        });
      }
    } else {
      console.error('获取结算账户失败:', res.msg);
      uni.showToast({
        title: res.msg || '获取结算账户失败',
        icon: 'none'
      });
    }
  } catch (err) {
    console.error('获取结算账户请求失败:', err);
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    });
  }
};

/**
 * 处理滚动
 * @param e - 滚动事件
 */
const handleScroll = (e: ScrollEvent) => {
  const scrollTopVal = e.detail.scrollTop;
  const index = Math.round(scrollTopVal / uni.upx2px(ITEM_HEIGHT));
  if (index !== currentIndex.value && index >= 0 && index < accountList.value.length) {
    currentIndex.value = index;
    selectedItem.value = accountList.value[index];
  }
};

/**
 * 处理触摸开始
 * @param e - 触摸事件
 */
const handleTouchStart = (e: TouchEvent) => {
  startY.value = e.touches[0].pageY;
};

/**
 * 处理触摸移动
 * @param e - 触摸事件
 */
const handleTouchMove = (e: TouchEvent) => {
  const moveY = e.touches[0].pageY;
  const diff = moveY - startY.value;
  const newScrollTop = scrollTop.value - diff;
  scrollTop.value = Math.max(0, Math.min(newScrollTop, (accountList.value.length - 1) * uni.upx2px(ITEM_HEIGHT)));
  startY.value = moveY;
};

/**
 * 处理触摸结束
 */
const handleTouchEnd = () => {
  // 滚动到最近的选项
  const targetScrollTop = currentIndex.value * uni.upx2px(ITEM_HEIGHT);
  scrollTop.value = targetScrollTop;
};

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedItem.value) {
    console.log(selectedItem.value);
    emit('confirm', selectedItem.value);
  }
  closePopup();
};

/**
 * 关闭弹窗
 */
const closePopup = () => {
  emit('update:accountSelectShow', false);
  emit('close');
};

/**
 * 新增
 */
const newlyAdded = () => {
  console.log(111);
  addShow.value = true;
};

/**
 * 关闭新增弹出层
 */
const closeAddPopup = () => {
  addShow.value = false;
};
</script>

<style scoped>
.popup-content {
    width: 100%;
    background: #fff;
    padding: 20rpx;
    height: 600rpx;
}

.header {
    padding: 10rpx;
    width: 93%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
}

.cancel {
    color: #666;
    font-size: 28rpx;
    padding: 0 20rpx;
}

.confirm {
    color: #7fafff;
    font-size: 28rpx;
    padding: 0 20rpx;
}

.custom-picker {
    position: relative;
    height: 500rpx;
    overflow: hidden;
}

.picker-mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    background-image: linear-gradient(180deg,
            rgba(255, 255, 255, 0.9),
            rgba(255, 255, 255, 0.4) 45%,
            rgba(255, 255, 255, 0.4) 55%,
            rgba(255, 255, 255, 0.9));
    pointer-events: none;
}

.picker-indicator {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    height: 100rpx;
    transform: translateY(-50%);
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    z-index: 2;
    pointer-events: none;
}

.picker-content {
    height: 100%;
    box-sizing: border-box;
}

.picker-item {
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 32rpx;
    color: #333;
    transition: all 0.2s;
}

.picker-item.placeholder {
    height: 200rpx;
}

.picker-item.active {
    color: #2979ff;
    font-size: 36rpx;
    font-weight: bold;
}
.add_icon {
	position: absolute;
	bottom: 60rpx;
	right: 70rpx;
	z-index: 111;
}
/*.title{
    font-size: 28rpx;
    font-weight: 700;
}*/
</style>