<template>
  <view>
    <u-popup v-model:show="CategoryShow" mode="bottom" @close="closeProductCategory" @open="loadCategories">
      <view class="container">
        <view class="header">
          <view class="categoryConfirmationBtn">
            <view @click="closeProductCategory">取消</view>
            <view class="popup_title">{{ title }}</view>
            <view class="blueFont" @click="confirmCategory">确认</view>
          </view>
          <view class="divider"></view>
        </view>
        <scroll-view class="content" scroll-y @scrolltolower="onReachBottom">
          <!-- 一级分类循环 -->
          <view class="category" v-for="item in categoryList" :key="item.id">
            <!-- 图标部分 -->
            <view class="scalableIcon" @click="expandOrNot(item)">
              <i-down-one theme="filled" size="24" fill="#b2b2b2" v-if="item.scalableIconShow" />
              <i-right-one theme="filled" size="24" fill="#b2b2b2" v-else />
            </view>
            <!-- 单选区域 -->
            <view class="selectArea">
              <!-- 一级分类 -->
              <view class="levelOne" @click="showActions(item)">
                <view class="levelOne_left">
                  <view class="levelOneName" :class="{ 'active-text': item.showActions }">{{ item.name }}</view>
                </view>
                <!-- 编辑部分 -->
                <view class="actions" v-if="item.showActions">
                  <view class="action-btn edit" @click.stop="editPdCate(item, 1)">编辑</view>
                  <view class="action-btn delete" @click.stop="deletePdCate(item)">删除</view>
                </view>
              </view>

              <!-- 二级分类 -->
              <template v-if="item.scalableIconShow">
                <view class="levelTwo" v-for="(item2, index2) in item.children" :key="item2.id"
                  @click="showActions(item2, index2, item)">
                  <view class="levelTwo_left">
                    <view class="circleTool" :class="{
                      'active-circle':
                        categoryName === item2.name || item2.showActions,
                    }">
                      <view :class="{
                        'selected-circle': item2.showActions,
                      }"></view>
                    </view>
                    <view class="levelTwoName" :class="{
                      'active-text-two': item2.showActions,
                    }">{{ item2.name }}</view>
                  </view>
                  <!-- 编辑部分 -->
                  <view class="actions" v-if="item2.showActions">
                    <view class="action-btn edit" @click.stop="editPdCate(item2)">编辑</view>
                    <view class="action-btn delete" @click.stop="deletePdCate(item2)">删除</view>
                  </view>
                </view>
              </template>
            </view>
          </view>

          <!-- 加载更多 -->
          <view class="loading-more" v-if="isLoading">
            <u-loading-icon></u-loading-icon>
            <text class="loading-text">正在加载...</text>
          </view>
        </scroll-view>

        <view class="addBtn">
          <view @click="editPdCate()">
            <i-add-one theme="outline" size="24" fill="#5fa3eb" />
          </view>
        </view>
      </view>
    </u-popup>

    <edit :editShow="editShow" :editInfo="editInfo" :categoryList="categoryList" :CategoryName="queryCategoryName"
      @update:show="updateParentShow" @refresh="handleRefresh" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue';
import edit from "./components/editCategory.vue";
import { getCatalogtypes, deleteCatalogtypes } from "@/api/category";

// 定义接口
interface CategoryItem {
  id: string;
  name: string;
  scalableIconShow: boolean;
  showActions: boolean;
  children?: CategoryItem[];
  parent?: string | null;
}

interface EditInfo {
  editName: string;
  editId: string | null;
  editParent: string | null;
  editParentName: string | null;
  for_type: string;
}

// 定义 props
const props = defineProps({
  CategoryShow: {
    type: Boolean,
    default: false,
  },
  queryCategoryName: {
    type: String,
    default: "",
  },
});

// 定义 emits
const emit = defineEmits(['update:CategoryShow', 'confirm']);

// 响应式数据
const title = ref('选择类别');
const CategoryShow = ref(props.CategoryShow); // 商品类别弹出层是否展示
const editShow = ref(false); // 编辑弹出层是否展示
const editInfo = reactive<EditInfo>({
  editName: "", // 编辑的分类名称
  editId: null, // 编辑的分类id
  editParent: null, // 编辑的分类父id
  editParentName: null, // 编辑的分类父name
  for_type: '', // 修改分类的类型
});
const categoryName = ref<string | null>(null); // 分类选中值name
const categoryId = ref<string | null>(null); // 分类选中值id
const pageParams = reactive({
  page: 1,
  page_size: 500,
  for_type: "",
});
const isLoading = ref(false); // 是否正在加载
const hasMore = ref(true); // 是否还有更多数据
const categoryList = ref<CategoryItem[]>([]); // 分类列表数据

// 监听 CategoryShow prop 的变化
watch(() => props.CategoryShow, (newValue) => {
  CategoryShow.value = newValue;
  if (newValue) {
    // 打开弹窗时加载数据
    pageParams.page = 1;
    categoryList.value = [];
    loadCategories();

    if (props.queryCategoryName === 'item') {
      title.value = '选择物品分类';
    } else if (props.queryCategoryName === 'supplier') {
      title.value = '选择供应商分类';
    } else if (props.queryCategoryName === 'customer') {
      title.value = '选择客户分类';
    }
  }
});

// 方法
/**
 * 加载分类数据
 */
const loadCategories = async () => {
  if (!hasMore.value || isLoading.value) return;

  isLoading.value = true;
  pageParams.for_type = props.queryCategoryName;

  try {
    const response = await getCatalogtypes(pageParams);
    const newCategories = response.data.results.map((item: any) => ({
      id: item.id,
      name: item.name,
      scalableIconShow: false,
      showActions: false,
      children: item.children || [],
      parent: item.parent,
    }));

    if (pageParams.page === 1) {
      categoryList.value = newCategories;
    } else {
      categoryList.value = [...categoryList.value, ...newCategories];
    }

    hasMore.value = response.next !== null;
    if (hasMore.value) {
      pageParams.page++;
    }
  } catch (error) {
    console.error("加载分类失败:", error);
    uni.showToast({
      title: "加载失败，请重试",
      icon: "none",
    });
  } finally {
    isLoading.value = false;
  }
};

/**
 * 展开或收起子分类
 * @param item - 分类项
 */
const expandOrNot = (item: CategoryItem) => {
  item.scalableIconShow = !item.scalableIconShow;
};

/**
 * 监听滚动到底部
 */
const onReachBottom = () => {
  if (hasMore.value && !isLoading.value) {
    loadCategories();
  }
};

/**
 * 重置分类选中状态
 */
const resetCategory = () => {
  categoryName.value = null;
  categoryId.value = null;
  categoryList.value.forEach((category) => {
    category.showActions = false;
    category.scalableIconShow = false;
    if (Array.isArray(category.children)) {
      category.children.forEach((child) => {
        if (typeof child === "object") {
          child.showActions = false;
        }
      });
    }
  });
};

/**
 * 确认选择的分类
 */
const confirmCategory = () => {
  if (categoryId.value !== null && categoryName.value !== null) {
    const category = {
      id: categoryId.value,
      name: categoryName.value,
    };
    emit("confirm", category);
  } else {
    uni.showToast({
      title: "请选择商品类别",
      icon: "none",
    });
    return;
  }
  CategoryShow.value = false;
  emit("update:CategoryShow", CategoryShow.value);
  resetCategory();
};

/**
 * 关闭商品类别弹出层
 */
const closeProductCategory = () => {
  CategoryShow.value = false;
  emit("update:CategoryShow", CategoryShow.value);
  resetCategory();
};

/**
 * 编辑或添加分类
 * @param item - 要编辑的分类项，默认为空对象表示新增
 * @param index - 可选参数，用于区分一级分类和二级分类
 */
const editPdCate = (item: Partial<CategoryItem> = {}, index: number = 0) => {
  console.log(item);
  editInfo.editName = item?.name || "";
  editInfo.editId = item?.id || null;
  editInfo.editParent = null;
  editInfo.editParentName = null;
  editInfo.for_type = props.queryCategoryName;

  if (item.parent) {
    const parentCategory = categoryList.value.find(cat => cat.id === item.parent);
    if (parentCategory) {
      editInfo.editParent = parentCategory.id;
      editInfo.editParentName = parentCategory.name;
    }
  }

  editShow.value = true;
  console.log(editInfo);
};

/**
 * 更新父组件的显示状态
 * @param newMessage - 新的显示状态
 */
const updateParentShow = (newMessage: boolean) => {
  editShow.value = newMessage;
  handleRefresh();
};

/**
 * 显示操作按钮并记录选中分类
 * @param item - 当前分类项
 * @param childIndex - 子分类索引
 * @param parent - 父分类项
 */
const showActions = (item: CategoryItem, childIndex?: number, parent?: CategoryItem) => {
  // 关闭所有其他项的操作按钮
  categoryList.value.forEach((category) => {
    category.showActions = false;
    if (Array.isArray(category.children)) {
      category.children.forEach((child) => {
        if (typeof child === "object") {
          child.showActions = false;
        }
      });
    }
  });

  // 显示当前项的操作按钮，并记录名称
  if (childIndex !== undefined && parent) {
    // 如果是子项
    parent.children![childIndex].showActions = true;
    categoryName.value = parent.children![childIndex].name;
    categoryId.value = parent.children![childIndex].id;
  } else {
    // 如果是父项
    item.showActions = true;
    categoryName.value = item.name;
    categoryId.value = item.id;
  }
};

/**
 * 删除分类
 * @param item - 要删除的分类项
 */
const deletePdCate = (item: CategoryItem) => {
  editInfo.editId = item.id;
  editInfo.for_type = props.queryCategoryName;

  uni.showModal({
    title: "确认删除",
    content: `确定要删除\"${item.name}\"分类吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const res = await deleteCatalogtypes(editInfo);
          console.log('删除分类响应:', res);
          if (res.code === 0) {
            uni.showToast({
              title: "删除成功",
              icon: "success",
            });
          } else {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
          handleRefresh();
        } catch (err: any) {
          console.error('删除分类错误:', err);
          uni.showToast({
            title: err.msg || err.message || "删除失败",
            icon: "none",
            duration: 3000
          });
        }
      }
    },
  });
};

/**
 * 处理刷新事件
 */
const handleRefresh = () => {
  pageParams.page = 1;
  hasMore.value = true;
  loadCategories();
};
</script>

<style lang="scss" scoped>
.container {
  height: 600rpx;
  display: flex;
  flex-direction: column;
}

.header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
}

//商品类别弹出层
.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 0 20rpx;
  height: calc(100% - 100rpx); // 减去头部高度
}

.category {
  display: flex;
}

.selectArea {
  width: 90%;
  display: flex;
  flex-direction: column;

  .levelOne,
  .levelTwo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx;
    border-radius: 4rpx;
    transition: all 0.3s;

    &:active {
      background-color: #f5f5f5;
    }
  }

  .levelTwo {
    margin-left: 30rpx;
    font-size: 25rpx;
  }

  .levelOne_left,
  .levelTwo_left {
    display: flex;
    align-items: center;
    gap: 15rpx;
  }

  .levelOneName {
    font-size: 28rpx;
  }

  .levelTwoName {
    font-size: 25rpx;
    transition: all 0.3s;
  }

  .active-text {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
  }

  .active-text-two {
    font-size: 25rpx;
    font-weight: bold;
    color: #333;
  }
}

.circleTool {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #b2b2b2;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  position: relative;
  flex-shrink: 0;
}

.active-circle {
  width: 8px;
  height: 8px;
  border-color: #3982cf;
  border-width: 2px;
}

.selected-circle {
  width: 6px;
  height: 6px;
  background-color: #3982cf;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  transition: all 0.3s;

  &.edit {
    color: #2b85e4;

    &:active {
      background-color: rgba(43, 133, 228, 0.1);
    }
  }

  &.delete {
    color: #ff4d4f;

    &:active {
      background-color: rgba(255, 77, 79, 0.1);
    }
  }
}

.scalableIcon {
  margin-right: 10rpx;
  margin-top: 10rpx;
}

.newProductCategory {
  width: 95%;
  display: flex;
  flex-direction: row-reverse;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;

  .loading-text {
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #999;
  }
}

.addBtn {
  width: 90%;
  height: 60rpx;
  display: flex;
  flex-direction: row-reverse;
  margin: 10rpx auto;
}
</style>