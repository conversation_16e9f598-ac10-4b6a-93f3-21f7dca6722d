<template>
  <view class="goodsList">
    <scroll-view
      class="scroll-Y"
      :style="{ height: scrollViewHeight + 'px' }"
      scroll-y="true"
      @scrolltolower="loadMore"
      v-if="(type !== 1 && type !== 2 && type !== 5 && type !== 6 && type !== 10) || goodsList.length > 0"
    >
      <view
        class="goods_item"
        :class="{ goods_item_disabled: item.is_active === false || item.isWarehouse === false }"
        v-for="(item, index) in goodsList"
        :key="index"
        @click="selectProduct(item)"
      >
        <view class="goods_theme">
          <view class="goods_img" v-if="item.image">
            <image :src="item.image" mode="aspectFit"></image>
          </view>
          <view class="goods_title">
            <view class="goods_name">
              <text
                :class="{
                  'text-disabled': item.is_active === false || item.isWarehouse === false
                }"
                >{{ item.name || item.item_name }}</text
              >
              <view
                class="disabled-tag"
                v-if="item.is_active === false || item.isWarehouse === false"
              >
                <u-icon name="info-circle" size="12" color="#999"></u-icon>
                <text>停用</text>
              </view>
            </view>
            <view class="goods_id"> 编号：{{ item.code }} </view>
          </view>
          <view
            class="select-goods"
            v-if="item.isSelected && (type == 0 || type == 3 || type == 7 || type == 8)"
          >
            <u-icon name="checkmark" color="#2b9939" size="20"></u-icon>
          </view>
        </view>
        <view class="goods_info">
          <view class="goods_attributes">
            <view class="goods_attribute">
              <text class="goods_attribute_name">零售价：</text>
              <text>￥{{ $toFixed2(item.retail_price) }}</text>
            </view>
            <view class="goods_attribute">
              <text class="goods_attribute_name">库存：</text>
              <text>{{ item.stock }}</text>
            </view>
            <view class="goods_attribute" v-if="item.quantity">
              <text class="goods_attribute_name">数量：</text>
              <text>{{ item.quantity }}</text>
            </view>
            <view class="goods_attribute" v-if="item.金额">
              <text class="goods_attribute_name">金额：</text>
              <text>{{ item.金额 }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 退货单显示仓库 -->
      <view
        class="sales-item"
        v-for="(item, index) in goodsList"
        :key="'sales-' + index"
        v-if="type == 4"
        @click="selectProduct(item)"
      >
        <text>{{ item.name || item.item_name }}</text>
        <text>仓库：{{ item.warehouse_name }}</text>
        <text>库存余量：{{ item.stock }}</text>
      </view>

      <!-- 零售退货显示仓库 -->
      <view
        class="sales-item"
        v-for="(item, index) in goodsList"
        :key="'sales-' + index"
        v-if="type == 5"
        @click="selectProduct(item)"
      >
        <text>{{ item.name || item.item_name }}</text>
        <text>仓库：{{ item.warehouse_name }}</text>
        <text>库存余量：{{ item.stock }}</text>
      </view>

      <view class="loading-more" v-if="loading">
        <u-loading mode="circle" size="20"></u-loading>
        <text class="status-text">加载中...</text>
      </view>
      <view class="no-more" v-else-if="!hasMore">没有更多了</view>
      <view class="empty" v-if="goodsList.length === 0 && !loading">
        <u-empty text="暂无数据" mode="list"></u-empty>
      </view>
    </scroll-view>

    <productDetails
      v-if="productDetailsShow"
      :productData="productData"
      :type="type"
      @close="closeSelectPopup"
      @confirm="confirmSelectProduct"
    ></productDetails>

    <!-- 出库记录弹窗 -->
    <u-popup v-model="goodsExitRecord" mode="center" width="90%" height="80%">
      <view class="popup-title-container">
        <view class="popup-title">
          <text class="popup-title-text">出库记录</text>
        </view>
        <u-icon
          class="close-icon"
          name="close"
          size="24"
          @click="goodsExitRecord = false"
        ></u-icon>
      </view>
      <scroll-view style="height: 90%" scroll-y>
        <view class="sales-item" v-for="(item, index) in recordList" :key="index">
          <text>{{ item.name }}</text>
          <text>时间：{{ item.create_time }}</text>
          <text>数量：{{ item.quantity }}</text>
        </view>
      </scroll-view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onShow } from 'vue';
import productDetails from './productDetails.vue';

// API imports
import {
  getGoods,
  getGoodsDetail,
  getGoodsPages,
  entranceAndExitRecord
} from '@/api/goods';

import { $toFixed2 } from '@/utils/format';
import eventBus from '@/utils/eventBus';

interface ProductUnit {
  name: string;
  value: string;
}

interface ProductStock {
  warehouse: string;
  warehouse_name: string;
  stock: number;
}

interface GoodsItem {
  id?: string;
  item?: string;
  code: string;
  name?: string;
  item_name?: string;
  retail_price: number;
  stock: number;
  quantity?: number;
  金额?: number;
  is_active?: boolean;
  isWarehouse?: boolean;
  isSelected?: boolean;
  unit?: string;
  image?: string;
  warehouse?: string;
  warehouse_name?: string;
  // 添加销货单相关字段
  price?: number;
  wholesale_price?: number;
  conversion_rate?: number;
  remaining_stock?: number;
  total?: number;
  total_cost?: number;
}

interface PageData {
  page: number;
  page_size: number;
  extra_param: {
    warehouse_id?: string;
    [key: string]: any; // 允许其他额外参数
  };
}

// 定义组件的props
const props = defineProps({
  type: {
    type: Number,
    default: 0
  },
  includeInactive: {
    type: Boolean,
    default: false
  },
  warehouseData: {
    type: Object,
    default: () => null
  },
  searchResults: {
    type: Array,
    default: () => []
  },
  relatedOrderGoods: {
    type: Array,
    default: () => []
  },
  deselectItem: {
    type: Object,
    default: () => null
  },
  deselectItemList: {
    type: Array,
    default: () => []
  },
  isGoodsManageRefresh: {
    type: Boolean,
    default: false
  },
  returnWarehouse: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件的emits
const emit = defineEmits(['selectedGoodsList', 'selectType']);

// 响应式数据
const goodsListParams = reactive({
  page: 1,
  page_size: 20,
  warehouse_id: '',
  is_active: true
});

const pageData: PageData = reactive({
  page: 1,
  page_size: 20,
  extra_param: {
    warehouse_id: ''
  }
});

const goodsList = ref<GoodsItem[]>([]);
const loading = ref(false);
const hasMore = ref(true);
const selectGoodsList = ref<GoodsItem[]>([]);
const productDetailsShow = ref(false);
const productData = ref<GoodsItem | null>(null);
const returnWarehouse = ref({
  warehouse: '',
  warehouse_name: ''
});
const goodsExitRecord = ref(false);
const recordList = ref([]);
const cumulativeReturnGoods = ref([]);
const isShowPayAttentionTo = ref(false);
const selectedGoodsQuantity = ref({});
const isShowUnit = ref(true);
const scrollViewHeight = ref(0);

// 过滤仓库数据
const filterItemsForWarehouse = () => {
  console.log('过滤仓库数据', props.warehouseData);
  if (props.warehouseData && props.warehouseData.warehouse) {
    goodsList.value = goodsList.value.filter(
      item => item.warehouse === props.warehouseData.warehouse
    );
    selectGoodsList.value = selectGoodsList.value.filter(
      item => item.warehouse === props.warehouseData.warehouse
    );
  }
};

// 监听props变化
watch(
  () => props.includeInactive,
  newVal => {
    goodsListParams.is_active = !newVal;
    pageData.extra_param.is_active = !newVal;
    getGoodsList();
  }
);

watch(
  () => props.warehouseData,
  newVal => {
    console.log('仓库数据变化', newVal);
    if (newVal && newVal.warehouse) {
      goodsListParams.warehouse_id = newVal.warehouse;
      pageData.extra_param.warehouse_id = newVal.warehouse;
      // 如果是退货单类型，需要重新获取数据
      if (props.type === 1 || props.type === 2 || props.type === 5 || props.type === 6 || props.type === 10) {
        getGoodsList();
      } else {
        filterItemsForWarehouse();
      }
    }
  }
);

watch(
  () => props.searchResults,
  newVal => {
    if (newVal && newVal.length > 0) {
      goodsList.value = newVal as GoodsItem[];
    }
  }
);

watch(
  () => props.relatedOrderGoods,
  (newVal, oldVal) => {
    console.log('关联订单商品变化', newVal);
    if (newVal && newVal.length > 0) {
      // 处理关联订单商品
      const processedGoods = newVal.map((item: any) => {
        // 确保item字段存在
        if (!item.item) {
          item.item = item.id || item.code;
        }
        return item;
      });
      
      // 更新goodsList
      goodsList.value = processedGoods as GoodsItem[];
      
      // 更新selectGoodsList
      selectGoodsList.value = [...processedGoods] as GoodsItem[];
      
      // 标记为已选择
      goodsList.value.forEach(item => {
        item.isSelected = true;
      });
      
      // 发送更新后的商品列表
      emit('selectedGoodsList', selectGoodsList.value);
    }
  }
);

watch(
  () => props.deselectItem,
  newVal => {
    if (newVal) {
      const index = selectGoodsList.value.findIndex(
        item => item.item === newVal.item && item.unit === newVal.unit
      );
      if (index > -1) {
        selectGoodsList.value.splice(index, 1);
      }
      const indexGoods = goodsList.value.findIndex(
        item => item.item === newVal.item && item.unit === newVal.unit
      );
      if (indexGoods > -1) {
        goodsList.value[indexGoods].isSelected = false;
      }
      emit('selectedGoodsList', selectGoodsList.value);
    }
  }
);

watch(
  () => props.deselectItemList,
  newVal => {
    if (newVal && newVal.length > 0) {
      newVal.forEach((item: any) => {
        const index = selectGoodsList.value.findIndex(
          selectItem => selectItem.item === item.item && selectItem.unit === item.unit
        );
        if (index > -1) {
          selectGoodsList.value.splice(index, 1);
        }
        const indexGoods = goodsList.value.findIndex(
          goodsItem => goodsItem.item === item.item && goodsItem.unit === item.unit
        );
        if (indexGoods > -1) {
          goodsList.value[indexGoods].isSelected = false;
        }
      });
      emit('selectedGoodsList', selectGoodsList.value);
    }
  }
);

watch(
  () => props.isGoodsManageRefresh,
  newVal => {
    if (newVal) {
      getGoodsList();
    }
  }
);

watch(
  () => props.returnWarehouse,
  newVal => {
    if (newVal && newVal.warehouse) {
      returnWarehouse.value = newVal;
      // 如果是退货单类型，需要重新获取数据
      if (props.type === 4 || props.type === 5) {
        getGoodsList();
      }
    }
  },
  { deep: true }
);

// 在onShow钩子中过滤仓库数据
onShow(() => {
  filterItemsForWarehouse();
});

// created生命周期钩子
// created() {
//   console.log('goodsList created', this.type);
//   // 销货单类型(10)特殊处理
//   if (this.type === 10) {
//     console.log('销货单类型初始化');
//     // 设置仓库参数
//     if (this.warehouseData && this.warehouseData.warehouse) {
//       console.log('设置销货单仓库参数:', this.warehouseData.warehouse);
//       this.goodsListParams.warehouse_id = this.warehouseData.warehouse;
//       this.pageData.extra_param.warehouse_id = this.warehouseData.warehouse;
//     }
//   }
//   // 其他类型初始化
//   else if (this.type === 0 || this.type === 3 || this.type === 6 || this.type === 7 || this.type === 8) {
//     console.log(`类型${this.type}初始化`);
//     // 设置仓库参数
//     if (this.warehouseData && this.warehouseData.warehouse) {
//       console.log('设置仓库参数:', this.warehouseData.warehouse);
//       this.goodsListParams.warehouse_id = this.warehouseData.warehouse;
//       this.pageData.extra_param.warehouse_id = this.warehouseData.warehouse;
//     }
//     // 获取商品列表
//     this.getGoodsList();
//   }
// }

// onShow生命周期钩子
onShow(() => {
  // 注册事件总线
  eventBus.on('refreshGoodsList', () => {
    getGoodsList();
  });
  
  // 销货单类型(10)特殊处理
  if (props.type === 10) {
    console.log('销货单onShow处理');
    // 设置仓库参数
    if (props.warehouseData && props.warehouseData.warehouse) {
      console.log('设置销货单仓库参数:', props.warehouseData.warehouse);
      goodsListParams.warehouse_id = props.warehouseData.warehouse;
      pageData.extra_param.warehouse_id = props.warehouseData.warehouse;
    }
    // 获取商品列表
    getGoodsList();
  }
  // 其他类型处理
  else if (props.type === 0 || props.type === 3 || props.type === 6 || props.type === 7 || props.type === 8) {
    console.log(`类型${props.type}onShow处理`);
    // 设置仓库参数
    if (props.warehouseData && props.warehouseData.warehouse) {
      console.log('设置仓库参数:', props.warehouseData.warehouse);
      goodsListParams.warehouse_id = props.warehouseData.warehouse;
      pageData.extra_param.warehouse_id = props.warehouseData.warehouse;
    }
    // 获取商品列表
    getGoodsList();
  }
});

// mounted生命周期钩子
onMounted(() => {
  console.log(props.type);

  initScrollViewHeight();

  if (props.type == 0 || props.type == 3 || props.type == 7 || props.type == 8) {
    isShowUnit.value = false;
  }
  if (props.type == 6) {
    goodsListParams.warehouse_id = uni.getStorageSync("retailWarehouseId");
    pageData.extra_param.warehouse_id = uni.getStorageSync("retailWarehouseId");
  }

  if (props.warehouseData && props.warehouseData.warehouse) {
    console.log('设置仓库ID参数:', props.warehouseData.warehouse);
    goodsListParams.warehouse_id = props.warehouseData.warehouse;
    pageData.extra_param.warehouse_id = props.warehouseData.warehouse;
  }

  if (props.type == 1 || props.type == 2 || props.type == 6 || props.type == 5) {
    getGoodsList();
  }
});

// 销货单数据处理
const initSalesOrderData = (data: any): GoodsItem[] => {
  console.log('处理销货单数据:', data);
  
  if (!data || !data.results) {
    console.error('销货单数据格式不正确');
    return [];
  }
  
  try {
    // 处理每个商品项
    const processedItems = data.results.map((item: any) => {
      // 确保基本字段存在
      const processedItem: GoodsItem = {
        id: item.id || item.item || item.code,
        item: item.item || item.id || item.code,
        code: item.code || '',
        name: item.name || item.item_name || '',
        item_name: item.item_name || item.name || '',
        retail_price: parseFloat(item.retail_price) || 0,
        stock: parseFloat(item.stock) || 0,
        // 销货单特有字段
        price: parseFloat(item.price) || parseFloat(item.retail_price) || 0,
        wholesale_price: parseFloat(item.wholesale_price) || 0,
        quantity: parseFloat(item.quantity) || 0,
        total: parseFloat(item.total) || 0,
        warehouse: item.warehouse || (props.warehouseData ? props.warehouseData.warehouse : ''),
        warehouse_name: item.warehouse_name || (props.warehouseData ? props.warehouseData.warehouse_name : ''),
      };
      
      console.log('处理后的销货单商品项:', processedItem);
      return processedItem;
    });
    
    console.log('销货单数据处理完成，共处理:', processedItems.length, '项');
    return processedItems;
  } catch (err) {
    console.error('处理销货单数据出错:', err);
    return [];
  }
};

// 关闭弹窗
const close = () => {
  goodsExitRecord.value = false;
};

// 处理注意事项
const handlePayAttentionTo = (val: boolean) => {
  isShowPayAttentionTo.value = val;
};

// 获取历史类型文本
const getHistoryTypeText = (type: number): string => {
  const typeMap: { [key: number]: string } = {
    1: '采购订单',
    2: '采购入库',
    3: '采购退货',
    4: '销货订单',
    5: '销售出库',
    6: '销售退货',
    7: '零售订单',
    8: '零售出库',
    9: '零售退货',
    10: '盘点'
  };
  return typeMap[type] || '未知类型';
};

// 记录
const record = (item: GoodsItem) => {
  console.log(item);
  const params = {
    item: item.item,
    warehouse: item.warehouse
  };
  entranceAndExitRecord(params).then((res: any) => {
    if (res.code == 0) {
      recordList.value = res.data;
      goodsExitRecord.value = true;
    }
  });
};

// 处理退货仓库数据
const processReturnWarehouseData = (data: any) => {
  console.log('处理退货仓库数据', data);
  if (data && data.warehouse) {
    returnWarehouse.value.warehouse = data.warehouse;
    returnWarehouse.value.warehouse_name = data.warehouse_name;
    
    // 过滤商品列表
    if (data.warehouse) {
      goodsList.value = goodsList.value.filter(
        item => item.warehouse === data.warehouse
      );
    }
  }
};

// 映射商品项
const mapGoodsItems = (items: any[]): GoodsItem[] => {
  console.log('映射商品项', items);
  
  if (!items || !Array.isArray(items)) {
    console.error('传入的商品数据不是有效数组');
    return [];
  }
  
  try {
    return items.map(item => {
      // 直接退货类型(10)特殊处理
      if (props.type === 10) {
        console.log('处理直接退货类型商品');
        return {
          id: item.id || item.item || item.code,
          item: item.item || item.id || item.code,
          code: item.code || '',
          name: item.name || item.item_name || '',
          item_name: item.item_name || item.name || '',
          retail_price: parseFloat(item.retail_price) || 0,
          stock: parseFloat(item.stock) || 0,
          // 销货单特有字段
          price: parseFloat(item.price) || parseFloat(item.retail_price) || 0,
          wholesale_price: parseFloat(item.wholesale_price) || 0,
          quantity: parseFloat(item.quantity) || 0,
          total: parseFloat(item.total) || 0,
          warehouse: item.warehouse || (props.warehouseData ? props.warehouseData.warehouse : ''),
          warehouse_name: item.warehouse_name || (props.warehouseData ? props.warehouseData.warehouse_name : ''),
          isSelected: false
        };
      }
      
      // 销售订单详情类型(5)特殊处理
      if (props.type === 5) {
        console.log('处理销售订单详情类型商品');
        // 确保字段存在
        const normalizedItem = {
          ...item,
          id: item.id || item.item || item.code,
          item: item.item || item.id || item.code,
          code: item.code || '',
          name: item.name || item.item_name || '',
          item_name: item.item_name || item.name || '',
          retail_price: parseFloat(item.retail_price) || 0,
          stock: parseFloat(item.stock) || 0,
          warehouse: item.warehouse || '',
          warehouse_name: item.warehouse_name || ''
        };
        
        console.log('标准化后的商品:', normalizedItem);
        return normalizedItem;
      }
      
      // 其他类型保持原有逻辑
      return {
        ...item,
        id: item.id || item.item || item.code,
        item: item.item || item.id || item.code,
        code: item.code || '',
        name: item.name || item.item_name || '',
        item_name: item.item_name || item.name || '',
        retail_price: parseFloat(item.retail_price) || 0,
        stock: parseFloat(item.stock) || 0,
        isSelected: false
      };
    });
  } catch (err) {
    console.error('映射商品项时出错:', err);
    return [];
  }
};

// 处理API响应数据
const processApiResponseData = (data: any): any[] => {
  console.log('处理API响应数据:', data);
  
  if (!data || !data.results || !Array.isArray(data.results)) {
    console.error('API响应数据格式不正确');
    return [];
  }
  
  // 确保每个商品项都有正确的id字段
  return data.results.map((item: any) => {
    // 创建新对象避免修改原始数据
    const processedItem = { ...item };
    
    // 确保id字段存在且正确
    if (!processedItem.id) {
      processedItem.id = processedItem.item || processedItem.code;
    }
    
    // 确保item字段存在
    if (!processedItem.item) {
      processedItem.item = processedItem.id || processedItem.code;
    }
    
    console.log(`处理商品项: ${processedItem.name || processedItem.item_name}, id: ${processedItem.id}, item: ${processedItem.item}`);
    return processedItem;
  });
};

// 初始化滚动视图高度
const initScrollViewHeight = () => {
  uni.getSystemInfo({
    success: (res) => {
      const systemInfo = res;
      const customBarHeight = 0; // 如果有自定义导航栏，需要获取其高度
      scrollViewHeight.value = systemInfo.windowHeight - customBarHeight;
      console.log('设置滚动视图高度:', scrollViewHeight.value);
    }
  });
};

// 获取商品列表
const getGoodsList = () => {
  console.log('获取商品列表', goodsListParams);
  loading.value = true;
  
  // 销货单类型(10)特殊处理
  if (props.type === 10) {
    console.log('获取销货单商品列表');
    // 使用销货单数据处理方法
    // 注意：这里可能需要调用不同的API或传递不同的参数
    getGoodsPages(pageData).then((res: any) => {
      console.log('销货单商品列表响应:', res);
      if (res.code == 0) {
        // 使用销货单数据处理方法
        const processedData = initSalesOrderData(res.data);
        goodsList.value = processedData;
        hasMore.value = res.data.next !== null;
      }
      loading.value = false;
    }).catch((err: any) => {
      console.error('获取销货单商品列表失败:', err);
      loading.value = false;
    });
    return;
  }
  
  // 采购订单(1)、采购入库(2)、销售退货(6)、销售订单详情(5)类型处理
  if (props.type === 1 || props.type === 2 || props.type === 6 || props.type === 5) {
    console.log(`获取类型${props.type}商品列表`);
    // 设置仓库参数
    if (props.warehouseData && props.warehouseData.warehouse) {
      console.log('设置仓库参数:', props.warehouseData.warehouse);
      goodsListParams.warehouse_id = props.warehouseData.warehouse;
    }
    
    // 如果是销售退货(6)且没有仓库信息，则不获取数据
    if (props.type === 6 && !goodsListParams.warehouse_id) {
      console.log('销售退货类型缺少仓库信息，不获取数据');
      loading.value = false;
      return;
    }
    
    getGoodsPages(pageData).then((res: any) => {
      console.log('商品列表响应:', res);
      if (res.code == 0) {
        // 处理API响应数据
        const processedData = processApiResponseData(res.data);
        // 映射商品项
        goodsList.value = mapGoodsItems(processedData);
        hasMore.value = res.data.next !== null;
      }
      loading.value = false;
    }).catch((err: any) => {
      console.error('获取商品列表失败:', err);
      loading.value = false;
    });
    return;
  }
  
  // 其他类型保持原有逻辑
  getGoodsPages(pageData).then((res: any) => {
    console.log('商品列表响应:', res);
    if (res.code == 0) {
      goodsList.value = mapGoodsItems(res.data.results);
      hasMore.value = res.data.next !== null;
    }
    loading.value = false;
  }).catch((err: any) => {
    console.error('获取商品列表失败:', err);
    loading.value = false;
  });
};

// 加载更多
const loadMore = () => {
  console.log('加载更多', loading.value, hasMore.value);
  if (loading.value || !hasMore.value) return;
  
  pageData.page += 1;
  console.log('新页码:', pageData.page);
  
  loading.value = true;
  
  getGoodsPages(pageData).then((res: any) => {
    console.log('加载更多响应:', res);
    if (res.code == 0) {
      const newItems = mapGoodsItems(res.data.results);
      goodsList.value = [...goodsList.value, ...newItems];
      hasMore.value = res.data.next !== null;
    }
    loading.value = false;
  }).catch((err: any) => {
    console.error('加载更多失败:', err);
    loading.value = false;
  });
};

// 导航到编辑页面
const navigateToEdit = (item: GoodsItem) => {
  console.log(item);
  let id = '';
  if (props.type === 1 || props.type == 2 || props.type == 6) {
    item.item = item.id;
    id = item.id || '';
  }
  if (props.type == 4 || props.type == 5) {
    id = item.item || '';
    returnWarehouse.value.warehouse = item.warehouse || '';
    returnWarehouse.value.warehouse_name = item.warehouse_name || '';
  }
  if (props.type == 7 || props.type == 8) {
    id = item.item || '';
  }
  
  getGoodsDetail(id).then((res: any) => {
    if (res.code == 0) {
      productData.value = { ...item, ...res.data };
      productData.value!.id = item.id;
      productData.value!.unit = item.unit;
      console.log(productData.value);
      productDetailsShow.value = true;
    }
  });
};

// 选择商品
const selectProduct = (item: GoodsItem) => {
  console.log('选择商品', item);
  
  // 检查商品是否停用
  if (item.is_active === false) {
    uni.showToast({
      title: '该商品已停用',
      icon: 'none'
    });
    return;
  }
  
  // 检查是否有仓库信息
  if (item.isWarehouse === false) {
    uni.showToast({
      title: '该商品无仓库信息',
      icon: 'none'
    });
    return;
  }
  
  // 退货单类型(4,5)特殊处理
  if (props.type == 4 || props.type == 5) {
    console.log('退货单选择商品');
    // 检查是否有仓库信息
    if (!item.warehouse) {
      uni.showToast({
        title: '该商品无仓库信息，无法选择',
        icon: 'none'
      });
      return;
    }
    
    // 设置退货仓库信息
    returnWarehouse.value.warehouse = item.warehouse;
    returnWarehouse.value.warehouse_name = item.warehouse_name || '';
    
    // 过滤商品列表
    goodsList.value = goodsList.value.filter(
      goodsItem => goodsItem.warehouse === item.warehouse
    );
    
    // 更新选择列表
    selectGoodsList.value = selectGoodsList.value.filter(
      selectItem => selectItem.warehouse === item.warehouse
    );
  }
  
  // 销货单类型特殊处理
  if (props.type === 10) {
    console.log('销货单选择商品', item);
    // 确保仓库信息正确
    if (props.warehouseData && props.warehouseData.warehouse) {
      item.warehouse = item.warehouse || props.warehouseData.warehouse;
      item.warehouse_name = item.warehouse_name || props.warehouseData.warehouse_name;
    }
    // 记录原始数量和价格
    const savedQuantity = item.quantity;
    const savedPrice = item.price;
    console.log('保留销售订单中的原始数量:', savedQuantity, '和价格:', savedPrice);
  }
  
  // 获取商品详情
  let id = item.id || '';
  if (props.type === 1 || props.type == 2 || props.type == 6) {
    item.item = item.id;
    id = item.id || '';
  }
  if (props.type == 4 || props.type == 5) {
    id = item.item || '';
    returnWarehouse.value.warehouse = item.warehouse || '';
    returnWarehouse.value.warehouse_name = item.warehouse_name || '';
  }
  if (props.type == 7 || props.type == 8) {
    id = item.item || '';
  }
  
  // 获取商品详情，确保使用正确的ID
  const itemId = item.id || item.item || item.code || '';
  console.log(`获取商品详情，使用ID: ${itemId}`);
  
  getGoodsDetail(itemId).then((res) => {
    if (res.code == 0) {
      // 合并API返回的详细数据与当前商品数据
      const detailedItem: GoodsItem = {
        ...item,
        ...res.data,
        // 确保保留原始的ID和item值
        id: item.id || item.item || item.code || '',
        item: item.item || item.id || item.code || ''
      };
      
      // 检查商品是否已在选择列表中
      const existingIndex = selectGoodsList.value.findIndex(
        selectItem =>
          ((selectItem.id && selectItem.id === detailedItem.id) ||
           (selectItem.item && selectItem.item === detailedItem.item) ||
           (selectItem.code && selectItem.code === detailedItem.code)) &&
          selectItem.unit === detailedItem.unit
      );
      
      if (existingIndex !== -1) {
        // 如果已在选中列表中，更新详情
        console.log('商品已在选择列表中，更新详情');
        selectGoodsList.value[existingIndex] = {
          ...selectGoodsList.value[existingIndex],
          ...detailedItem
        };
      } else {
        // 如果不在选中列表中，添加到列表
        console.log('添加商品到选择列表');
        detailedItem.isSelected = true;
        detailedItem.quantity = detailedItem.quantity || 1;
        
        // 销货单特殊处理
        if (props.type === 10) {
          detailedItem.price = detailedItem.price || detailedItem.retail_price || detailedItem.wholesale_price || 0;
          detailedItem.total = (detailedItem.quantity || 0) * (detailedItem.price || 0);
        }
        
        selectGoodsList.value.push(detailedItem);
      }
      
      // 打开商品详情弹窗
      productData.value = detailedItem;
      console.log('设置商品详情数据:', productData.value);
      productDetailsShow.value = true;
      
      // 发送更新后的商品列表
      emit('selectedGoodsList', selectGoodsList.value);
      
      // 更新商品列表中的选择状态
      const updateIndex = goodsList.value.findIndex(
        goodsItem =>
          ((goodsItem.id && goodsItem.id === detailedItem.id) ||
           (goodsItem.item && goodsItem.item === detailedItem.item) ||
           (goodsItem.code && goodsItem.code === detailedItem.code)) &&
          goodsItem.unit === detailedItem.unit
      );
      
      if (updateIndex !== -1) {
        goodsList.value[updateIndex].isSelected = true;
      }
    } else {
      console.error('获取商品详情失败:', res.msg);
      uni.showToast({
        title: res.msg || '获取商品详情失败',
        icon: 'none'
      });
    }
  }).catch(err => {
    console.error('调用商品详情API错误:', err);
    uni.showToast({
      title: '系统错误',
      icon: 'none'
    });
  });
};

const fakeLoad = (data: any) => {
  console.log("fakeLoad", data);
  goodsList.value = data.results;
  goodsList.value = mapGoodsItems(goodsList.value);
  hasMore.value = data.count > data.results.length;
  loading.value = false;
};

const pageOne = (data: any) => {
  console.log("pageOne", data);
  const result = mapGoodsItems(data.results);
  
  result.forEach((newItem) => {
    const existingIndex = goodsList.value.findIndex(
      (existingItem) => existingItem.id === newItem.id && existingItem.unit === newItem.unit
    );
    
    if (existingIndex !== -1) {
      goodsList.value[existingIndex] = newItem;
    } else {
      goodsList.value.push(newItem);
    }
  });
  
  hasMore.value = data.count > goodsList.value.length;
  loading.value = false;
  
  console.log(goodsList.value);
};

const expandPage = (data: any, page: number) => {
  console.log("expandPage", data, page);
  
  if (data.isLastPage || (data.results && data.results.length === 0)) {
    console.log("已到达最后一页，没有更多数据");
    hasMore.value = false;
    loading.value = false;
    return;
  }
  
  const result = mapGoodsItems(data.results);
  
  result.forEach((newItem) => {
    const existingIndex = goodsList.value.findIndex(
      (existingItem) => existingItem.id === newItem.id && existingItem.unit === newItem.unit
    );
    
    if (existingIndex !== -1) {
      goodsList.value[existingIndex] = newItem;
    } else {
      goodsList.value.push(newItem);
    }
  });
  
  hasMore.value = data.count > goodsList.value.length;
  loading.value = false;
};

const closeSelectPopup = (item: GoodsItem) => {
  productDetailsShow.value = false;
};

const confirmSelectProduct = (item: GoodsItem) => {
  console.log(item);
  item.id = "";
  const index = selectGoodsList.value.findIndex(
    (g) => g.item === item.item && g.unit === item.unit
  );
  
  if (index > -1) {
    selectGoodsList.value.splice(index, 1, {
      ...selectGoodsList.value[index],
      ...item,
      selected: true,
    });
  } else {
    selectGoodsList.value.push({ ...item, selected: true });
  }
  console.log(selectGoodsList.value);
  const indexGoods = goodsList.value.findIndex((g) => {
    if (props.type === 0 || props.type === 3) {
      return g.item === item.item && g.unit === item.unit;
    }
    return g.item === item.item;
  });
  console.log(indexGoods);
  
  if (indexGoods > -1) {
    goodsList.value[indexGoods] = {
      ...goodsList.value[indexGoods],
      selected: true,
    };
  }
  productDetailsShow.value = false;
  
  if (props.type === 6) {
    const totalMap: { [key: string]: number } = {};
    
    selectGoodsList.value.forEach((item) => {
      const itemId = item.item;
      const quantity = Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      if (totalMap[itemId as string]) {
        totalMap[itemId as string] += quantity;
      } else {
        totalMap[itemId as string] = quantity;
      }
    });
    
    selectGoodsList.value.forEach((item) => {
      item.remaining_stock = totalMap[item.item as string] || 0;
    });
    goodsList.value.forEach((item) => {
      item.remaining_stock = totalMap[item.item as string] || 0;
    });
    console.log(selectGoodsList.value);
  }
  
  emit("selectedGoodsList", selectGoodsList.value);
  console.log(selectGoodsList.value);
  
  emit("selectType", props.type);
};

// 确保item字段存在
const ensureItemField = (goods: any): any => {
  if (!goods) return goods;
  
  // 如果是数组，处理数组中的每一项
  if (Array.isArray(goods)) {
    return goods.map(item => ensureItemField(item));
  }
  
  // 处理单个商品对象
  const processedItem = { ...goods };
  
  // 确保item字段存在，优先使用id字段
  if (!processedItem.item) {
    processedItem.item = processedItem.id || processedItem.code;
    console.log(`商品 ${processedItem.item_name || processedItem.name} 缺少item字段，已设置为: ${processedItem.item}`);
  }
  
  return processedItem;
};

// 在发送选中商品前确保数据完整性
const emitSelectedGoods = (goodsList: GoodsItem[]) => {
  // 确保所有商品都有item字段
  const processedList = ensureItemField(goodsList) as GoodsItem[];
  
  // 检查处理后的数据
  const missingItemCount = processedList.filter(item => !item.item).length;
  if (missingItemCount > 0) {
    console.warn(`警告: 发送前仍有${missingItemCount}个商品缺少item字段`);
  }
  
  // 发送处理后的数据
  emit("selectedGoodsList", processedList);
};
</script>

<style lang="scss" scoped>
.goods_item_disabled {
  opacity: 0.7;
  background: #f5f5f5;
}

.text-disabled {
  color: #999 !important;
}

.disabled-tag {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  display: flex;
  align-items: center;
  padding: 4rpx 10rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  
  text {
    font-size: 24rpx;
    color: #999;
    margin-left: 4rpx;
  }
}

.goods_theme {
  position: relative;
}

.corner-bg {
  position: absolute;
  top: -10px;
  left: -14px;
}

.goodsList {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  
  .scroll-Y {
    width: 100%;
  }
  
  .goods_item {
    width: 90%;
    background-color: #fff;
    margin: 10rpx auto;
    padding: 20rpx;
    cursor: pointer;
    position: relative;
    
    .goods_theme {
      width: 100%;
      // height: 40%;
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: row;
      
      .select-goods {
        position: absolute;
        right: 0;
        
        top: 0;
      }
      
      .goods_img {
        width: 100rpx;
        height: 100rpx;
        margin: 10rpx;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
      
      .goods_title {
        width: 70%;
        height: 100%;
        display: flex;
        // align-items: center;
        // justify-content: center;
        flex-direction: column;
        
        .goods_name {
          font-size: 30rpx;
          font-weight: bold;
          display: flex;
          align-items: center;
          
          ::v-deep .u-tag--primary {
            background-color: #62c8ee;
            border-width: 1px;
            border-color: #62c8ee;
          }
          
          ::v-deep .u-tag--mini {
            height: 18px;
            /* line-height: 22px; */
            padding: 0 5px;
          }
          
          .goods_id {
            font-size: 24rpx;
            font-weight: bold;
          }
        }
      }
    }
    
    .goods_info {
      display: flex;
      
      .goods_attributes {
        width: 50%;
        font-size: 24rpx;
        display: flex;
        align-content: flex-start;
        align-items: flex-start;
        flex-direction: column;
        
        .goods_attribute {
          display: flex;
          align-items: center;
          margin-top: 10rpx;
        }
      }
    }
  }
  
  .loading-more,
  .no-more,
  .empty {
    text-align: center;
    padding: 20rpx;
    color: #999;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .status-text {
      margin-left: 10rpx;
    }
  }
}

.goods_attribute_name {
  width: 60px;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.popup-title-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  position: relative;
}

.popup-title {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .popup-title-text {
    font-size: 32rpx;
    font-weight: bold;
  }
}

.sales-item {
  width: 95%;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  margin: 0 auto;
  border-bottom: 1px solid #eee;
  
  text {
    margin-bottom: 8rpx;
    font-size: 28rpx;
  }
  
  text:first-child {
    font-weight: bold;
    color: #333;
  }
  
  text:nth-child(2) {
    color: #666;
  }
  
  text:last-child {
    color: #999;
    font-size: 24rpx;
  }
}

.cumulative {
  height: 50rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: red;
  font-size: 28rpx;
}
</style>