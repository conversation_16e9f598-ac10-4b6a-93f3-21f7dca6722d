<template>
    <view class="loading-container" v-if="loading">
        <view class="loading-content">
            <u-loading-icon :show="true" text="正在努力加载中..." textSize="18" :vertical="true"></u-loading-icon>
        </view>
    </view>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';

interface Props {
  loading: boolean;
}

const props = defineProps<Props>();
</script>
<style scoped>
.loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 9999;
}

.loading-content {
    text-align: center;
}
</style>
