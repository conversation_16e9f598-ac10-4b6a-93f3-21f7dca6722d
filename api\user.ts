import http from '@/utils/http';

// 定义请求数据接口
interface RequestData {
  [key: string]: any;
}

// 注册自定义错误处理器
let request = (http as any).register({
  1: (body: any) => {
    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none',
    });
    // console.log("api/user request handler", body);
    //return Promise.reject(new Error(body?.msg || '请求失败'));
    //return Promise.resolve(body);
    // 这行没用的
    // return {code:"asdfasfd"}
  },
});

/**
 * 登录接口（校验手机号）
 * @param {RequestData} data - 请求数据，包含手机号等信息
 * @returns {Promise<any>}
 */
export const validatePhoneNumber = (data: RequestData): Promise<any> => {
  return request.post('/api/platform/login/', data);
};

/**
 * 绑定手机号（如果用户第一次登录，则需要）
 * @param {RequestData} data - 请求数据，包含手机号等信息
 * @returns {Promise<any>}
 */
export const bindPhoneNumber = (data: RequestData): Promise<any> => {
  return request.post('/api/platform/login_bdph/', data);
};

/**
 * 登录接口（获取企业信息和账套信息）
 * @returns {Promise<any>}
 */
export const loginGetInfo = (): Promise<any> => {
  return request.post('/api/login/');
};
