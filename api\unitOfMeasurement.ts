import request from '@/utils/request.ts';
import store from '@/store';

// 定义单位数据接口
interface UnitOfMeasurementData {
  id?: string; // 单位ID，可选，因为新增时可能没有
  name: string; // 单位名称
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

console.log(getLedgerName());

/**
 * 获取单位信息
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getUnitOfMeasurement = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/unittypes/`, data);
};

/**
 * 获取单位信息（带名称）
 * @param {string} name - 单位名称
 * @returns {Promise<any>}
 */
export const getUnitOfMeasurementName = (name: string): Promise<any> => {
  const encodedName = encodeURIComponent(name);
  return request.get(`/ztx/${getLedgerName()}/unittypes/search/?query=${encodedName}`);
};

/**
 * 新增单位
 * @param {UnitOfMeasurementData} data - 单位数据
 * @returns {Promise<any>}
 */
export const addUnitOfMeasurement = (data: UnitOfMeasurementData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/unittypes/`, data);
};

/**
 * 登陆接口（获取企业信息和账套信息）
 * @returns {Promise<any>}
 */
export const loginGetInfo = (): Promise<any> => {
  return request.post('/api/login/');
};
