import request from '@/utils/request.ts';
import store from '@/store';

// 定义采购商品项接口
interface PurchaseItem {
  item: string; // 商品ID
  unit: string; // 单位ID
  quantity: number; // 数量
  purchase_price: number; // 采购价格
  purchase_order_item?: string; // 采购订单项ID，可选
}

// 定义分摊成本详情接口
interface AllocationDetail {
  item: string; // 商品ID
  amount: number; // 分摊金额
}

// 定义分摊成本接口
interface AllocatedCost {
  allocation_method: string; // 分摊方法
  allocation_details: AllocationDetail[]; // 分摊详情
  supplier?: string; // 供应商ID，可选
}

// 定义采购入库订单数据接口
interface PurchaseGoodsData {
  id?: string; // 订单ID，可选，因为新增时可能没有
  is_commit?: number; // 是否提交
  in_type: string; // 入库类型
  purchase_order?: string; // 采购订单ID，可选
  supplier?: string; // 供应商ID，可选
  items?: PurchaseItem[]; // 商品列表
  warehouse: string; // 仓库ID
  total_cost?: number; // 总成本
  allocated_pay_amount?: number; // 已分摊支付金额
  allocated_payment_method?: string; // 已分摊支付方式
  pay_amount?: number; // 支付金额
  payment_method?: string; // 支付方式
  allocated_cost?: AllocatedCost; // 分摊成本信息，可选
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取入库订单列表
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getPurchaseGoods = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/purchaseins/`, data);
};

/**
 * 获取入库订单物品的库存记录
 * @param {string} id - 入库订单ID
 * @returns {Promise<any>}
 */
export const getPurchaseGoodsStock = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/purchaseins/${id}/StockList/`);
};

/**
 * 根据入库订单id获取入库订单详情
 * @param {string} id - 入库订单ID
 * @returns {Promise<any>}
 */
export const getPurchaseGoodsDetail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/purchaseins/${id}/`);
};

/**
 * 新建入库订单
 * @param {PurchaseGoodsData} data - 入库订单数据
 * @returns {Promise<any>}
 */
export const savePurchaseGoods = (data: PurchaseGoodsData): Promise<any> => {
  console.log(data);
  const filteredData: PurchaseGoodsData = {
    is_commit: data.is_commit,
    in_type: data.in_type,
    purchase_order: data.purchase_order,
    supplier: data.supplier,
    items: data.items?.map(item => ({
      item: item.item,
      unit: item.unit,
      quantity: item.quantity,
      purchase_price: item.purchase_price,
      purchase_order_item: item.purchase_order_item
    })),
    warehouse: data.warehouse,
    total_cost: data.total_cost,
    allocated_pay_amount: data.allocated_pay_amount,
    allocated_payment_method: data.allocated_payment_method,
    pay_amount: data.pay_amount,
    payment_method: data.payment_method
  };
  if (data.allocated_cost && Object.keys(data.allocated_cost).length > 0) {
    filteredData.allocated_cost = {
      allocation_method: data.allocated_cost.allocation_method,
      allocation_details: data.allocated_cost.allocation_details,
      supplier: data.allocated_cost.supplier
    };
  }
  return request.post(`/ztx/${getLedgerName()}/purchaseins/`, filteredData);
};

/**
 * 搜索入库订单
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchPurchaseGoods = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/purchaseins/search/`, data);
};

/**
 * 编辑入库订单
 * @param {PurchaseGoodsData} data - 入库订单数据
 * @returns {Promise<any>}
 */
export const updatePurchaseGoods = (data: PurchaseGoodsData): Promise<any> => {
  console.log(data);

  // 过滤需要传递给后端的字段
  const filteredData: PurchaseGoodsData = {
    is_commit: data.is_commit,
    in_type: data.in_type,
    purchase_order: data.purchase_order,
    supplier: data.supplier,
    items: data.items?.map(item => ({
      item: item.item,
      unit: item.unit,
      quantity: item.quantity,
      purchase_price: item.purchase_price,
      purchase_order_item: item.purchase_order_item
    })),
    warehouse: data.warehouse,
    total_cost: data.total_cost,
    allocated_pay_amount: data.allocated_pay_amount,
    allocated_payment_method: data.allocated_payment_method,
    pay_amount: data.pay_amount,
    payment_method: data.payment_method
  };

  if (data.allocated_cost && Object.keys(data.allocated_cost).length > 0) {
    filteredData.allocated_cost = {
      allocation_method: data.allocated_cost.allocation_method,
      allocation_details: data.allocated_cost.allocation_details,
      supplier: data.allocated_cost.supplier
    };
  }
  console.log('Filtered data:', filteredData);
  return request.put(`/ztx/${getLedgerName()}/purchaseins/${data.id}/`, filteredData);
};

/**
 * 删除入库订单
 * @param {string} id - 入库订单ID
 * @returns {Promise<any>}
 */
export const deletePurchaseGoods = (id: string): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/purchaseins/${id}/cancel/`);
};

/**
 * 库存记录信息 (此函数在原js文件中是delete请求，但注释写的是库存记录信息，且参数是data，与deleteGoods的id参数不符，此处根据原js的实现保留delete方法，但注释改为与实际操作相符)
 * @param {object} data - 包含入库订单ID的数据
 * @returns {Promise<any>}
 */
export const inventoryRecords = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/purchaseins/${data.id}/StockList/`);
};
