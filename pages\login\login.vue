<template>
	<view class="container">
		<img src="/static/img/loginBackgroundImage.png" mode="aspectFill" class="background-image" />
		<up-overlay :show="show">
			<view class="custom-loading">
				<u-loading-icon mode="circle" color="#fff" size="40"></u-loading-icon>
				<text class="loading-text">登录中...</text>
			</view>
		</up-overlay>
		<view class="login">
			<view class="login_header">
				<view class="logo">
					<image src="/static/img/logo.png" mode=""></image>
				</view>
				<view class="login_header_title">
					<text>仓小助</text>
				</view>
			</view>
			<view class="login_buttons" :class="{ 'show-buttons': showLoginButtons }">
				<!-- 新用户且协议未勾选时，显示提醒按钮 -->
				<button class="login_btn" @click="remindAgreement" v-if="!isAgreement">微信登录</button>
				<!-- 新用户且协议已勾选时，显示正常登录按钮 -->
				<button class="login_btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
					v-if="isNewUser && isAgreement">微信登录</button>
				<!-- 老用户且协议未勾选时，显示提醒按钮 -->
				<!-- <button class="login_btn" @click="remindAgreement" v-if="!isNewUser && !isAgreement">微信登录</button> -->
				<!-- 老用户且协议已勾选时，显示正常登录按钮 -->
				<button class="login_btn" @click="jumpHome" v-if="!isNewUser && isAgreement">微信登录</button>

				<checkbox-group @change="toggleAgreement" :class="{ 'shake': isShaking }">
					<label class="agreement">
						<checkbox :checked="isAgreement" style="transform:scale(0.7)" />
						我已阅读并同意
						<navigator class="agreement_content" url="/pages/order/order" hover-class="navigator-hover">
							《用户协议》</navigator>
					</label>
				</checkbox-group>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import {
	wxLogin
} from '@/utils/wechatVerification';
import { useStore } from 'vuex';
import { onMounted, ref, reactive, nextTick } from 'vue';

const store = useStore();

const loginData = reactive({
	code: '',
	platform_type: "wechat_mini",
});
const show = ref(false);
const isAgreement = ref(false);
const isNewUser = ref(true);
const showLoginButtons = ref(false);
const isShaking = ref(false);

onMounted(() => {
	wxLogin().then(wxcode => {
		loginData.code = wxcode.code;
		validatePhoneNumber();
	}).catch(error => {
		console.error(error);
	});
});

const remindAgreement = () => {
	uni.showToast({
		title: '请阅读并勾选协议',
		icon: 'none'
	});
	isShaking.value = true;
	setTimeout(() => {
		isShaking.value = false;
	}, 600);
};

const getPhoneNumber = (e: any) => {
	if (e.detail.errno == undefined) {
		loginData.code = e.detail.code;
		bindPhoneNumber();
	} else {
		uni.showToast({
			title: '手机号获取失败',
			icon: 'none'
		});
		console.error(e.detail.errno);
		console.error(e.detail.errMsg);
	}
};

const validatePhoneNumber = () => {
	store.dispatch('user/validatePhoneNumber', loginData)
		.then(res => {
			if (res.isnew === 0) {
				isNewUser.value = false;
				console.log('老用户，isNewUser:', isNewUser.value);
				return store.dispatch('user/loginGetInfo');
			} else {
				isNewUser.value = true;
				console.log('新用户，isNewUser:', isNewUser.value);
			}
		})
		.then(res => {
			if (res) {
				console.log('获取企业信息成功:', res);
			}
			showLoginButtonsWithAnimation();
		})
		.catch(err => {
			console.error('操作失败:', err);
			showLoginButtonsWithAnimation();
		});
};

const showLoginButtonsWithAnimation = () => {
	nextTick(() => {
		setTimeout(() => {
			showLoginButtons.value = true;
		}, 100);
	});
};

const bindPhoneNumber = () => {
	show.value = true;
	store.dispatch('user/bindPhoneNumber', loginData)
		.then((res) => {
			if (res) {
				store.dispatch('user/loginGetInfo').then(res => {
					console.log('获取企业信息以及账套信息成功:', res);
					jumpHome();
				})
			} else {
				uni.showToast({
					title: '登录失败',
					icon: 'none'
				});
				show.value = false;
			}
		})
		.catch(err => {
			console.error('登录失败:', err);
			uni.showToast({
				title: err.Error,
				icon: 'none'
			});
			show.value = false;
		});
};

const toggleAgreement = () => {
	isAgreement.value = !isAgreement.value;
};

const jumpHome = () => {
	console.log("直接登录");
	show.value = true;
	setTimeout(() => {
		uni.navigateTo({
			url: '/pages/index/index',
			success: () => {
				console.log('页面跳转成功');
				setTimeout(() => {
					uni.hideLoading();
				}, 300);
			},
			fail: (err) => {
				console.error('页面跳转失败:', err);
				uni.showToast({
					title: '页面跳转失败',
					icon: 'none'
				});
				show.value = false;
			}
		});
	}, 800);
};

</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	background-size: cover;
	background-position: center;
}

.background-image::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.7);
	/* 70%黑色填充 */
	opacity: 0.88;
	/* 88%不透明度 */
	border: 1px solid rgba(187, 187, 187, 1);
	/* 100%灰色描边 */
}

.background-image {
	position: fixed;
	width: 100%;
	height: 100%;
	z-index: -1;
}

.login {
	width: 100%;
	height: 100%;
	// background-color: #fff;
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: space-around;
	// justify-content: center;
	// background: rgba(255, 255, 255, 0.2);
	// backdrop-filter: blur(10px);
	// border-radius: 10px;
	// padding: 20px;
	// z-index: 111;

	.logo {
		width: 150rpx;
		height: 150rpx;
		border-radius: 50%;

		image {
			width: 100%;
			height: 100%;
			border-radius: 50%;
		}
	}

	.login_buttons {
		opacity: 0;
		transform: translateY(20rpx);
		transition: all 1s ease-in-out;
		
		&.show-buttons {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	// 抖动动画效果
	.shake {
		animation: shake 0.6s ease-in-out;
	}
	
	@keyframes shake {
		0%, 100% {
			transform: translateX(0);
		}
		10%, 30%, 50%, 70%, 90% {
			transform: translateX(-5rpx);
		}
		20%, 40%, 60%, 80% {
			transform: translateX(5rpx);
		}
	}

	.agreement {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 25rpx;
		margin-top: 20rpx;
		color: #fff;

		.agreement_content {
			color: #2b85e4;
		}
	}
}

.login_btn {
	width: 70%;
	height: 80rpx;
	border: 2px #2b85e4 solid;
	color: #2b85e4;
	font-size: 30rpx;
	font-weight: 700;
	box-shadow: 0 4px 8px rgba(43, 133, 228, 0.3);
}

.login_header{
	display: flex;
	flex-direction: column;
	align-items: center;
}



.login_header_title{
	width: 350rpx;
	font-size: 80rpx;
	font-family: 'CustomFont', sans-serif;
	font-weight: 700;
	color: #fff;
	display: flex;
	justify-content: space-between;
	margin-top: 20rpx;
	
	text{
		width: 100%;
		text-align: justify;
		text-align-last: justify;
		letter-spacing: 20rpx;
	}
}

.custom-loading {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 999;
}
.loading-text {
	color: #fff;
	margin-top: 20rpx;
	font-size: 32rpx;
}
</style>