<template>
  <view class="container">
    <scroll-view scroll-y class="content">
      <!-- 基本信息 -->
      <!-- <view class="info">
        <view class="info_title">
          <text>基本信息</text>
        </view>
        <view class="form">
          <u--form :model="retailOrderInfo" :rules="rules" ref="uForm" labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="客户" prop="customer_name" borderBottom required>
              <u--input v-model="retailOrderInfo.customer_name" border="none" placeholder="请选择客户" inputAlign="right"
                disabled disabledColor="#fff" @tap="openSelector(1)">
              </u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据日期" borderBottom>
              <u--input v-model="retailOrderInfo.out_date" border="none" placeholder="请选择日期" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
              <u--input v-model="retailOrderInfo.order_no" border="none" placeholder="提交后自动生成" inputAlign="right"
                disabled disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="仓库" prop="warehouse_name" borderBottom required>
              <u-row>
                <u-col :span="retailOrderInfo.warehouse_name ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.warehouse_name" border="none" placeholder="请选择仓库"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openInventorySelection()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeWarehouseName">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.warehouse_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
          </u--form>
        </view>
      </view> -->
<basicInfo :basicInfo="retailOrderInfo" :isDisabled="isView" type="retail" ref="basicInfo" @update:basicInfo="val => retailOrderInfo = val"/>
      <!-- 商品清单 -->
      <merch-bill :items="retailOrderInfo.items" type="retail" :prohibitModification="isView" @open-product-select="addGoods"
        @open-product-details="openProductDetails" @amount-change="handleAmountChange" />

      <!-- 结算信息 -->
      <view class="info">
        <view class="info_title">
          <text>结算信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left" :model="retailOrderInfo" :rules="rules" ref="uForm">
            <u-form-item :label-width="'180rpx'" prop="payment_method_name" label="结算账户" borderBottom required>
              <u-row>
                <u-col :span="retailOrderInfo.payment_method_name ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.payment_method_name" border="none" placeholder="请选择结算账户"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePurchaseOrder">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.payment_method_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="合计金额" borderBottom>
              <u--input v-model="retailOrderInfo.total_amount" border="none" placeholder="0" inputAlign="right" disabled
                disabledColor="#fff" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="优惠率" borderBottom>
              <u--input v-model="retailOrderInfo.discountRate" border="none" placeholder="0" inputAlign="right"
                :disabled="isView" disabledColor="color: #fff" @input="changeDiscount" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="收款优惠" borderBottom>
              <u--input v-model="retailOrderInfo.discount" border="none" placeholder="0" inputAlign="right"
                :disabled="isView" disabledColor="color: #fff" @input="changeDiscountRate" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="收款金额" borderBottom>
              <u--input v-model="retailOrderInfo.total_sale_price" border="none" placeholder="0" inputAlign="right"
                disabled disabledColor="color: #fff" @input="changePayAmount" />
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="retailOrderInfo.remark" placeholder="请输入备注" autoHeight border="none"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <view>
        <imageUpload />
      </view>

      <!-- 底部操作栏 -->
      <view class="operation" v-if="!isView">
        <u-button type="primary" text="保存" @click="save"></u-button>
      </view>
    </scroll-view>
    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>
    <!-- 交互组件 -->
    <interactive :isShowAddBtn="false" :jumpToId="4" />

    <!-- 保存提示框 -->
    <view>
      <u-modal :show="promptShow" :title="promptTitle" :content="promptContent" :showCancelButton="true"
        :closeOnClickOverlay="true" @confirm="addRetailOrders" @cancel="close" @close="close"></u-modal>
    </view>

    <!-- 客户选择器 -->
    <searchSelector v-model:selectorShow="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectCustomer" @close="closePopup" @customerScrollToLower="getCustomersList" />

    <!-- 库存弹出层 -->
    <inventorySelection :inventorySelectionShow="inventorySelectionShow" :isSelect="true"
      @close="closeInventorySelection" @save="confirmInventory" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />

    <!-- 商品详细信息弹出层 -->
    <productDetails :productDetailsShow="productDetailsShow" :type="6" :productData="productData"
      :isOpenFromOrder="true" :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing"
      @close="closeProductDetails" @confirm="handleProductDetails" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onBeforeUnmount } from 'vue';
import { onLoad, onReady } from '@dcloudio/uni-app';
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import inventorySelection from "@/components/inventorySelection/inventorySelection.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import MerchBill from "@/components/merchbill.vue";
import { getCustomerList } from "@/api/customer";
import { addRetailOrders } from "@/api/retailOrders";
import searchSelector from "@/components/searchSelector.vue";
import basicInfo from "@/components/modifyComponent/basicInfo/basicInfo.vue";
import productDetails from "@/components/productDetails.vue";
import { getGoodsDetail } from "@/api/goods";
import eventBus from "@/utils/eventBus";

const uForm = ref(null);

const isView = ref(false); //是否为修改进入
const promptShow = ref(false); //提示显示状态
const promptTitle = ref("温馨提示"); //提示标题
const promptContent = ref("保存后将无法修改，是否确认保存？"); //保存提示内容
const productDetailsShow = ref(false); //商品详细信息弹出层
const productData = ref({}); //物品信息
const accountSelectShow = ref(false);
const isExpandGoodsList = ref(false);
const activeQtyIndex = ref(-1);
const inventorySelectionShow = ref(false); // 仓库选择弹出层显示状态
const selectType = ref(0); //搜索类型 0 客户
const selectorShow = ref(false); //客户选择框展示状态
const selectList = ref<any[]>([]); //客户选择框数据
const customerList = ref<any[]>([]); //客户列表
const isCustomerMore = ref(true); //客户列表是否有更多数据
const customerPageParams = reactive({
  //获取客户列表的参数
  page: 1,
  page_size: 20,
});
const retailOrderInfo = reactive<any>({
  document_type: "retail",
  customer_name: "",
  customer: "",
  out_date: "",
  order_no: "",
  warehouse: "",
  warehouse_name: "",
  items: [],
  total_amount: 0,
  discountRate: 0, //优惠率
  discount: 0, //优惠金额
  total_sale_price: 0,
  payment_method: "",
  payment_method_name: "",
  remark: "",
  is_draft: false,
});
const rules = reactive<any>({
  customer_name: {
    type: "string",
    required: true,
    message: "请选择客户",
    trigger: ["blur", "change"],
  },
  warehouse_name: {
    type: "string",
    required: true,
    message: "请选择仓库",
    trigger: ["blur", "change"],
  },
  payment_method_name: {
    type: "string",
    required: true,
    message: "请选择结算账户",
    trigger: ["blur", "change"],
  },
});

// 关闭商品详情弹出层
const closeProductDetails = () => {
  productDetailsShow.value = false;
};
// 打开商品详情弹出层
const openProductDetails = (item: any) => {
  if (isView.value) {
    uni.showToast({
      title: '当前为已提交状态，不允许修改',
      icon: "none",
    })
    return;
  }

  // 直接循环retailOrderInfo.items，计算相同id的商品总用量
  let totalUsedStock = 0;
  retailOrderInfo.items.forEach((items: any) => {
    if (items.item === item.item) {
      totalUsedStock += Number(items.quantity || 0) * Number(items.conversion_rate || 1);
    }
  });
  getGoodsDetail(item.item).then((res) => {
    if (res.code == 0) {
      productData.value = { ...item, ...res.data };
      productData.value.remaining_stock = item.remaining_stock;
      productData.value.unit = item.unit;
      console.log(productData.value);
      productDetailsShow.value = true;
    }
  });
};

//处理商品详细提交回来的数据
const handleProductDetails = (data: any) => {
  console.log(data);
  console.log(retailOrderInfo.items);
  data.price = Number(data.purchase_price) || 0;
  data.quantity = Number(data.quantity) || 1;
  data.total = Number(data.total) || 0;
  const targetIndex = retailOrderInfo.items.findIndex(
    (infoItem: any) => infoItem.item === data.item && infoItem.unit === data.unit
  );

  if (targetIndex !== -1) {
    console.log('匹配到相同商品和单位类型，更新数据');
    console.log(retailOrderInfo.items[targetIndex]);

    retailOrderInfo.items[targetIndex] = data;

    // 直接循环retailOrderInfo.items，计算相同id的商品总用量
    let totalUsedStock = 0;
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === data.code) {
        totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      }
    });

    // 将计算出的remainingStock赋值给所有相同id的元素
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === data.code) {
        item.remaining_stock = totalUsedStock;
      }
    });

    console.log('更新后的数据:', retailOrderInfo.items[targetIndex]);
  } else {
    console.log('未找到匹配的商品');
  }

  // calcTotalAndDiscount();
};
//商品清单删除商品
const handleDelBtnPricing = (data: any) => {
  console.log(data);
  console.log(retailOrderInfo.items);

  // 找到与data相同item的元素
  let total = 0;
  retailOrderInfo.items.forEach((item: any) => {
    if (item.item === data.item) {
      total = Number(item.remaining_stock || 0);
    }
  });

  // 减去data的quantity乘以转换率
  total -= Number(data.quantity || 0) * Number(data.conversion_rate || 1);

  // 将其他相同item的元素赋值为total
  retailOrderInfo.items.forEach((item: any) => {
    if (item.item === data.item) {
      item.remaining_stock = total;
    }
  });

  // 过滤掉被删除的项目
  retailOrderInfo.items = retailOrderInfo.items.filter(
    (item: any) => !(item.item === data.item && item.unit === data.unit)
  );

  console.log(retailOrderInfo.items);

  productDetailsShow.value = false;
  // calcTotalAndDiscount();
};

//选择商品
const handleSelectGoodsList = (data: any) => {
  console.log("选择商品数据：", data);

  // 将选中的商品数据转换为销售订单所需格式
  const formattedItems = data.map((item: any) => ({
    ...item,
    // 确保商品数据字段与salesOrder.items结构匹配
    // 重新映射并转换为数字类型
    price: Number(item.purchase_price) || 0,
    quantity: Number(item.quantity) || 1,
    total: Number(item.total) || 0,
  }));

  // 遍历新选择的商品，进行合并或添加
  formattedItems.forEach((newItem: any) => {
    // 查找是否已存在相同商品和相同单位的项目
    // 使用商品ID和单位ID作为唯一标识，确保只有相同物品相同单位才会覆盖
    const existingItemIndex = retailOrderInfo.items.findIndex(
      (existingItem: any) =>
        existingItem.item === newItem.item &&
        existingItem.unit === newItem.unit
    );

    if (existingItemIndex !== -1) {
      // 如果找到相同商品和相同单位，则用新商品覆盖原有商品
      retailOrderInfo.items[existingItemIndex] = newItem;

      console.log(
        `覆盖商品: ${newItem.item_name} (${newItem.unit_name}), 新数量: ${newItem.quantity}`
      );
    } else {
      // 如果没有找到相同商品和单位的组合，则添加为新项目
      retailOrderInfo.items.push(newItem);
      console.log(
        `添加新商品: ${newItem.item_name} (${newItem.unit_name}), 数量: ${newItem.quantity}`
      );
    }
  });

      // 循环旧数组，然后再循环新数组，遇到item相同的元素，计算总量
      // 首先收集所有需要处理的唯一item
      // 找出新数组中存在的 item ID
      const newItemIds = [...new Set(formattedItems.map(item => item.item))];

      newItemIds.forEach(currentItemId => {
        let total = 0;

        // 循环旧数组，累加所有相同item商品的 remaining_stock
        this.retailOrderInfo.items.forEach(item => {
          if (item.item === currentItemId) {
            total += Number(item.remaining_stock || 0);
          }
        });

        // 循环新数组，累加所有相同item元素的 quantity 乘以转换率
        // 过滤掉旧数组中已存在的商品
        formattedItems.forEach(newItem => {
          if (newItem.item === currentItemId && !this.retailOrderInfo.items.some(item => item.item === newItem.item && item.unit === newItem.unit)) {
            total += Number(newItem.quantity || 0) * Number(newItem.conversion_rate || 1);
          }
        });

        // 给旧数组中所有item相同的元素的 remaining_stock 赋值为 total
        this.retailOrderInfo.items.forEach(item => {
          if (item.item === currentItemId) {
            item.remaining_stock = total;
            console.log("更新后的总数：", total);
            console.log("更新后的商品库存：", item.remaining_stock);
            console.log("更新后的商品数据：", item);
          }
        });
      });
  // 循环旧数组，然后再循环新数组，遇到item相同的元素，计算总量
  // 首先收集所有需要处理的唯一item
  // 找出新数组中存在的 item ID
  const newItemIds = [...new Set(formattedItems.map((item: any) => item.item))];

  newItemIds.forEach(currentItemId => {
    let total = 0;

    // 循环旧数组，累加所有相同item商品的 remaining_stock
    retailOrderInfo.items.forEach((item: any) => {
      if (item.item === currentItemId) {
        total += Number(item.remaining_stock || 0);
      }
    });

    // 循环新数组，累加所有相同item元素的 quantity 乘以转换率
    // 过滤掉旧数组中已存在的商品
    formattedItems.forEach((newItem: any) => {
      if (newItem.item === currentItemId && !retailOrderInfo.items.some((item: any) => item.item === newItem.item && item.unit === newItem.unit)) {
        total += Number(newItem.quantity || 0) * Number(newItem.conversion_rate || 1);
      }
    });

    // 给旧数组中所有item相同的元素的 remaining_stock 赋值为 total
    retailOrderInfo.items.forEach((item: any) => {
      if (item.item === currentItemId) {
        item.remaining_stock = total;
        console.log("更新后的总数：", total);
        console.log("更新后的商品库存：", item.remaining_stock);
        console.log("更新后的商品数据：", item);
      }
    });
  });
};

const showQtyArrows = (index: number) => {
  if (isView.value) {
    uni.showToast({
      title: '当前为已提交状态，不允许修改',
      icon: "none",
    })
    return;
  }
  if (activeQtyIndex.value === index) {
    // 如果已经是当前，说明是第二次点击，收起
    activeQtyIndex.value = -1;
  } else {
    // 否则显示
    activeQtyIndex.value = index;
  }
};
const hideQtyArrows = () => {
  activeQtyIndex.value = -1;
};
const changeQuantity = (item: any, val: number) => {
  // 只更新当前操作的条目quantity
  const currentItemIndex = retailOrderInfo.items.findIndex(
    (infoItem: any) => infoItem.code === item.code && infoItem.unit === item.unit
  );
  if (currentItemIndex === -1) return;
  const currentItem = retailOrderInfo.items[currentItemIndex];

  // 验证库存限制
  if (val > Number(currentItem.stock_remaining)) {
    uni.showToast({ title: "数量大于库存余量", icon: "none", mask: true });
    val = Number(currentItem.stock_remaining);
  } else if (val * Number(currentItem.conversion_rate) > Number(currentItem.total_stock)) {
    uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
    val = Number(currentItem.total_stock) / Number(currentItem.conversion_rate);
  }

  // 更新当前条目的quantity
  currentItem.quantity = val;

  // 直接循环retailOrderInfo.items，计算相同id的商品总用量
  let totalUsedStock = 0;
  retailOrderInfo.items.forEach((item: any) => {
    if (item.code === currentItem.code) {
      totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
    }
  });

  // 将计算出的remainingStock赋值给所有相同id的元素
  retailOrderInfo.items.forEach((item: any) => {
    if (item.code === currentItem.code) {
      item.remaining_stock = totalUsedStock;
    }
  });
  // calcTotalAndDiscount();
};
const decreaseQty = (index: number, item: any) => {
  if (!retailOrderInfo || !retailOrderInfo.items) return;
  const currentItem = retailOrderInfo.items[index];
  if (currentItem && Number(currentItem.quantity) > 1) {
    // 只减少当前条目的quantity
    currentItem.quantity = Number(currentItem.quantity) - 1;

    // 计算所有相同code商品的总用量（考虑各自的conversion_rate）
    const sameCodeItems = retailOrderInfo.items.filter((i: any) => i.code === currentItem.code);
    const totalStock = sameCodeItems[0].total_stock;

    // 直接循环retailOrderInfo.items，计算相同id的商品总用量
    let totalUsedStock = 0;
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      }
    });

    // 将计算出的remainingStock赋值给所有相同id的元素
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        item.remaining_stock = totalUsedStock;
      }
    });
    // calcTotalAndDiscount();
  } else {
    uni.showToast({ title: "数量不能小于1", icon: "none" });
  }
};
const increaseQty = (index: number, item: any) => {
  if (!retailOrderInfo || !retailOrderInfo.items) return;
  if (item) {
    const currentItem = retailOrderInfo.items[index];
    // 验证库存限制
    if (Number(currentItem.quantity) == Number(currentItem.stock_remaining)) {
      uni.showToast({ title: "数量大于库存余量", icon: "none", mask: true });
      return;
    } else if (
      Number(currentItem.quantity * currentItem.conversion_rate) >= Number(currentItem.total_stock)
    ) {
      uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
      currentItem.quantity = Number(currentItem.total_stock) / Number(currentItem.conversion_rate);
    } else {
      // 只增加当前条目的quantity
      currentItem.quantity = Number(currentItem.quantity || 0) + 1;
    }

    // 计算所有相同code商品的总用量（考虑各自的conversion_rate）
    const sameCodeItems = retailOrderInfo.items.filter((i: any) => i.code === currentItem.code);
    const totalStock = sameCodeItems[0].total_stock;

    // 直接循环retailOrderInfo.items，计算相同id的商品总用量
    let totalUsedStock = 0;
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      }
    });

    // 将计算出的remainingStock赋值给所有相同id的元素
    retailOrderInfo.items.forEach((item: any) => {
      if (item.code === currentItem.code) {
        item.remaining_stock = totalUsedStock;
      }
    });
    // calcTotalAndDiscount();
  }
};


//从商品清单组件回来的数据
// 处理金额变化
const handleAmountChange = (amountData: any) => {
  console.log(amountData);
  retailOrderInfo.total_amount = amountData.totalAmount;
  calcDiscount();
  calcDiscountRate();

};
/**
 * 计算折扣金额
 * 根据订单总金额和折扣率计算折扣金额，并保留两位小数
 */
const calcDiscountRate = () => {
  const total = Number(retailOrderInfo.total_amount) || 0;
  const discountRate = Number(retailOrderInfo.discountRate) || 0;
  console.log(discountRate);

  retailOrderInfo.discount = ((total * discountRate) / 100).toFixed(2);
};
/**
 * 计算折扣率
 * 根据订单总金额和折扣金额计算折扣率，并保留两位小数
 * 若总金额为 0，则折扣率设为 0
 */
const calcDiscount = () => {
  const total = Number(retailOrderInfo.total_amount) || 0;
  const discount = Number(retailOrderInfo.discount) || 0;
  if (total > 0) {
    retailOrderInfo.discountRate = ((discount / total) * 100).toFixed(2);
  } else {
    retailOrderInfo.discountRate = "0";
  }
  calcActualAmount();
};
/**
 * 计算实际支付金额
 * 用订单总金额减去折扣金额得到实际支付金额，并保留两位小数
 */
const calcActualAmount = () => {
  const total = Number(retailOrderInfo.total_amount) || 0;
  const discount = Number(retailOrderInfo.discount) || 0;
  const actualAmount = total - discount;
  retailOrderInfo.total_sale_price = actualAmount.toFixed(2);
  retailOrderInfo.total_amount = total.toFixed(2);
};
/**
 * 当折扣率变化时更新相关数据
 * 先计算折扣金额，再更新总金额、实际支付金额、已支付金额和欠款金额
 */
const changeDiscount = () => {
  calcDiscountRate();
  calcActualAmount();
};
/**
 * 当折扣金额变化时更新相关数据
 * 先计算折扣率，再更新总金额、实际支付金额、已支付金额和欠款金额
 */
const changeDiscountRate = () => {
  calcDiscount();
  calcActualAmount();
};

// 获取客户列表
const getCustomersList = () => {
  console.log("开始获取客户列表，参数：", customerPageParams);
  if (!isCustomerMore.value) return;
  getCustomerList(customerPageParams)
    .then((res) => {
      console.log("客户列表API返回结果：", res);
      if (res.code == 0) {
        customerList.value = res.data.results;
        console.log(
          "客户列表加载成功，共",
          customerList.value.length,
          "条数据"
        );
        if (customerPageParams.page_size > res.data.results.length) {
          isCustomerMore.value = false;
        } else {
          customerPageParams.page++;
          isCustomerMore.value = true;
        }
      } else {
        console.error("客户列表加载失败：", res.msg);
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    })
    .catch((err) => {
      console.error("客户列表接口调用失败：", err);
    });
};
//打开客户选择框
const openSelector = (index: number) => {
  if (isView.value) {
    return;
  }
  console.log("用户点击了选择客户，选择类型：", index);
  selectType.value = index;
  // 如果客户列表为空，先拉取数据
  if (selectType.value === 1 && customerList.value.length === 0) {
    console.log("客户列表为空，正在拉取数据...");
    getCustomersList();
  }
  selectorShow.value = true;
  console.log("选择器已打开，当前状态：", selectorShow.value);
  if (selectType.value === 1) {
    selectList.value = customerList.value;
    console.log("客户选择列表数据：", selectList.value);
  } else {
    // this.selectList = this.settlementAccountList; // settlementAccountList is not defined
  }
};
// 选择客户
const selectCustomer = (item: any) => {
  console.log("用户选择了客户：", item);
  if (selectType.value === 1) {
    retailOrderInfo.customer = item.id;
    retailOrderInfo.customer_name = item.name;
    // 手动触发客户字段验证
    // uForm.value.validateField("customer_name"); // validateField is not a direct method on uForm.value
  }
};
// 关闭搜索弹出层
const closePopup = () => {
  console.log("用户关闭了选择器");
  selectorShow.value = false;
};

// 打开库存选择弹出层
const openInventorySelection = () => {
  if (isView.value) {
    uni.showToast({
      title: '当前为已提交状态，不允许修改',
      icon: "none",
    })
    return;
  }
  inventorySelectionShow.value = true;
};
// 关闭库存选择弹出层
const closeInventorySelection = () => {
  inventorySelectionShow.value = false;
};
// 接收库存弹出层的数据
const confirmInventory = (data: any) => {
  console.log(data);

  retailOrderInfo.warehouse = data.id;
  retailOrderInfo.warehouse_name = data.name;
  uni.setStorageSync("retailWarehouseId", data.id);
};

// 添加商品
const addGoods = (index: number) => {
  if (isView.value) {
    uni.showToast({
      title: '当前为已提交状态，不允许修改',
      icon: "none",
    })
    return;
  }
  if (!retailOrderInfo.warehouse) {
    uni.showToast({
      title: '请先选择仓库',
      icon: "none",
    });
    return;
  }
  uni.navigateTo({
    url:
      "/components/productSelection?type=6&isShowWarehouse=true&warehouse=" +
      retailOrderInfo.warehouse +
      "&warehouse_name=" +
      retailOrderInfo.warehouse_name,
  });
};

//打开结算账户选择
const openAccountSelector = () => {
  if (isView.value) {
    uni.showToast({
      title: '当前为已提交状态，不允许修改',
      icon: "none",
    })
    return;
  }
  accountSelectShow.value = true;
};
//删除结算账户
const removePurchaseOrder = () => {
  if (isView.value) {
    uni.showToast({
      title: '当前为已提交状态，不允许修改',
      icon: "none",
    })
    return;
  }
  retailOrderInfo.payment_method = "";
  retailOrderInfo.payment_method_name = "";
};
//处理结算账户的数据
const handleAccount = (data: any) => {
  retailOrderInfo.payment_method = data.id;
  retailOrderInfo.payment_method_name = data.account_name;
};
//关闭结算账户选择
const closeAccountSelector = () => {
  accountSelectShow.value = false;
};

// 打开保存提示框逻辑
const save = () => {
  // 先检查商品列表
  if (
    !retailOrderInfo.items ||
    retailOrderInfo.items.length === 0
  ) {
    uni.showToast({
      title: "请至少添加一个商品",
      icon: "none",
    });
    return;
  }
  //检查结算账户
  // if (
  //   retailOrderInfo.pay_amount > 0 &&
  //   !retailOrderInfo.payment_method
  // ) {
  //   uni.showToast({
  //     title: "请填写结算账户",
  //     icon: "none",
  //   });
  //   return;
  // }
  // 触发表单校验
  if (uForm.value) {
    uForm.value.validate().then((valid: boolean) => {
      if (valid) {
        // 校验通过
        promptShow.value = true;
      } else {
        // 校验不通过
        uni.showToast({ title: "信息未填写完整", icon: "none" });
      }
    })
    .catch((error: any) => {
      console.error("表单验证错误:", error);
      uni.showToast({ title: "信息未填写完整", icon: "none" });
    });
  }
};
//关闭提示框
const close = () => {
  promptShow.value = false;
};

//新建逻辑
const addRetailOrdersFunc = () => {
  addRetailOrders(retailOrderInfo).then((res) => {
    if (res.code == 0) {
      console.log(res);
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });
      uni.navigateBack({
        delta: 1,
      });
    }
  });
};

//删除仓库
const removeWarehouseName = () => {
  if (isView.value) {
    uni.showToast({
      title: '当前为已提交状态，不允许修改',
      icon: "none",
    })
    return;
  }
  retailOrderInfo.warehouse = "";
  retailOrderInfo.warehouse_name = "";
  retailOrderInfo.items = [];
  uni.removeStorageSync("retailWarehouseId");
};

onReady(() => {
  if (uForm.value) {
    uForm.value.setRules(rules);
  }
});

onMounted(() => {
  getCustomersList();
  eventBus.$on("selectGoodsList", handleSelectGoodsList);
});

onBeforeUnmount(() => {
  eventBus.$off("selectGoodsList", handleSelectGoodsList);
});

onLoad((options: any) => {
  console.log(options);

  // 如果有传入订单ID，则加载订单详情进行编辑
  if (options.data) {
    const data = JSON.parse(options.data);
    Object.assign(retailOrderInfo, data);
    retailOrderInfo.total_amount = Number(data.discount) + Number(data.total_sale_price);
    retailOrderInfo.discountRate = (Number(data.discount) / Number(data.total_amount)) * 100;
    // 设置页面标题为编辑模式
    uni.setNavigationBarTitle({
      title: "编辑零售订单",
    });
    isView.value = true;
  } else {
    // 设置页面标题为新增模式
    uni.setNavigationBarTitle({
      title: "新增零售订单",
    });
  }

  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  retailOrderInfo.out_date = `${year}-${month}-${day}`;
});

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
}

.form {
  padding: 0 20rpx;
  width: 95%;
  margin: 0 auto;
}

.goods-list {
  padding: 0 20rpx;
}

.goods_item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  padding: 0 0 0 0;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  position: relative;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_extra {
  font-size: 12px;
  color: #888;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}



.qty_x {
  font-size: 15px;
  font-weight: bold;
  margin-right: 2px;
}

.qty_control {
  display: flex;
  align-items: center;
  position: relative;
}

.qty_input {
  width: 40px;
  height: 22px;
  border: 1px solid #222;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
  background: #fff;
  color: #222;
  margin-left: 2px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;
  margin-left: 30rpx;

  .total-row-right {
    display: flex;
    align-items: center;
  }

  text:nth-child(1) {
    width: 80px;
    margin-left: 30rpx;
  }
}

.telescoping {
  display: flex;
  justify-content: center;
  align-items: center;
}

.remark {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.upload-area {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}

::v-deep .u-form-item__body__left__content__required.data-v-5e7216f1 {
  position: absolute;
  left: 0px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}

::v-deep .u-form-item__body__left__content__label.data-v-5e7216f1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  margin-left: 30rpx;
}

.stock-tag {
  display: inline-block;
  border: 1px solid #ffa940;
  color: #ffa940;
  border-radius: 12px;
  font-size: 20rpx;
  padding: 0 12rpx;
  margin-left: 10rpx;
  line-height: 24rpx;
  height: 24rpx;
  vertical-align: middle;
}
</style>
