{"common": {"uni.app.quit": "もう一度押すと、アプリケーションが終了します", "uni.async.error": "サーバーへの接続がタイムアウトしました。画面をクリックして再試行してください", "uni.showActionSheet.cancel": "キャンセル", "uni.showToast.unpaired": "使用するには、showToastとhideToastをペアにする必要があることに注意してください", "uni.showLoading.unpaired": "使用するには、showLoadingとhideLoadingをペアにする必要があることに注意してください", "uni.showModal.cancel": "キャンセル", "uni.showModal.confirm": "OK", "uni.chooseImage.cancel": "キャンセル", "uni.chooseImage.sourceType.album": "アルバムから選択", "uni.chooseImage.sourceType.camera": "カメラ", "uni.chooseVideo.cancel": "キャンセル", "uni.chooseVideo.sourceType.album": "アルバムから選択", "uni.chooseVideo.sourceType.camera": "カメラ", "uni.previewImage.cancel": "キャンセル", "uni.previewImage.button.save": "画像を保存", "uni.previewImage.save.success": "画像をアルバムに正常に保存します", "uni.previewImage.save.fail": "画像をアルバムに保存できませんでした", "uni.setClipboardData.success": "コンテンツがコピーされました", "uni.scanCode.title": "スキャンコード", "uni.scanCode.album": "アルバム", "uni.scanCode.fail": "認識に失敗しました", "uni.scanCode.flash.on": "タッチして点灯", "uni.scanCode.flash.off": "タップして閉じる", "uni.startSoterAuthentication.authContent": "指紋認識...", "uni.picker.done": "完了", "uni.picker.cancel": "キャンセル", "uni.video.danmu": "「弾幕」", "uni.video.volume": "ボリューム", "uni.button.feedback.title": "質問のフィードバック", "uni.button.feedback.send": "送信"}, "ios": {}, "android": {}}