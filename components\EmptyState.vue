<template>
  <view class="empty-state">
    <view class="empty-content">
      <view class="empty-icon">{{ icon }}</view>
      <view class="empty-text">{{ text }}</view>
      <view class="empty-tip" v-if="tip">{{ tip }}</view>
    </view>
    <view class="empty-action" v-if="showAction">
      <view class="custom-button" @click="handleAction">
        <text class="button-text">{{ actionText }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 定义组件的 props
const props = defineProps({
  // 图标
  icon: {
    type: String,
    default: '📋'
  },
  // 主要文本
  text: {
    type: String,
    default: '暂无数据'
  },
  // 提示文本
  tip: {
    type: String,
    default: ''
  },
  // 是否显示操作按钮
  showAction: {
    type: Boolean,
    default: false
  },
  // 操作按钮文本
  actionText: {
    type: String,
    default: '重新加载'
  }
});

// 定义组件的事件
const emit = defineEmits(['action']);

// 处理按钮点击事件
const handleAction = () => {
  emit('action');
};
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 100rpx 40rpx 60rpx;
  text-align: center;
  min-height: 500rpx;
  position: relative;

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    justify-content: center;
  }

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 32rpx;
    opacity: 0.85;
    filter: grayscale(10%);
    transform: scale(1);
    transition: all 0.3s ease;

    // 添加轻微的动画效果
    animation: float 3s ease-in-out infinite;

    // 添加发光效果
    text-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
  }

  .empty-text {
    font-size: 36rpx;
    color: #333;
    margin-bottom: 16rpx;
    font-weight: 600;
    line-height: 1.4;
    letter-spacing: 0.5rpx;
  }

  .empty-tip {
    font-size: 28rpx;
    color: #888;
    margin-bottom: 40rpx;
    line-height: 1.5;
    max-width: 400rpx;
    opacity: 0.9;
  }

  .empty-action {
    margin-top: auto;
    padding-bottom: 20rpx;
  }

  .custom-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 40rpx;
    background: #87CEEB;
    border-radius: 50rpx;
    box-shadow: 0 4rpx 12rpx rgba(135, 206, 235, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;

    // 按钮按下效果
    &:active {
      transform: translateY(1rpx) scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(135, 206, 235, 0.4);
    }

    // 悬停效果
    &:hover {
      background: #7EC8E3;
      box-shadow: 0 6rpx 16rpx rgba(135, 206, 235, 0.4);
    }

    .button-text {
      color: #ffffff;
      font-size: 28rpx;
      font-weight: 500;
      letter-spacing: 0.5rpx;
    }
  }
}

// 浮动动画
@keyframes float {
  0%, 100% {
    transform: translateY(0rpx);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .empty-state {
    padding: 80rpx 30rpx 60rpx;

    .empty-icon {
      font-size: 100rpx;
      margin-bottom: 24rpx;
    }

    .empty-text {
      font-size: 32rpx;
      margin-bottom: 12rpx;
    }

    .empty-tip {
      font-size: 26rpx;
      margin-bottom: 32rpx;
    }
  }
}
</style>
