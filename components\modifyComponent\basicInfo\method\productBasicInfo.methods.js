export default {
  // 处理商品类型选择
  handleProductTypeChange(value) {
    this.localInfo.item_type = value;
  },
  
  // 更新商品信息
  updateProductInfo(data) {
    this.localInfo = { ...this.localInfo, ...data };
  },
  
  // 打开商品类别弹出层
  openProductCategory() {
    this.CategoryShow = true;
  },
  
  // 更新商品类别弹出层显示状态
  updateProductCategoryShow(newMessage) {
    this.CategoryShow = newMessage;
  },
  
  // 更新商品类别
  updateProductCategory(newMessage) {
    console.log(newMessage);
    this.localInfo.item_type = newMessage.id;
    this.localInfo.item_type_name = newMessage.name;
  },
  
}