import request from '@/utils/request';
import store from '@/store';

// 定义类别数据接口
interface CatalogTypeData {
  id?: string; // 类别ID，可选，因为新增时可能没有
  for_type: string; // 类别类型
  name: string; // 类别名称
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取类别列表
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getCatalogtypes = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/catalogtypes/`, data);
};

/**
 * 新增类别列表
 * @param {CatalogTypeData} data - 类别数据
 * @returns {Promise<any>}
 */
export const addCatalogtypes = (data: CatalogTypeData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/catalogtypes/`, data);
};

/**
 * 更新类别列表
 * @param {CatalogTypeData} data - 类别数据
 * @returns {Promise<any>}
 */
export const editCatalogtypes = (data: CatalogTypeData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/catalogtypes/${data.id}/`, data);
};

/**
 * 删除类别列表
 * @param {object} data - 包含类别ID、for_type 和 name 的数据
 * @returns {Promise<any>}
 */
export const deleteCatalogtypes = (data: { id: string; for_type: string; name: string }): Promise<any> => {
  console.log(data);
  const requestData = {
    id: data.id,
    for_type: data.for_type,
    name: data.name,
  };
  return request.delete(`/ztx/${getLedgerName()}/catalogtypes/${data.id}/`, requestData);
};
