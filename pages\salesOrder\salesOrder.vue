<template>
  <view>
    <view class="container">
      <view>
        <!-- 搜索区域 -->
        <search :searchType="4" @search-result="handleSearchResult" />
        <!-- 展示销售订单列表区域 -->
        <orderList :orderResults="orderResults" :isOrderListRefresh="isSalesOrderRefresh"
          @update:isOrderListRefresh="isSalesOrderRefresh = $event"
          @orderDetail="handleOrderDetail" />
      </view>
      <!-- 底部输入区域 -->
      <view class="inputArea">
        <Input style="height: 100%;" :isShowCamera="true" />
      </view>
    </view>
    <view>
      <!-- 对话悬浮以及添加订单按钮 -->
      <interactive :isShowAddBtn="true" :jumpToId="5"  />
      
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { onShow, onReady } from '@dcloudio/uni-app';
import Input from '@/components/input/input.vue';
import search from '@/components/search.vue';
import orderList from '@/components/orderList.vue';
import interactive from '@/components/interactive.vue';
import { getSalesOrderList, getSalesOrderDetail } from '@/api/salesOrder';
import eventBus from '@/utils/eventBus';

const orderResults = ref({});
const isSalesOrderRefresh = ref(false);

const fetchOrderList = (params = {}) => {
  getSalesOrderList(params).then((res: any) => {
    if (res && res.data) {
      orderResults.value = res.data;
      console.log('订单数据已加载，共', res.data.results?.length || 0, '条');
      console.log('订单数据结构:', res.data);
      console.log('第一个订单数据:', res.data.results?.[0]);
    }
  }).catch((err: any) => {
    console.error('获取订单列表失败:', err);
    uni.showToast({
      title: '获取订单列表失败',
      icon: 'none'
    });
  });
};

const handleSearchResult = (results: any) => {
  console.log('搜索结果:', results);

  if (results === null || results === undefined) {
    // 搜索为空，重新获取所有数据
    fetchOrderList();
  } else if (results && typeof results === 'object') {
    // 后端搜索返回的分页对象（包括空结果）
    orderResults.value = results;

    // 如果搜索结果为空，显示提示
    if (results.results && results.results.length === 0) {
      console.log('搜索无结果');
      // 可以选择显示提示信息
      // uni.showToast({
      //   title: '未找到匹配的订单',
      //   icon: 'none'
      // });
    }
  } else {
    // 其他情况，重新获取数据
    fetchOrderList();
  }
};

// 处理销售订单刷新事件
const handleRefreshSalesOrders = () => {
  console.log('收到销售订单刷新事件，重新获取订单列表');
  fetchOrderList();
};

// 处理订单详情点击
const handleOrderDetail = (orderItem: any) => {
  console.log('=== salesOrder页面 - 接收到订单详情事件 ===');
  console.log('接收到的orderItem参数:', orderItem);
  console.log('orderItem的类型:', typeof orderItem);
  console.log('orderItem是否为null:', orderItem === null);
  console.log('orderItem是否为undefined:', orderItem === undefined);

  // 检查订单数据是否有效
  if (!orderItem) {
    console.error('订单数据为空');
    uni.showToast({
      title: '订单数据异常',
      icon: 'none'
    });
    return;
  }

  if (!orderItem.id) {
    console.error('订单ID不存在:', orderItem);
    uni.showToast({
      title: '订单ID不存在',
      icon: 'none'
    });
    return;
  }

  console.log('准备跳转，订单ID:', orderItem.id);

  // 跳转到销售订单编辑页面
  uni.navigateTo({
    url: `/pages/salesOrder/addSalesOrder?id=${orderItem.id}&mode=edit`
  });

  console.log('=== salesOrder页面 - 跳转完成 ===');
};

const goAddOrder = () => {
  uni.navigateTo({ url: '/pages/salesOrder/addSalesOrder' });
  console.log("添加销售订单");
};

onShow(() => {
  fetchOrderList();
});

onReady(() => {
  // 页面渲染完成后再次检查数据
  console.log('页面渲染完成，当前订单数据:', orderResults.value);
});

onMounted(() => {
  // 监听销售订单刷新事件
  eventBus.$on('refreshSalesOrders', handleRefreshSalesOrders);
});

onBeforeUnmount(() => {
  // 移除事件监听
  eventBus.$off('refreshSalesOrders', handleRefreshSalesOrders);
});

</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #f1f1f1;
}
.inputArea {
  width: 100%;
}
.add-order-btn {
  width: 90%;
  margin: 20rpx auto 10rpx auto;
  display: block;
  background: #2b85e4;
  color: #fff;
  font-size: 30rpx;
  border-radius: 10rpx;
}
</style> 