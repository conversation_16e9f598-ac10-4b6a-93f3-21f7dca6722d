<template>
  <view>
    <Search :searchType="0" @search-result="getSearchResult" v-if="isShowSearch" />
    <goodsList :selectGoods="true" :type="type" :searchResults="searchResults" :relatedOrderGoods="relatedOrderGoods"
      :deselectItemList="deselectItemList" :deselectItem="deselectItem" :warehouseData="warehouseData"
      @selectedGoodsList="selectedGoodsList" @selectType="selectType" :key="'goodsList-' + type" />

    <!-- 遮罩层 -->
    <view v-if="shoppingCartShow" class="cart_mask" @click="expandShoppingCart"></view>

    <!-- 购物车面板 -->
    <view v-if="shoppingCartShow" class="cart_popup" :animation="cartAnimation">
      <view class="cart_container">
        <view class="categoryConfirmationBtn">
          <view>已选商品</view>
          <view class="blueFont" @click="clearSelectGoodsList">清空</view>
        </view>
        <view class="divider"></view>
        <view class="goods">
          <view class="goods_item" v-for="(item, index) in selectGoodsList" :key="index">
            <view class="goods_left">
              <image class="goods_img" :src="item.imgurl || '/static/img/logo.png'" mode=""></image>
              <view class="goods_info">
                <view class="goods_extra">
                  {{ item.name }}
                  <view class="stock-tag" v-if="type == 6">
                    库存：{{ Math.floor(item.total_stock) }} - {{ Math.floor(item.remaining_stock) }}
                  </view>
                </view>
                <view class="goods_code">{{ item.code }}</view>
                <view class="goods_price">
                  <text class="price">￥{{ item.purchase_price || "未填写" }}</text>
                  <text class="unit">/{{ item.unit_name || "未填写" }}</text>
                </view>
              </view>
            </view>
            <view class="goods_num">
              <view class="goods_num_reduce" @click.stop="reduceNum(item)">
                <i-reduce-one theme="outline" size="20" fill="#3894ff" />
              </view>
              <view class="goods_num_input">{{ item.quantity || 0 }}</view>
              <view class="goods_num_add" @click.stop="addNum(item)">
                <i-add-one theme="filled" size="20" fill="#3894ff" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 操作按钮（展开时） -->
      <view class="operatingButton popup-btn">
        <view class="selectGoods">
          <u-button type="warning" @click="expandShoppingCart">
            <i-shopping theme="outline" size="24" fill="#fff" />
            已选择({{ selectGoodsList.length || 0 }})
          </u-button>
        </view>
        <view class="selectGoods">
          <u-button type="primary" @click="confirmSelectGoods">
            <i-check-small theme="outline" size="24" fill="#fff" />选好了
          </u-button>
        </view>
      </view>
      <Input style="height: 100%" />
    </view>

    <!-- 底部操作按钮（收缩时） -->
    <view v-if="!shoppingCartShow" class="operatingButton fixed-btn">
      <view class="selectGoods">
        <u-button type="warning" @click="expandShoppingCart">
          <i-shopping theme="outline" size="24" fill="#fff" />
          已选择({{ selectGoodsList.length || 0 }})
        </u-button>
      </view>
      <view class="selectGoods">
        <u-button type="primary" @click="confirmSelectGoods">
          <i-check-small theme="outline" size="24" fill="#fff" />选好了
        </u-button>
      </view>
    </view>

    <Input style="height: 100%" v-if="!shoppingCartShow" />

    <ProductDetails :selectGoodsList="selectGoodsList" :selectedGoodsQuantity="selectedGoodsQuantity" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import Input from "@/components/input/input.vue";
import goodsList from "@/components/goodsList.vue";
import productDetails from "@/components/productDetails.vue";
import Search from "@/components/search.vue";
import eventBus from "@/utils/eventBus";

// 响应式状态
const relatedOrderGoods = ref([]); //关联订单商品
const selectGoodsList = ref([]); //选择的商品列表
const searchResults = ref([]); //搜索内容
const deselectItem = ref({}); //取消选择商品
const deselectItemList = ref([]); //取消选择商品列表
const shoppingCartShow = ref(false);
const cartAnimation = ref(null);
const isShowWarehouse = ref(null); //在商品列表是否显示仓库字段
const type = ref(0);
const isShowSearch = ref(false); //是否选择商品

const warehouseData = reactive({
  warehouse: '', //仓库id
  warehouse_name: '', //仓库名称
});

// 生命周期钩子
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  // 这里可以处理options
});

export default {
  components: {
    Input,
    goodsList,
    Search,
    productDetails,
  },
  data() {
    return {
      relatedOrderGoods: [], //关联订单商品
      selectGoodsList: [], //选择的商品列表
      searchResults: [], //搜索内容
      deselectItem: {}, //取消选择商品
      deselectItemList: [], //取消选择商品列表
      shoppingCartShow: false,
      cartAnimation: null,
      isShowWarehouse: null,//在商品列表是否显示仓库字段
      warehouseData: {
        warehouse: '',//仓库id
        warehouse_name: '',//仓库名称
      },
      type: 0,
      /* 判断进货关联订单(0)
        任意商品(1)
        采购订单 || 销售订单(2)
        退货:关联进货单进入(4)
        退货：任意商品进入(5)
        零售订单(6)
        零售退货订单：关联订单进入(7)
        零售退货订单:任意商品进入(8)
        销货单(10)
      */
      isShowSearch: false, //是否选择商品
      related_order_id: '', // 关联订单ID
      source: '', // 商品选择来源
    };
  },
  onLoad(options) {
    // 接收type
    console.log("productSelection onLoad options:", options);

  if (options.type) {
    type.value = Number(options.type);
    if (type.value == 0 || type.value == 4 || type.value == 7) {
      isShowSearch.value = false;
    } else {
      isShowSearch.value = true;
    }
  }
    if (options.type) {
      this.type = Number(options.type);
      console.log("设置类型为:", this.type);
      // 确保类型设置正确
      setTimeout(() => {
        console.log("确认当前类型设置:", this.type);
      }, 100);

      if (this.type == 0 || this.type == 4 || this.type == 7) {
        this.isShowSearch = false;
      } else {
        this.isShowSearch = true;
      }
    }

  if (options.isShowWarehouse) {
    isShowWarehouse.value = options.isShowWarehouse;
  }
    // 保存关联订单ID
    if (options.related_order_id) {
      this.related_order_id = options.related_order_id;
      console.log('保存关联订单ID:', this.related_order_id);
    }

    // 保存来源标记，用于区分是从普通添加还是从关联物品按钮进入
    if (options.source) {
      this.source = options.source;
      console.log('商品选择来源:', this.source);
    }

    if (options.isShowWarehouse) {
      this.isShowWarehouse = options.isShowWarehouse
    }

  if (options.warehouse && options.warehouse_name) {
    warehouseData.warehouse = Number(options.warehouse);
    warehouseData.warehouse_name = options.warehouse_name;
  }
    //接收warehouse和warehouse_name
    if (options.warehouse && options.warehouse_name) {
      this.warehouseData.warehouse = options.warehouse
      this.warehouseData.warehouse_name = options.warehouse_name || ""
      console.log('设置仓库信息:', this.warehouseData);
    } else if (options.warehouse) {
      // 只有warehouse没有warehouse_name的情况
      this.warehouseData.warehouse = options.warehouse
      console.log('只设置仓库ID:', this.warehouseData);
    }

    // 处理销货单类型，确保仓库信息正确设置
    if (this.type === 10) {
      console.log("销货单类型特殊处理，检查仓库信息");

      // 确保销货单情况下仓库信息正确
      setTimeout(() => {
        console.log("检查销货单仓库信息:", this.warehouseData);
      }, 300);
    }

  if (
    options.data !== null &&
    options.data !== "" &&
    options.data !== "{}" &&
    options.data !== "[]"
  ) {
    const data = JSON.parse(options.data);
    //接收列表数组
    if (
      options.data !== null &&
      options.data !== "" &&
      options.data !== "{}" &&
      options.data !== "[]"
    ) {
      console.log("开始解析传入的数据");
      try {
        // 先进行URL解码，再解析JSON
        const decodedData = decodeURIComponent(options.data);
        console.log("URL解码后的数据长度:", decodedData.length);

        const data = JSON.parse(decodedData);
        console.log("JSON解析后的数据类型:", typeof data, "是否为数组:", Array.isArray(data));

    if (Array.isArray(data) && data.length === 0) return;
    if (
      typeof data === "object" &&
      !Array.isArray(data) &&
      Object.keys(data).length === 0
    )
      return;
        // 销货单(type=10)的特殊处理 - 直接获取items数组
        if (this.type === 10 && data.items) {
          console.log("销货单数据处理 - 使用items数组，长度:", data.items.length);

          // 提取订单详情中的仓库信息
          if (data.orderDetail) {
            console.log("从订单详情中提取仓库信息:", {
              warehouse_id: data.orderDetail.warehouse_id,
              warehouse_name: data.orderDetail.warehouse_name
            });

            // 如果订单详情中有仓库信息且当前没有设置，则使用订单详情中的仓库信息
            if (data.orderDetail.warehouse_id && !this.warehouseData.warehouse) {
              this.warehouseData.warehouse = data.orderDetail.warehouse_id;
            }

            if (data.orderDetail.warehouse_name && !this.warehouseData.warehouse_name) {
              this.warehouseData.warehouse_name = data.orderDetail.warehouse_name;
            }

            console.log("更新后的仓库信息:", this.warehouseData);
          }

          // 处理商品数据，确保每个商品都有仓库信息
          this.relatedOrderGoods = data.items.map(item => ({
            ...item,
            // 确保每个商品都有仓库信息
            warehouse: item.warehouse || this.warehouseData.warehouse,
            warehouse_name: item.warehouse_name || this.warehouseData.warehouse_name
          }));

          console.log("设置relatedOrderGoods完成:", this.relatedOrderGoods.length);

          // 额外检查
          if (this.relatedOrderGoods.length === 0) {
            console.warn("警告: 销货单数据处理后relatedOrderGoods为空数组");
          } else {
            console.log("销货单第一个商品数据:", this.relatedOrderGoods[0]);
          }
        }
        // 非销货单处理逻辑
        else {
          let processData = data;
          // 如果data不是数组而是包含数组的对象，尝试提取数组
          if (!Array.isArray(data) && typeof data === 'object') {
            if (data.items && Array.isArray(data.items)) {
              processData = data.items;
              console.log("从对象中提取items数组:", processData.length);
            }
          }

          if (Array.isArray(processData) && processData.length === 0) {
            console.log("解析后的数据是空数组");
            return;
          }

    // 计算并添加total_cost字段
    const processedData = data.map((item) => ({
      ...item,
      total_cost: item.purchase_price * item.quantity,
    }));
    relatedOrderGoods.value = processedData;
  }
});

// 方法
const getSearchResult = (newVal) => {
  searchResults.value = newVal;
};
          const processedData = processData.map((item) => ({
            ...item,
            total_cost: item.purchase_price * item.quantity,
          }));

          this.relatedOrderGoods = processedData;
          console.log("处理后的relatedOrderGoods:", this.relatedOrderGoods.length);
        }

        // 确认数据设置完成后，再次检查type和数据
        setTimeout(() => {
          console.log("数据处理完成后再次确认 - 类型:", this.type, "数据长度:", this.relatedOrderGoods.length);
        }, 200);

      } catch (error) {
        console.error("解析数据时出错:", error, "原始数据:", options.data);
      }
    } else {
      console.log("没有接收到有效的数据");
    }
  },

  onShow() {
    console.log("productSelection onShow - 当前类型:", this.type, "数据长度:", this.relatedOrderGoods.length, "仓库信息:", this.warehouseData);

    // 确保销货单数据正确设置
    if (this.type === 10 && (!this.relatedOrderGoods || this.relatedOrderGoods.length === 0)) {
      console.warn("警告: onShow时销货单数据为空，尝试重新获取数据");

      // 尝试重新处理页面参数中的数据
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;

      if (options && options.data) {
        console.log("尝试从页面参数重新解析数据");
        try {
          const decodedData = decodeURIComponent(options.data);
          const data = JSON.parse(decodedData);

          if (data && data.items) {
            console.log("找到items数据，长度:", data.items.length);

            // 如果有仓库信息，先更新仓库信息
            if (data.orderDetail && data.orderDetail.warehouse_id) {
              console.log("从订单详情中提取仓库信息");
              if (!this.warehouseData.warehouse) {
                this.warehouseData.warehouse = data.orderDetail.warehouse_id;
              }
              if (!this.warehouseData.warehouse_name && data.orderDetail.warehouse_name) {
                this.warehouseData.warehouse_name = data.orderDetail.warehouse_name;
              }
              console.log("更新仓库信息:", this.warehouseData);
            }

            // 更新商品数据，确保包含仓库信息
            this.relatedOrderGoods = data.items.map(item => ({
              ...item,
              warehouse: item.warehouse || this.warehouseData.warehouse,
              warehouse_name: item.warehouse_name || this.warehouseData.warehouse_name
            }));

            console.log("重新设置relatedOrderGoods完成, 包含仓库信息");
          }
        } catch (error) {
          console.error("重新解析数据失败:", error);
        }
      }
    }
  },
  watch: {
  },
  methods: {
    getSearchResult(newVal) {
      this.searchResults = newVal;
    },
    selectedGoodsList(data) {
      console.log('接收到商品数据:', data);

const selectedGoodsList = (data) => {
  selectGoodsList.value = data;
};

const selectType = (newType) => {
  type.value = newType;
};

const expandShoppingCart = () => {
  if (selectGoodsList.value.length <= 0 && !shoppingCartShow.value) {
    uni.showToast({
      title: "您需要先挑选商品",
      icon: "none",
    });
    return;
  }
  if (!shoppingCartShow.value) {
    // 展开动画
    shoppingCartShow.value = true;
    nextTick(() => {
      let animation = uni.createAnimation({
        duration: 300,
        timingFunction: "ease",
      });
      animation.translateY(0).opacity(1).step();
      cartAnimation.value = animation.export();
    });
  } else {
    // 收缩动画
    let animation = uni.createAnimation({
      duration: 300,
      timingFunction: "ease",
    });
    animation.translateY("100%").opacity(0).step();
    cartAnimation.value = animation.export();
    setTimeout(() => {
      shoppingCartShow.value = false;
    }, 300);
  }
};
      if (!data || !Array.isArray(data)) {
        console.warn('接收到的数据无效或不是数组');
        return;
      }

      // 检查是否已经从数据源接收了sales_order_item标记
      const hasSalesOrderItems = data.some(item => item.sales_order_item === true);

      // 检查数据中是否有item字段，如果没有则使用id字段
      const processData = data.map(item => {
        // 确保有正确的item字段（优先使用id作为item值）
        const processedItem = {
          ...item,
          item: item.id || item.item || item.code
        };

        // 打印处理后的商品信息，便于调试
        console.log(`处理商品: ${processedItem.item_name || item.name}, id=${item.id}, 映射后item=${processedItem.item}`);

        return processedItem;
      });

      // 如果数据已经包含sales_order_item标记，或者来源是从关联物品按钮进入，才保留标记
      if (hasSalesOrderItems || this.source === 'wholesale') {
        console.log('保留销售订单商品标记');
        this.selectGoodsList = processData;
      } else {
        // 否则清除任何销售订单标记，确保普通添加的商品没有这些标记
        this.selectGoodsList = processData.map(item => ({
          ...item,
          sales_order_item: false,
          from_sales_order: false,
          related_order_id: ''
        }));
        console.log('清除销售订单商品标记');
      }

      // 最后检查一下选中商品列表中的item字段
      console.log('处理后的选中商品列表:', this.selectGoodsList.map(item => ({
        name: item.item_name || item.name,
        id: item.id,
        item: item.item,
        code: item.code
      })));
    },
    //控制选好了的跳转逻辑
    selectType(type) {
      this.type = type;
    },
    expandShoppingCart() {
      if (this.selectGoodsList.length <= 0 && !this.shoppingCartShow) {
        uni.showToast({
          title: "您需要先挑选商品",
          icon: "none",
        });
        return;
      }
      if (!this.shoppingCartShow) {
        // 展开动画
        this.shoppingCartShow = true;
        this.$nextTick(() => {
          let animation = uni.createAnimation({
            duration: 300,
            timingFunction: "ease",
          });
          animation.translateY(0).opacity(1).step();
          this.cartAnimation = animation.export();
        });
      } else {
        // 收缩动画
        let animation = uni.createAnimation({
          duration: 300,
          timingFunction: "ease",
        });
        animation.translateY("100%").opacity(0).step();
        this.cartAnimation = animation.export();
        setTimeout(() => {
          this.shoppingCartShow = false;
        }, 300);
      }
    },

// 重新计算相同item的remaining_stock
const recalculateRemainingStock = (itemId) => {
  // 计算相同item的总用量
  let totalUsedStock = 0;
  selectGoodsList.value.forEach(goods => {
    if (goods.item === itemId) {
      totalUsedStock += Number(goods.quantity || 0) * Number(goods.conversion_rate || 1);
    }
  });

  // 更新所有相同item的remaining_stock
  selectGoodsList.value.forEach(goods => {
    if (goods.item === itemId) {
      goods.remaining_stock = totalUsedStock;
    }
  });
};

const reduceNum = (item) => {
  item.quantity -= 1;

  if (type.value == 6) {//如果是零售单，减少数量，需要变换库存减少量
    const total = 1 * Number(item.conversion_rate);
    const reduceStock = item.remaining_stock - total;

    // 循环selectGoodsList，对所有相同item的商品都减少库存
    selectGoodsList.value.forEach((goods) => {
      if (goods.item === item.item) {
        goods.remaining_stock = reduceStock;
      }
    });
  }

  if (item.quantity <= 0) {
    deselectItem.value = item;
    selectGoodsList.value.splice(selectGoodsList.value.indexOf(item), 1);

    // 当商品被移除时，重新计算相同item的remaining_stock
    if (type.value == 6) {
      recalculateRemainingStock(item.item);
    }

    if (type.value == 4) {
      uni.removeStorageSync('returnWarehouse');
      eventBus.$emit('returnWarehouse', {});
    }
  }
};

const addNum = (item) => {
  item.quantity = Number(item.quantity) + 1;

  if (type.value == 6) {//如果是零售单，增加数量，需要变换库存减少量
    const oldRemainingStock = item.remaining_stock;
    const total = 1 * Number(item.conversion_rate);
    const AddStock = item.remaining_stock + total;

    if (AddStock >= item.total_stock) {
      uni.showToast({
        title: "当前库存不足",
        icon: "none",
        mask: true,
      });
      item.quantity -= 1;
      selectGoodsList.value.forEach((goods) => {
        if (goods.item === item.item) {
          goods.remaining_stock = oldRemainingStock;
        }
      });
      return;
    }

    // 更新所有相同item的remaining_stock
    selectGoodsList.value.forEach((goods) => {
      if (goods.item === item.item) {
        goods.remaining_stock = AddStock;
      }
    });
  }
};

    //确认选择商品
    confirmSelectGoods() {
      console.log("确认选择的商品列表:", this.selectGoodsList);

      // 获取页面栈信息，用于调试
      const pages = getCurrentPages();
      console.log("当前页面栈:", pages.map(p => p.route));
      console.log("当前页面栈长度:", pages.length);
      console.log("当前页面:", pages[pages.length - 1].route);

      // 确保选择商品时保持销售订单标记一致性
      const hasSalesOrderItems = this.selectGoodsList && this.selectGoodsList.some(item => item.sales_order_item === true);

      // 只有真正的销售订单商品才应该保留标记
      if (!hasSalesOrderItems && this.source !== 'wholesale' && this.selectGoodsList) {
        // 清除任何销售订单标记
        this.selectGoodsList = this.selectGoodsList.map(item => ({
          ...item,
          sales_order_item: false,
          from_sales_order: false,
          related_order_id: ''
        }));
        console.log("已清除非销售订单商品的标记");
      }

      // 仅当有关联订单ID且类型为批发订单(type=10)时，才添加标记
      if (this.related_order_id && this.selectGoodsList && this.selectGoodsList.length > 0 && this.type === 10) {
        this.selectGoodsList = this.selectGoodsList.map(item => ({
          ...item,
          related_order_id: this.related_order_id,
          from_sales_order: true
        }));
        console.log("已为所有商品添加关联订单标记");
      }

      // 在发送数据前，确保所有商品都有正确的item字段
      if (this.selectGoodsList && this.selectGoodsList.length > 0) {
        // 再次检查并确保所有商品都有正确的item字段
        this.selectGoodsList = this.selectGoodsList.map(item => {
          // 如果没有item字段，优先使用id
          if (!item.item) {
            console.log(`商品 ${item.item_name || item.name} 缺少item字段，使用id=${item.id}或code=${item.code}`);
            return {
              ...item,
              item: item.id || item.code
            };
          }
          return item;
        });

        // 检查处理后的数据
        const missingItemCount = this.selectGoodsList.filter(item => !item.item).length;
        if (missingItemCount > 0) {
          console.warn(`警告: 仍有${missingItemCount}个商品缺少item字段`);
        } else {
          console.log("所有商品都已有正确的item字段");
        }

        try {
          uni.setStorageSync('selected_goods_list', JSON.stringify(this.selectGoodsList));
          console.log("商品数据已存入本地缓存");
        } catch (e) {
          console.error("缓存商品数据失败:", e);
        }
      }

      // 发送选择的商品数据到监听页面
      eventBus.$emit("selectGoodsList", this.selectGoodsList);
      console.log("已发送selectGoodsList事件，事件数据长度:", this.selectGoodsList.length);

      // 针对type=10销货单的处理，直接跳转到批发订单编辑页面
      if (this.type === 10) {
        console.log("销货单类型(type=10)，直接跳转到批发订单编辑页面");

        // 检查页面栈是否有editWholesaleOrders页面
        const hasEditWholesaleOrdersPage = pages.some(p =>
          p.route && p.route.includes('wholesaleOrders/editWholesaleOrders/editWholesaleOrders')
        );

        if (hasEditWholesaleOrdersPage) {
          console.log("找到批发订单编辑页面在页面栈中，使用navigateBack");

          // 找到editWholesaleOrders页面在栈中的位置
          let deltaCount = 1; // 默认返回一级
          for (let i = pages.length - 2; i >= 0; i--) {
            if (pages[i].route && pages[i].route.includes('wholesaleOrders/editWholesaleOrders/editWholesaleOrders')) {
              deltaCount = pages.length - 1 - i;
              break;
            }
          }

          console.log(`需要返回${deltaCount}级页面到达批发订单编辑页面`);
          uni.navigateBack({
            delta: deltaCount,
            success: () => {
              console.log(`成功返回${deltaCount}级页面到批发订单编辑页面`);
            },
            fail: (err) => {
              console.error("返回失败:", err);

              // 如果navigateBack失败，使用redirectTo直接跳转
              this.redirectToEditPage();
            }
          });
        } else {
          // 如果页面栈中没有批发订单编辑页面，直接使用redirectTo
          this.redirectToEditPage();
        }
      } else if (this.type === 0 || this.type === 4 || this.type === 7) {
        console.log("返回两级页面");
        uni.navigateBack({
          delta: 2,
          success: () => {
            console.log("成功返回两级页面");
          },
          fail: (err) => {
            console.error("返回失败:", err);
          }
        });
      } else {
        console.log("返回一级页面，类型:", this.type);
        uni.navigateBack({
          delta: 1,
          success: () => {
            console.log("成功返回到上一级页面");
          },
          fail: (err) => {
            console.error("返回失败:", err);
          }
        });
      }
    },

    // 辅助方法：直接跳转到批发订单编辑页面
    redirectToEditPage() {
      console.log("使用redirectTo直接跳转到批发订单编辑页面");
      uni.redirectTo({
        url: "/pages/wholesaleOrders/editWholesaleOrders/editWholesaleOrders",
        success: () => {
          console.log("跳转到批发订单编辑页面成功");
        },
        fail: (err) => {
          console.error("跳转失败:", err);

          // 如果redirectTo失败，尝试使用reLaunch
          console.log("尝试使用reLaunch方法");
          uni.reLaunch({
            url: "/pages/wholesaleOrders/editWholesaleOrders/editWholesaleOrders",
            success: () => {
              console.log("重新启动到批发订单编辑页面成功");
            },
            fail: (error) => {
              console.error("重新启动失败:", error);

              // 最后尝试跳转到首页
              uni.switchTab({
                url: "/pages/index/index",
                success: () => {
                  uni.showToast({
                    title: "请重新进入批发订单",
                    icon: "none",
                    duration: 3000
                  });
                }
              });
            }
          });
        }
      });
    },
const confirmSelectGoods = () => {
  eventBus.$emit("selectGoodsList", selectGoodsList.value);
  if (type.value === 0 || type.value === 4 || type.value == 7) {
    uni.navigateBack({
      delta: 2,
    });
  } else {
    uni.navigateBack({
      delta: 1,
    });
  }
};

const clearSelectGoodsList = () => {
  if (type.value == 4) {
    uni.removeStorageSync('returnWarehouse');
    eventBus.$emit('returnWarehouse', {});
  }
  selectGoodsList.value.forEach((item) => {
    deselectItemList.value.push(item.item);
  });
  selectGoodsList.value = [];
};
</script>

<style lang="scss" scoped>
::v-deep .cart-popup-slide-enter-active,
::v-deep .cart-popup-slide-leave-active {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s;
}

::v-deep .cart-popup-slide-enter-from,
::v-deep .cart-popup-slide-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

::v-deep .cart-popup-slide-enter-to,
::v-deep .cart-popup-slide-leave-from {
  transform: translateY(0);
  opacity: 1;
}

.cartAndBtn {
  height: 900rpx;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  background-color: #fff;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selectGoods {
  width: 50%;
}

.cart_container {
  height: 800rpx;
  background-color: #fff;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.goods_item {
  width: 90%;
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin: 0 auto;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.goods_left {
  display: flex;
  align-items: center;
  flex: 1;
}

.goods_img {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.goods_info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.goods_extra {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #333;

  .stock-tag {
    /* background-color: #fff3cd; */
    border: 1px solid #e88b32;
    border-radius: 12rpx;
    padding: 0rpx 30rpx;
    font-size: 24rpx;
    color: #e88b32;
    white-space: nowrap;
  }
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_code {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.goods_num {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.goods_num_input {
  text-align: center;
  font-size: 15px;
  margin: 0 6px;
  color: #222;
}

.cart_mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.cart_popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1001;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
}

.cart_container {
  overflow-y: auto;
  max-height: 60vh;
  background-color: #fff;
}

.popup-btn {
  width: 100%;
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.selectGoods {
  width: 50%;
}

::v-deep .operatingButton .u-button.data-v-3bf2dba7 {
  height: 35px;
  width: 90%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}
</style>
