<template>
  <view class="a-icon-svg" :style="[boxStyle]">
    <!-- #ifdef APP-NVUE -->
    <image class="a-icon-pack-image" :style="[boxStyle]" :src="png" mode="scaleToFill"></image>
    <web-view v-if="isShow" class="__wh-0" :ref="_uid"  @onPostMessage="changeMessage" 
    src="/uni_modules/icon-park/hybrid/html/index.html" ></web-view>
    <!-- #endif -->
    <!-- #ifndef APP-NVUE -->
    <image class="a-icon-pack-image" :src="url" mode="aspectFit"></image>
    <!-- #endif -->
  </view>
</template>

<script>
import { IconWrapper , genContent } from '../runtime'
export default genContent('i-bye', IconWrapper('i-bye',(props)=>(
    '<?xml version="1.0" encoding="UTF-8"?>'
    + '<svg width="' + props.sizeWithUnit + '" height="' + props.sizeWithUnit + '" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">'
        + '<path d="M34.9998 26.6138L15.146 7.31381C13.9866 6.18672 12.133 6.21294 11.0059 7.37237C10.965 7.41445 10.9254 7.45775 10.8871 7.50222C9.74412 8.82893 9.82256 10.8138 11.0666 12.0462L21.0908 21.9762" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '"/>'
        + '<path d="M21.0909 21.9761L10.1773 11.1547C8.88352 9.87182 6.8201 9.80162 5.44214 10.9936C4.17554 12.0892 4.03694 14.0041 5.13256 15.2707C5.17411 15.3188 5.21715 15.3655 5.26164 15.4108L16.2553 26.6137" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
        + '<path d="M16.2551 26.6137L9.99976 20.5C8.73742 19.2102 6.67293 19.1744 5.36657 20.4195C4.0742 21.6514 4.02513 23.6977 5.25697 24.99C5.26264 24.996 5.26833 25.0019 5.27405 25.0078C14.5036 34.5442 19.2938 39.0485 21.0908 40.5534C23.9998 42.9896 29.7348 44 32.7302 42C35.7257 40 38.4331 37.1544 39.7181 34.3287C40.4831 32.6464 41.9689 27.4595 44.1756 18.7678C44.6248 16.9986 43.5547 15.2001 41.7854 14.7509C41.7625 14.7451 41.7395 14.7395 41.7164 14.7342C39.8832 14.3104 38.0429 15.4115 37.5496 17.2273L34.9998 26.6137" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '"/>'
        + '<path d="M31.7156 12.6658C31.0037 11.6024 30.19 10.613 29.2884 9.71137C28.3868 8.80978 27.3973 7.99606 26.334 7.28417C25.8048 6.92996 25.2574 6.60096 24.6934 6.29889C24.1327 5.99858 23.5557 5.72488 22.9639 5.47949" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '"/>'
        + '<path d="M5.19397 33.7761C5.84923 34.8753 6.61005 35.906 7.46322 36.8536C8.31639 37.8011 9.26192 38.6655 10.2866 39.4321C10.7964 39.8135 11.3259 40.1707 11.8733 40.5019C12.4175 40.8311 12.9795 41.1346 13.5576 41.4106" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '"/>'
    + '</svg>'
)))
</script>

<style lang="scss" scoped>
@import '../../style/index.css';
</style>
