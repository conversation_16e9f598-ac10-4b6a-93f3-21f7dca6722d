<template>
  <view class="container">
    <scroll-view scroll-y class="content">
      <!-- 基本信息 -->
      <view class="info">
        <view class="info_title">
          <text>基本信息</text>
        </view>
        <view class="form">
          <u--form :model="returnGoodsInfo" :rules="rules" ref="uForm" labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="供应商" borderBottom required>
              <u--input v-model="returnGoodsInfo.supplier_name" border="none" placeholder="请选择供应商" inputAlign="right"
                disabled disabledColor="#fff" @tap="openSelector(0)"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="退货日期" borderBottom>
              <u--input v-model="returnGoodsInfo.return_date" border="none" placeholder="请选择日期" inputAlign="right"
                disabled disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
              <u--input v-model="returnGoodsInfo.order_id" border="none" placeholder="自动生成" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="关联订单" borderBottom>
              <u-row>
                <u-col :span="returnGoodsInfo.purchase_in_code ? 10.5 : 12">
                  <u--input v-model="returnGoodsInfo.purchase_in_code" border="none" placeholder="请选择关联订单"
                    inputAlign="right" disabled disabledColor="#fff" @tap="addGoods(0)"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePurchaseInCode">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="returnGoodsInfo.purchase_in_code" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="仓库" borderBottom required>
              <u-row>
                <u-col :span="returnGoodsInfo.warehouse_name ? 10.5 : 12">
                  <u--input v-model="returnGoodsInfo.warehouse_name" border="none" placeholder="请选择仓库"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openInventorySelection()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeWarehouse">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="returnGoodsInfo.warehouse_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 商品清单 -->
      <view class="info">
        <view class="info_title">
          <text>商品清单</text>
          <view class="add-icon" @click="addGoods(1)">
            <i-add-one theme="outline" size="20" fill="#80b5ee" />
          </view>
        </view>
        <view class="goods-list">
          <view class="goods_item" v-for="(item, index) in isExpandGoodsList
            ? returnGoodsInfo.items.slice(0, 1)
            : returnGoodsInfo.items" :key="index">
            <view class="goods_item_click">
              <view class="goods_img" @click="openProductDetails(item)">
                <image :src="item.image || '/static/img/logo.png'" mode=""></image>
              </view>
              <view class="goods_main" @click="openProductDetails(item)">
                <view class="goods_name">{{ item.item_name }}</view>
                <view class="goods_id">{{ item.code || item.barcode }}</view>
                <view class="goods_extra" v-if="returnGoodsInfo.purchase_in_code">
                  原数量: {{ item.stock_quantity || item.originalQuantity }}　 库存余量:
                  {{ item.stock_remaining || item.remaining_quantity }}
                </view>
                <view class="goods_price">
                  <text class="price">￥{{ item.return_price }}</text>
                  <text class="unit">/{{ item.unit_name }}</text>
                </view>
              </view>
              <view class="goods_qty" style="top: 133rpx;">
                <view v-if="activeQtyIndex === index" @click.stop="decreaseQty(index, item)">
                  <i-left-one theme="filled" size="24" fill="#333" />
                </view>
                <i-close-small theme="outline" size="20" fill="#333" v-if="activeQtyIndex !== index" />
                <u--input class="qty_input" :value="item.quantity" inputAlign="center" @tap="showQtyArrows(index, item)"
                  @change="(val) => changeQuantity(item, val)" />
                <view v-if="activeQtyIndex === index" @click.stop="increaseQty(index, item)">
                  <i-right-one theme="filled" size="24" fill="#333" />
                </view>
              </view>
              <view class="arrow" @click="openProductDetails(item)">
                <i-right theme="outline" size="24" fill="#333" />
              </view>
            </view>
          </view>
        </view>
        <view class="total-info" v-if="isExpandGoodsList">
          <view class="total-row">
            <text>合计</text>
            <u--input v-model="returnGoodsInfo.total_amount" placeholder="请输入合计" inputAlign="right" border="none"
              disabled disabledColor="color: #fff"></u--input>
          </view>
          <view class="total-row">
            <text>优惠率(%)</text>
            <u--input v-model="returnGoodsInfo.discount" placeholder="请输入优惠率" inputAlign="right" border="none"
              @blur="changeDiscount"></u--input>
          </view>
          <view class="total-row">
            <text>退款优惠</text>
            <u--input v-model="returnGoodsInfo.discountRate" placeholder="请输入优惠金额" inputAlign="right" border="none"
              @blur="changeDiscountRate"></u--input>
          </view>
          <view class="total-row">
            <text>优惠后金额</text>
            <u--input v-model="returnGoodsInfo.actual_amount" placeholder="请输入优惠后金额" inputAlign="right"
              border="none"></u--input>
          </view>
        </view>
        <!-- 伸缩按钮 -->
        <view class="telescoping" @click="isExpandGoodsList = !isExpandGoodsList">
          <i-up theme="outline" size="24" fill="#333" v-if="isExpandGoodsList" />
          <i-down theme="outline" size="24" fill="#333" v-else />
        </view>
      </view>

      <!-- 结算账户 -->
      <view class="info">
        <view class="info_title">
          <text>结算账户</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom
              :required="Number(returnGoodsInfo.pay_amount) > 0">
              <u-row>
                <u-col :span="returnGoodsInfo.payment_method_name ? 10.5 : 12">
                  <u--input v-model="returnGoodsInfo.payment_method_name" border="none" placeholder="请选择结算账户"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector(0)"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePayMethod">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="returnGoodsInfo.payment_method_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="本次退款" borderBottom>
              <u--input v-model="returnGoodsInfo.pay_amount" border="none" placeholder="0" inputAlign="right"
                @input="changePayAmount" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="本次欠款" borderBottom>
              <u--input v-model="returnGoodsInfo.debt" border="none" placeholder="0" inputAlign="right"
                @blur="changeDebt"></u--input>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 其他费用 -->
      <view class="info">
        <view class="info_title">
          <text>其他费用</text>
        </view>
        <view class="form">
          <view class="otherExpenses" @click="openOtherCosts">
            <text class="total-row-title">其他费用</text>
            <view class="other-expenses-row">
              <u--input v-model="returnGoodsInfo.allocated_cost.totalAmount" border="none" placeholder="0"
                inputAlign="right" disabled disabledColor="#fff"></u--input>
              <view class="icon-group">
                <view @click.stop="removeOtherCosts" v-if="returnGoodsInfo.allocated_cost.totalAmount">
                  <i-close-one theme="filled" size="20" fill="#d13b3b" />
                </view>
                <view>
                  <i-right theme="filled" size="20" fill="#333" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="returnGoodsInfo.remark" placeholder="请输入备注" autoHeight border="none"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <view>
        <imageUpload />
      </view>

      <!-- 底部操作栏 -->
      <view class="operation" v-if="returnGoodsInfo.return_status !== 'completed'">
        <u-button type="error" text="删除" @click="del" v-if="isEdit"></u-button>
        <u-button type="success" text="暂存" @click="save"></u-button>
        <u-button type="primary" text="提交" @click="savePrompt"></u-button>
      </view>
    </scroll-view>
    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>
    <!-- 交互组件 -->
    <interactive :isShowAddBtn="false" :jumpToId="4" />

    <!-- 审核模态框 -->
    <u-modal :show="isAuditShow" :zoom="isAuditShowZoom" :title="auditTitle" :content="auditContent"
      :closeOnClickOverlay="true" :showCancelButton="true" :showConfirmButton="true" @confirm="save(1)" @cancel="cancel"
      @close="cancel"></u-modal>

    <!-- 选择器、商品详情、其他费用弹窗等可按需添加 -->
    <searchSelector v-model:selectorShow="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectSupplier" @close="closePopup" @supplierScrollToLower="getSupplierList" />

    <!-- 商品详细信息弹出层 -->
    <productDetails :productDetailsShow="productDetailsShow" :type="productDetailType" :productData="productData"
      :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing" @close="closeProductDetails"
      @confirm="handleProductDetails" />

    <!-- 库存弹出层 -->
    <inventorySelection :inventorySelectionShow="inventorySelectionShow" :isSelect="true"
      @close="closeInventorySelection" @save="confirmInventory" />

    <!-- 其他费用弹出层 -->
    <otherExpenses :otherExpensesShow="otherExpensesShow" @close="closeOtherCosts" @confirm="handleExpensesConfirm" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue';
import { onLoad, onReady, onShow } from '@dcloudio/uni-app';
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import searchSelector from "@/components/searchSelector.vue";
import productDetails from "@/components/productDetails.vue";
import inventorySelection from "@/components/inventorySelection/inventorySelection.vue";
import otherExpenses from "@/pages/purchaseGoods/components/otherExpenses.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import { getSupplier } from "@/api/supplier";
import { addReturnGoods, editReturnGoods, deleteReturnGoods } from "@/api/returnGoods";
import { getPurchaseGoodsStock, getPurchaseGoodsDetail } from "@/api/purchaseGoods";
import { getGoodsDetail } from "@/api/goods";

import eventBus from "@/utils/eventBus";

const uForm = ref(null);

const accountSelectShow = ref(false); //结算账户弹出层
const otherExpensesShow = ref(false); //其他费用弹出层
const inventorySelectionShow = ref(false); //仓库选择弹出层显示状态
const productDetailsShow = ref(false); //商品详细信息弹出层显示状态
const productData = ref({});//物品信息
const productDetailType = ref(0);
// 是否展开商品清单
const isExpandGoodsList = ref(false);
// 审核模态框显示状态
const isAuditShow = ref(false);
const isAuditShowZoom = ref(false);
const auditTitle = ref("温馨提示"); // 审核模态框标题
const auditContent = ref("审核后将不可撤回，是否确认保存并审核？"); // 审核模态框内容
const activeQtyIndex = ref(-1); // 当前操作数量的商品索引
const returnGoodsList = ref<any[]>([]); // 退货商品选择列表
// 退货单表单数据
const returnGoodsInfo = reactive<any>({
  is_commit: 0, //是否提交,传1为提交，其他任何值都为暂存
  supplier_name: "", // 供应商名称
  supplier: "", // 供应商ID
  return_date: "", // 退货日期
  purchase_in: "", // 单据编号id
  purchase_in_code: "", // 关联订单编号
  warehouse: "", // 仓库
  warehouse_name: "", //仓库名称
  items: [],
  total_amount: 0, // 合计金额
  discount: 0, // 优惠率
  discountRate: 0, // 优惠金额
  actual_amount: 0, // 实际退款金额/优惠后金额
  other_fee: 0, // 其他费用
  account: "", // 结算账户
  pay_amount: 0, // 本次退款
  debt: 0, // 本次欠款
  remark: "", // 备注
  allocated_cost: "", //其他费用
  allocated_pay_amount: "", //其他费用金额
  payment_method: "", //结算账户
  payment_method_name: "", //结算账户名称
});
// 表单校验规则
const rules = reactive<any>({
  "supplier_name": {
    type: "string",
    required: true,
    message: "请选择供应商",
    trigger: ["blur", "change"],
  },
  "warehouse_name": {
    type: "string",
    required: true,
    message: "请选择仓库",
    trigger: ["blur", "change"],
  },
});
// 选择器相关
const selectorShow = ref(false); // 选择器弹窗显示状态
const selectList = ref<any[]>([]); // 选择器数据列表
const selectType = ref(0); // 选择器类型
const supplierList = ref<any[]>([]); // 供应商列表
const isSupplierMore = ref(true); // 是否还有更多供应商
const supplierPageParams = reactive({
  page: 1,
  page_size: 20,
});
const isQueryWarehouse = ref(false);
const isEdit = ref(false);//是否为编辑状态

//修改输入框输入状态
const showQtyArrows = (index: number, item: any) => {

  if (activeQtyIndex.value === index) {
    activeQtyIndex.value = -1;
    const targetItem = returnGoodsInfo.items.find(
      (infoItem: any) => infoItem.item == item.item && infoItem.unit === item.unit
    );
    if (!targetItem) return;
  } else {
    activeQtyIndex.value = index;
  }
};
const changeQuantity = (item: any, data: string | number) => {
  console.log('changeQuantity被调用，item:', item, 'data:', data);

  // 查找目标元素的索引
  const targetIndex = returnGoodsInfo.items.findIndex(
    (infoItem: any) => infoItem.item === item.item && infoItem.unit === item.unit && infoItem.purchase_order_item === item.purchase_order_item
  );

  if (targetIndex === -1) {
    console.log('未找到targetItem，直接返回');
    return;
  }

  console.log('data值:', data, '类型:', typeof data);

  if (!data || data === "" || Number(data) <= 0 || isNaN(Number(data))) {
    console.log('触发数量限制');
    uni.showToast({
      title: "数量不能小于1",
      icon: "none",
    });

    // 强制更新：先设为临时值，再设为1，确保触发响应式更新
    returnGoodsInfo.items[targetIndex].quantity = 0;
    // Use nextTick if needed for DOM updates, but direct assignment should be reactive
    returnGoodsInfo.items[targetIndex].quantity = 1;
    calcTotalAndDiscount();
  } else {
    returnGoodsInfo.items[targetIndex].quantity = Number(data);
    calcTotalAndDiscount();
  }
};
//减少数量
const decreaseQty = (index: number, item: any) => {
  if (item && item.quantity > 1) {
    returnGoodsInfo.items.forEach((infoItem: any) => {
      if (infoItem.id === item.id) {
        infoItem.quantity = Number(infoItem.quantity) - 1;
      }
    });
    calcTotalAndDiscount();
  } else {
    uni.showToast({
      title: "数量不能小于1",
      icon: "none",
    });
  }
};
//增加数量
const increaseQty = (index: number, item: any) => {
  if (item) {
    returnGoodsInfo.items.forEach((infoItem: any) => {
      if (infoItem.id === item.id) {
        if (Number(infoItem.quantity) == Number(infoItem.remaining_quantity)) {
          uni.showToast({
            title: "退货数量大于在库数量",
            icon: "none",
            mask: true,
          });
          return;
        } else {
          infoItem.quantity = Number(infoItem.quantity || 0) + 1;
        }
      }
    });
    calcTotalAndDiscount();
  }
};

// 1. 计算合计金额
const calcTotalAmount = () => {
  let total = 0;
  if (
    Array.isArray(returnGoodsInfo.items) &&
    returnGoodsInfo.items.length > 0
  ) {
    returnGoodsInfo.items.forEach((item: any) => {
      if (
        item.return_price !== undefined &&
        item.quantity !== undefined
      ) {
        total += Number(item.return_price) * Number(item.quantity);
      }
    });
  }
  returnGoodsInfo.total_amount = total.toFixed(2);
  return total;
};

// 2. 计算优惠金额
const calcDiscountRate = () => {
  const total = Number(returnGoodsInfo.total_amount) || 0;
  const discount = Number(returnGoodsInfo.discount) || 0;
  returnGoodsInfo.discountRate = ((total * discount) / 100).toFixed(2);
};

// 3. 计算优惠率
const calcDiscount = () => {
  const total = Number(returnGoodsInfo.total_amount) || 0;
  const discountRate = Number(returnGoodsInfo.discountRate) || 0;
  if (total > 0) {
    returnGoodsInfo.discount = ((discountRate / total) * 100).toFixed(
      2
    );
  } else {
    returnGoodsInfo.discount = "0";
  }
};

// 4. 计算优惠后金额
const calcActualAmount = () => {
  const total = Number(returnGoodsInfo.total_amount) || 0;
  const discountRate = Number(returnGoodsInfo.discountRate) || 0;
  const actualAmount = total - discountRate;
  returnGoodsInfo.actual_amount = actualAmount.toFixed(2);
  return actualAmount;
};

// 5. 计算本次退款和欠款
const calcPayAndDebt = () => {
  const actualAmount = Number(returnGoodsInfo.actual_amount) || 0;
  let payAmount = Number(returnGoodsInfo.pay_amount);
  if (
    isNaN(payAmount) ||
    !returnGoodsInfo.pay_amount ||
    returnGoodsInfo.pay_amount === "0"
  ) {
    payAmount = actualAmount;
    returnGoodsInfo.pay_amount = actualAmount.toFixed(2);
  }
  const debt = actualAmount - payAmount;
  returnGoodsInfo.debt = debt.toFixed(2);
};

// 总控方法
const calcTotalAndDiscount = () => {
  calcTotalAmount();
  calcActualAmount();
  calcPayAndDebt();
};

// 优惠率输入框失焦
const changeDiscount = () => {
  calcDiscountRate();
  calcTotalAndDiscount();
};

// 优惠金额输入框失焦
const changeDiscountRate = () => {
  calcDiscount();
  calcTotalAndDiscount();
};

// 本次退款输入框失焦
const changePayAmount = (val: string) => {
  const actualAmount = Number(returnGoodsInfo.actual_amount) || 0;
  const payAmount = Number(val) || 0;
  returnGoodsInfo.debt = (actualAmount - payAmount).toFixed(2);
};

// 本次欠款输入框失焦
const changeDebt = () => {
  const actualAmount = Number(returnGoodsInfo.actual_amount) || 0;
  const debt = Number(returnGoodsInfo.debt) || 0;
  returnGoodsInfo.pay_amount = (actualAmount - debt).toFixed(2);
};
// 打开选择器
const openSelector = (type: number) => {
  selectType.value = type;
  selectorShow.value = true;
  if (type === 0) {
    selectList.value = supplierList.value;
    if (!supplierList.value.length) getSupplierList();
  }
};
// 关闭选择搜索器
const closePopup = () => {
  selectorShow.value = false;
};
// 选择供应商
const selectSupplier = (item: any) => {
  if (selectType.value === 0) {
    returnGoodsInfo.supplier_name = item.name;
    returnGoodsInfo.supplier = item.id;
  }
  selectorShow.value = false;
};
// 添加商品（跳转商品选择页面）
const addGoods = (index: number) => {
  /*
    逻辑说明：
    1. 关联订单点击进来（index为0）：
       - 有 purchase_in：
           - 有 warehouse：跳转带参数 purchasingAssociation
           - 无 warehouse：跳转不带参数 purchasingAssociation
       - 无 purchase_in：跳转不带参数 purchasingAssociation
    2. 加号图标点击进来（index不为0）：
       - 有 purchase_in：跳转 productSelection
       - 无 purchase_in：跳转不带参数 purchasingAssociation
  */
  if (index === 0) {
    // 关联订单点击进来
    uni.navigateTo({
      url:
        "/pages/returnGoodsOrder/components/purchasingAssociation?returnGoodsInfo=" +
        JSON.stringify(returnGoodsInfo),
    })
  } else {
    // 加号图标点击进来
    if (returnGoodsInfo.purchase_in) {
      uni.navigateTo({
        url:
          "/components/productSelection?data=" +
          JSON.stringify(returnGoodsList.value) +
          "&type=4",
      });
    } else {
      uni.navigateTo({
        url:
          "/pages/returnGoodsOrder/components/purchasingAssociation?returnGoodsInfo=" +
          JSON.stringify(returnGoodsInfo),
      });
    }
  }
};
// 打开审核模态框
const savePrompt = () => {
  isAuditShow.value = true;
};

//删除退货单
const del = () => {
  deleteReturnGoods(returnGoodsInfo).then((res: any) => {
    if (res.code == 0) {
      uni.showToast({ title: "删除成功", icon: "success" });
      uni.navigateBack({
        delta: 1,
      });
    } else {
      uni.showToast({ title: res.msg, icon: "none" });
    }
  }).catch((err: any) => {
    console.error(err);
    uni.showToast({ title: "请求失败", icon: "none" }); // Assuming REQUEST_ERROR is a global constant or needs to be defined
  });
};
// 保存退货单
const save = (index: number) => {
  if (index == 1) {
    returnGoodsInfo.is_commit = 1;
  }
  if (returnGoodsInfo.supplier == "") {
    uni.showToast({ title: "请选择供应商", icon: "none" });
    return;
  }
  if (returnGoodsInfo.warehouse == "") {
    uni.showToast({ title: "请选择仓库", icon: "none" });
    return;
  }
  console.log(returnGoodsInfo.pay_amount);

  if (
    returnGoodsInfo.pay_amount &&
    returnGoodsInfo.pay_amount !== "" &&
    Number(returnGoodsInfo.pay_amount) > 0
  ) {
    if (!returnGoodsInfo.payment_method) {
      uni.showToast({ title: "请填写结算账户", icon: "none" });
      return;
    }
  }
  if (
    returnGoodsInfo.id
  ) {
    editReturnGoods(returnGoodsInfo)
      .then((res: any) => {
        if (res.code == 0) {
          // 校验通过后执行保存逻辑
          if (returnGoodsInfo.is_commit == 1) {
            uni.showToast({ title: "提交成功", icon: "success" });
          } else {
            uni.showToast({ title: "暂存成功", icon: "success" });
          }
        } else {
          uni.showToast({ title: res.msg, icon: "none" });
        }
        uni.navigateBack({
          delta: 1,
        });
      })
      .catch((err: any) => {
        console.error(err);
        uni.showToast({ title: "请求失败", icon: "none" }); // Assuming REQUEST_ERROR is a global constant or needs to be defined
      });

  } else {
    addReturnGoods(returnGoodsInfo)
      .then((res: any) => {
        if (res.code == 0) {
          // 校验通过后执行保存逻辑
          if (returnGoodsInfo.is_commit == 1) {
            uni.showToast({ title: "提交成功", icon: "success" });
          } else {
            uni.showToast({ title: "暂存成功", icon: "success" });
          }
        } else {
          uni.showToast({ title: res.msg, icon: "none" });
        }
        uni.navigateBack({
          delta: 1,
        });
        cancel();
      })
      .catch((err: any) => {
        console.error(err);
        uni.showToast({ title: "请求失败", icon: "none" }); // Assuming REQUEST_ERROR is a global constant or needs to be defined
      });
  }
};
// 取消审核模态框
const cancel = () => {
  isAuditShow.value = false;
  isAuditShowZoom.value = false;
};
// 打开商品详情弹窗（可自定义）
const openProductDetails = (item: any) => {
  productDetailType.value = 5;
  getGoodsDetail(item.item).then((res: any) => {
    if (res.code == 0) {
      productData.value = { ...item, ...res.data };
      console.log(productData.value);
      productDetailsShow.value = true;
    }
  }).catch((err: any) => {
    console.error('获取商品详情失败:', err);
    uni.showToast({
      title: '获取商品详情失败',
      icon: 'none'
    });
  });
};
const handleProductDetails = (data: any) => {
  returnGoodsInfo.items.forEach((infoItem: any, index: number) => {
    if (infoItem.item === data.item && infoItem.unit === data.unit) {
      returnGoodsInfo.items[index] = data;
    }
  });
  calcTotalAndDiscount();
};
//关闭商品详细弹窗
const closeProductDetails = () => {
  productDetailsShow.value = false;
};

//获取供应商列表
const getSupplierList = () => {
  if (!isSupplierMore.value) return;
  getSupplier(supplierPageParams).then((res: any) => {
    console.log(res);
    if (res.code == 0) {
      supplierList.value = res.data.results;
      if (supplierPageParams.page_size > res.data.results.length) {
        isSupplierMore.value = false;
      } else {
        supplierPageParams.page++;
        isSupplierMore.value = true;
      }
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  });
};

//关闭库存弹出层
const closeInventorySelection = () => {
  inventorySelectionShow.value = false;
};
// 打开库存弹出层
const openInventorySelection = () => {
  inventorySelectionShow.value = true;
};
//接收库存弹出层的数据
const confirmInventory = (data: any) => {
  returnGoodsInfo.warehouse = data.id;
  returnGoodsInfo.warehouse_name = data.name;

  const returnWarehouse = {
    warehouse: data.id,
    warehouse_name: data.name,
  };
  uni.setStorageSync("returnWarehouse", returnWarehouse);
  eventBus.$emit("returnWarehouse", returnWarehouse);
};

// 打开其他费用弹出层
const openOtherCosts = () => {
  otherExpensesShow.value = true;
};
// 关闭其他费用弹出层
const closeOtherCosts = () => {
  otherExpensesShow.value = false;
};
//处理接收其他费用数据
const handleExpensesConfirm = (data: any) => {
  returnGoodsInfo.allocated_cost = data;
  returnGoodsInfo.allocated_pay_amount = data.allocated_pay_amount;
  returnGoodsInfo.allocated_payment_method =
    data.allocated_payment_method;
};

//打开结算账户选择
const openAccountSelector = () => {
  accountSelectShow.value = true;
};
//处理结算账户的数据
const handleAccount = (data: any) => {
  returnGoodsInfo.payment_method = data.id;
  returnGoodsInfo.payment_method_name = data.account_name;
};
//关闭结算账户选择
const closeAccountSelector = () => {
  accountSelectShow.value = false;
};

//删除关联订单
const removePurchaseInCode = () => {
  returnGoodsInfo.purchase_in_code = '';
  returnGoodsInfo.supplier = '';
  returnGoodsInfo.supplier_name = '';
  returnGoodsInfo.items = [];
  removeWarehouse();
};
//删除仓库
const removeWarehouse = () => {
  returnGoodsInfo.warehouse = '';
  returnGoodsInfo.warehouse_name = '';
  returnGoodsInfo.items = [];
  uni.removeStorageSync("returnWarehouse");
};
//删除结算账户
const removePayMethod = () => {
  returnGoodsInfo.payment_method = '';
  returnGoodsInfo.payment_method_name = '';
};
//删除其他费用
const removeOtherCosts = () => {
  returnGoodsInfo.allocated_cost = {};
  returnGoodsInfo.allocated_pay_amount = '';
  returnGoodsInfo.allocated_payment_method = '';
};

watch(() => returnGoodsInfo.discount, (newVal) => {
  if (newVal > 100) {
    uni.showToast({ title: "优惠率不能大于100", icon: "none" });
    returnGoodsInfo.discount = 100;
    return;
  }
  // 只在输入框@blur时联动，watch不自动算
});

watch(() => returnGoodsInfo.items, () => {
  calcTotalAndDiscount();
}, { deep: true });

onReady(() => {
  if (uForm.value) {
    uForm.value.setRules(rules);
  }
});

onMounted(() => {
  eventBus.$on("selectReturnOrder", (data: any) => {
    console.log("接收到选择的订单数据:", data);
    returnGoodsInfo.purchase_in = data.id;
    returnGoodsInfo.purchase_in_code = data.order_id;
    returnGoodsInfo.supplier = data.supplier;
    returnGoodsInfo.supplier_name = data.supplier_name;
    returnGoodsInfo.items = [];
  });
  eventBus.$on("selectReturnOrderList", (data: any) => {
    console.log("接收到选择的订单物品数据:", data);
    returnGoodsList.value = data;

    // console.log("relatedOrdersGoods" + relatedOrdersGoods.value); // relatedOrdersGoods is not defined
  });
  eventBus.$on("selectGoodsList", (data: any) => {
    console.log("接收到选择的商品数据:", data);
    data.forEach((newItem: any) => {
      const existingIndex = returnGoodsInfo.items.findIndex(
        (item: any) => item.id === newItem.id && item.unit === newItem.unit
      );
      if (existingIndex !== -1) {
        returnGoodsInfo.items[existingIndex] = newItem;
      } else {
        returnGoodsInfo.items.push(newItem);
      }
    });
    calcTotalAndDiscount();
  });

  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  returnGoodsInfo.return_date = `${year}-${month}-${day}`;
});

onLoad((options: any) => {
  console.log(options);

  if (options.isEdit) {
    isEdit.value = options.isEdit
  }

  if (options.data) {
    Object.assign(returnGoodsInfo, JSON.parse(options.data));

    getPurchaseGoodsDetail(returnGoodsInfo.purchase_in).then((res: any) => {
      if (res.code == 0) {
        getPurchaseGoodsStock(res.data.id).then((stockRes: any) => {
          const allItems = [...res.data.items, ...stockRes.data];
          const mergedItems = allItems.reduce((acc: any[], item: any) => {
            const existingItem = acc.find(i => i.id === item.id);
            if (existingItem) {
              // 可根据实际需求修改合并逻辑，这里简单示例
              Object.keys(item).forEach(key => {
                if (!existingItem[key]) {
                  existingItem[key] = item[key];
                }
              });
            } else {
              acc.push({ ...item });
            }
            return acc;
          }, []);

          const combinedData = {
            items: mergedItems
          };
          returnGoodsList.value = combinedData.items
        })
      } else {
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    }).catch((err: any) => {
      console.error(err);
      uni.showToast({
        title: "请求失败", // Assuming REQUEST_ERROR is a global constant or needs to be defined
        icon: "none",
      });
    })
    console.log(returnGoodsInfo);
    return;
  }
  isQueryWarehouse.value = true
  uni.removeStorageSync("returnWarehouse");
  getSupplierList();
});

onShow(() => {
  if (isQueryWarehouse.value) {
    const storedWarehouse = uni.getStorageSync("returnWarehouse");
    if (storedWarehouse) {
      returnGoodsInfo.warehouse = storedWarehouse.warehouse;
      returnGoodsInfo.warehouse_name = storedWarehouse.warehouse_name;
    }
  }
});

onBeforeUnmount(() => {
  eventBus.$off("selectReturnOrder");
  eventBus.$off("selectReturnOrderList");
  eventBus.$off("selectGoodsList");
});

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
}

.form {
  padding: 0 20rpx;
  width: 95%;
  margin: 0 auto;
}

.otherExpenses {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;

}

.total-row-title {
  width: 80px;
  font-size: 28rpx;
  margin-left: 30rpx;
  color: #303133;
}

.other-expenses-row {
  display: flex;
  align-items: center;
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.goods-list {}

.goods_item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  position: relative;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 0;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_extra {
  font-size: 12px;
  color: #888;
  margin-bottom: 2px;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}



.qty_x {
  font-size: 15px;
  font-weight: bold;
  margin-right: 2px;
}

.qty_control {
  display: flex;
  align-items: center;
  position: relative;
}

.qty_input {
  width: 40px;
  height: 22px;
  border: 1px solid #222;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
  background: #fff;
  color: #222;
  margin-left: 2px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;
  margin-left: 30rpx;

  .total-row-right {
    display: flex;
    align-items: center;
  }

  text:nth-child(1) {
    width: 80px;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
  }
}

.telescoping {
  display: flex;
  justify-content: center;
  align-items: center;
}

.remark {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.upload-area {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}

::v-deep .u-form-item__body__left__content__required.data-v-5e7216f1 {
  position: absolute;
  left: 0px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}

::v-deep .u-form-item__body__left__content__label.data-v-5e7216f1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  margin-left: 30rpx;
}
</style>
