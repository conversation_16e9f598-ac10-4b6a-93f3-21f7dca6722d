<template>
  <view>
    <u-popup :show="show" @close="close" @open="onOpen">
      <view class="container">
        <view class="header fixed-header">
          <text class="cancel" @click="close">取消</text>
          <text class="popup-title">其他费用</text>
          <text class="confirm" @click="handleConfirm">确定</text>
        </view>
        <view class="popup-body">
          <!-- 分摊方式 -->
          <view class="form-row">
            <text class="form-label required">分摊方式</text>
            <view class="input-flex" @click="chooseTheAllocationMethod">
              <u--input v-model="shareTypeText" border="none" placeholder="请选择" inputAlign="right" disabled
                disabledColor="#fff" style="flex: 1" />
              <u-icon :name="showShareTypeDropdown ? 'arrow-up' : 'arrow-down'" size="20" />
            </view>
          </view>
          <!-- 下拉选择 -->
          <view v-if="showShareTypeDropdown" class="dropdown">
            <view class="dropdown-item" v-for="(item, idx) in shareTypeList" :key="idx" @click="selectShareType(item)">
              {{ item.allocation_name }}
            </view>
          </view>
          <!-- 费用分摊方 -->
          <view class="form-row">
            <text class="form-label required">费用分摊方</text>
            <u--input v-model="allocated_cost.supplier_name" border="none" placeholder="请选择费用分摊方" inputAlign="right"
              disabled disabledColor="#fff" @tap="openSelector(0)"></u--input>
          </view>
          <!-- 结算账户 -->
          <view class="form-row">
            <text :class="['form-label', { 'required': allocated_cost.allocated_pay_amount > 0 }]">结算账户</text>
            <u--input v-model="allocated_cost.allocated_payment_method_name" border="none" placeholder="请选择结算账户"
              inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector" />
          </view>
          <!-- 立即付款 -->
          <view class="form-row">
            <text class="form-label">立即付款</text>
            <u--input v-model="allocated_cost.allocated_pay_amount" border="none" placeholder="请选择付款金额"
              inputAlign="right" :disabled="prohibitModification" disabledColor="#fff" />
          </view>
          <!-- 动态费用项 -->
          <view v-for="(item, idx) in allocated_cost.allocation_details" :key="idx" class="expense-row">
            <view class="expense-row-inner">
              <!-- 删除图标 -->
              <view v-if="allocated_cost.allocation_details.length >= 2" class="delete-icon"
                @click="removeExpense(idx)">
                <i-reduce-one theme="outline" size="20" fill="red" />
              </view>
              <view class="expense-content">
                <view class="form-row">
                  <text class="form-label required">费用名称</text>
                  <u--input v-model="item.name" class="form-input" border="none" placeholder="请输入费用名称"
                    inputAlign="right" disabled dsiabledColor="color: #fff" @tap="openCostNameSelect(idx)" />
                </view>
                <view class="form-row">
                  <text class="form-label required">费用金额</text>
                  <u--input v-model="item.cost" class="form-input" border="none" placeholder="请输入费用金额"
                    inputAlign="right" type="number" :disabled="prohibitModification" disabledColor="#fff" />
                </view>
                <view class="form-row">
                  <text class="form-label">备注</text>
                  <u--input v-model="item.remark" class="form-input" border="none" placeholder="请输入备注"
                    inputAlign="right" :disabled="prohibitModification" disabledColor="#fff" />
                </view>
              </view>
            </view>
          </view>
          <!-- 添加费用信息 -->
          <view class="add-expense" @click="addExpense">
            <u-icon name="plus-circle" color="#1976d2" size="22" />
            <text class="add-text">添加费用信息</text>
          </view>
          <!-- 合计 -->
          <view class="total-row">
            <text>可分摊费用合计：</text>
            <text class="total-amount">{{ allocated_cost.totalAmount }}</text>
          </view>
        </view>
        <view class="popup-footer">
          <u-button type="error" text="清空" @click="remove"></u-button>
        </view>
      </view>
    </u-popup>

    <!-- 费用名称选择器 -->
    <u-picker :show="costNameSelect" :columns="costTypes" @confirm="selectCostType" @close="closeCostNameSelect"
      @cancel="closeCostNameSelect" closeOnClickOverlay></u-picker>

    <!-- 分摊方选择器 -->
    <searchSelector v-model:selectorShow="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectSupplier" @close="closePopup"/>

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import searchSelector from "@/components/searchSelector.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import { getSupplier } from "@/api/supplier";

const props = defineProps({
  otherExpensesShow: Boolean,
  otherExpensesContent: Object,
  otherExpensesPay: Object,
  prohibitModification: Boolean,
});

const emit = defineEmits(['close', 'confirm']);

const accountSelectShow = ref(false); //结算账户弹出层
const show = ref(false); //是否弹出其他费用弹出层
const costNameSelect = ref(false); //费用名称选择器
const costNameIndex = ref(0); //费用名称选择器选中的索引
const shareTypeText = ref("");
const shareTypeList = reactive([
  //分摊类型
  {
    allocation_name: "按金额",
    allocation_method: "amount",
  },
  {
    allocation_name: "按数量",
    allocation_method: "quantity",
  },
]);
const costTypes = reactive([["运费", "装卸费", "其他"]]); //分摊类型列表（符合选择器格式）
// 保留costName映射便于兼容旧代码
const costName = reactive({
  shipping: "运费",
  loading: "装卸费",
  other: "其他",
});
const showShareTypeDropdown = ref(false);
const shareTo = ref("");
const settleAccount = ref("");
const allocated_cost = reactive<any>({
  //分摊金额
  source_type: "purchase_in", //分摊来源（采购入库）
  allocation_method: "", //分摊方式
  supplier: "", //供应商
  supplier_name: "", //供应商名称
  allocated_payment_method: '',//分摊金额支付类型
  allocated_payment_method_name: '',//分摊金额支付类型
  allocated_pay_amount: 0,//分摊金额
  totalAmount: 0,//总计金额
  allocation_details: [
    {
      //分摊明细
      cost: 0, //分摊金额
      type: "", //分摊类型
      name: "", //费用名称
      remark: "", //备注
    },
  ],
});

const selectorShow = ref(false);
const selectType = ref(0); // 0 供应商 1 结算账户 2 仓库 3 关联订单
const selectList = ref<any[]>([]); // 搜索列表

// 弹出层打开时的回调
const onOpen = () => {
  // 目前不需要执行任何操作，因为数据通过 watch 监听器处理
  console.log('其他费用弹出层已打开');
};

// 检查是否禁止修改
const checkProhibitModification = () => {
  if (props.prohibitModification) {
    uni.showToast({
      title: "此订单状态为提交，不能修改",
      icon: "none"
    });
    return true;
  }
  return false;
};

//打开分摊方式的选择
const chooseTheAllocationMethod = () => {
  if (checkProhibitModification()) return;
  showShareTypeDropdown.value = !showShareTypeDropdown.value;
};

//打开结算账户选择
const openAccountSelector = () => {
  if (checkProhibitModification()) return;
  accountSelectShow.value = true;
};
//处理结算账户的数据
const handleAccount = (data: any) => {
  allocated_cost.allocated_payment_method = data.id;
  allocated_cost.allocated_payment_method_name = data.account_name;
};
//关闭结算账户选择
const closeAccountSelector = () => {
  accountSelectShow.value = false;
};
// 费用名称选择器
const selectCostType = (item: any) => {
  allocated_cost.allocation_details[costNameIndex.value].name =
    item.value[0];
  if (item.value[0] == "运费") {
    allocated_cost.allocation_details[costNameIndex.value].type =
      "shipping";
  } else if (item.value[0] == "装卸费") {
    allocated_cost.allocation_details[costNameIndex.value].type =
      "loading";
  } else {
    allocated_cost.allocation_details[costNameIndex.value].type =
      "other";
  }
  closeCostNameSelect();
};
//打开费用名称选择器
const openCostNameSelect = (index: number) => {
  if (checkProhibitModification()) return;
  costNameSelect.value = true;
  costNameIndex.value = index;
};
//关闭费用名称选择器
const closeCostNameSelect = () => {
  costNameSelect.value = false;
};
//关闭其他费用弹出层
const close = () => {
  emit("close");
};
const handleConfirm = () => {
  if (checkProhibitModification()) return;
  const validationRules = [
    {
      condition: allocated_cost.allocation_method === "",
      message: "请选择分摊方式"
    },
    {
      condition: allocated_cost.supplier === "",
      message: "请选择费用分摊方"
    },
    {
      condition: allocated_cost.allocated_payment_method === "",
      message: "请选择结算账户"
    },
    {
      condition: allocated_cost.allocation_details.length === 0,
      message: "请添加费用信息"
    },
    {
      condition: allocated_cost.allocation_details.some((e: any) => e.name === ""),
      message: "请填写费用名称"
    },
    {
      condition: allocated_cost.allocation_details.some((e: any) => e.cost === ""),
      message: "请填写费用金额"
    },
  ];

  for (const rule of validationRules) {
    if (rule.condition) {
      uni.showToast({
        title: rule.message,
        icon: "none",
      });
      return;
    }
  }

  const totalAmount = allocated_cost.allocation_details.reduce(
    (acc: number, curr: any) => acc + Number(curr.cost),
    0
  );
  if (allocated_cost.allocated_pay_amount > totalAmount) {
    uni.showToast({
      title: "分摊金额不能大于可分摊费用合计",
      icon: "none",
    });
    return;
  }
  // 处理allocation_details：移除name字段并转换为JSON字符串
  const processedDetails = allocated_cost.allocation_details.map((item: any) => {
    const { name, ...rest } = item;
    return rest;
  });
  const submitData = {
    ...allocated_cost,
    allocation_details: JSON.stringify(processedDetails)
  };

  // 提交逻辑
  emit("confirm", submitData);
  close();
};
//删除记录
const remove = () => {
  if (checkProhibitModification()) return;
  Object.assign(allocated_cost, {
    //分摊金额
    source_type: "purchase_in", //分摊来源（采购入库）
    allocated_pay_amount: 0, //分摊金额
    allocation_method: "", //分摊方式
    supplier: "", //供应商
    supplier_name: "", //供应商名称
    allocation_details: [
      {
        //分摊明细
        cost: "", //分摊金额
        type: "", //分摊类型
        name: "", //费用名称
        remark: "", //备注
      },
    ],
  });
};
//选择分摊类型
const selectShareType = (item: any) => {
  if (checkProhibitModification()) return;
  shareTypeText.value = item.allocation_name;
  allocated_cost.allocation_method = item.allocation_method;
  showShareTypeDropdown.value = false;
};
//增加分摊详细
const addExpense = () => {
  if (checkProhibitModification()) return;
  allocated_cost.allocation_details.push({
    type: "",
    name: "",
    cost: "",
    remark: "",
  });
};
const removeExpense = (idx: number) => {
  if (checkProhibitModification()) return;
  allocated_cost.allocation_details.splice(idx, 1);
};

// 费用分摊方选择器
const openSelector = (type: number) => {
  if (checkProhibitModification()) return;
  selectType.value = type;
  selectorShow.value = true;
};
// 费用分摊方选择器关闭
const closePopup = () => {
  selectorShow.value = false;
};
//  费用分摊方选择器选择
const selectSupplier = (item: any) => {
  if (selectType.value === 0) {
    allocated_cost.supplier_name = item.name;
    allocated_cost.supplier = item.id;
  }
  selectorShow.value = false;
};

watch(() => props.otherExpensesShow, (val) => {
  show.value = val;
});

watch(() => props.otherExpensesContent, (val) => {
  if (val) {
    val.allocation_details = JSON.parse(val.allocation_details);
    
    // 给每个allocation_details项添加name字段
    val.allocation_details.forEach((item: any) => {
      if (item.type === "shipping") {
        item.name = "运费";
      } else if (item.type === "loading") {
        item.name = "装卸费";
      } else if (item.type === "other") {
        item.name = "其他";
      } else {
        item.name = "";
      }
    });
    
    Object.assign(allocated_cost, val);
    
    if (val.allocation_method === "amount") {
      shareTypeText.value = "按金额";
    } else {
      shareTypeText.value = "按数量";
    }
  }
});

watch(() => props.otherExpensesPay, (val) => {
  if (val) {
    Object.assign(allocated_cost, {
      totalAmount: val.allocated_pay_amount,
      allocated_pay_amount: val.allocated_pay_amount,
      allocated_payment_method: val.allocated_payment_method,
      allocated_payment_method_name: val.allocated_account_name
    });
  }
});

watch(() => allocated_cost.allocation_details, () => {
  // 计算合计
  let sum = 0;
  allocated_cost.allocation_details.forEach((e: any) => {
    const val = Number(e.cost);
    if (!isNaN(val)) sum += val;
  });
  allocated_cost.totalAmount = sum.toFixed(2);
}, { deep: true, immediate: true });
</script>

<style lang="scss" scoped>
.container {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 1000rpx;
  overflow-y: auto;
  position: relative;
}

.fixed-header {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 100;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.header {
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;

  .cancel,
  .confirm {
    font-size: 28rpx;
  }

  .cancel {
    color: #333;
  }

  .confirm {
    color: #5a93e5;
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18rpx 24rpx 0 24rpx;
  font-size: 30rpx;

  .popup-title {
    font-weight: bold;
    font-size: 30rpx;
  }

  .popup-confirm {
    color: #1976d2;
  }
}

.popup-body {
  padding: 0 24rpx 24rpx 24rpx;
}

.form-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18rpx 0;
  border-bottom: 1px solid #eee;
  background: #fff;
}

.form-label {
  min-width: 160rpx;
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
  text-align: left;
}

.form-input {
  flex: 1;
  background: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #222;
  padding: 0 10rpx;
  height: 60rpx;
  text-align: right;
}

.required:after {
  content: "*";
  color: #e22;
  margin-left: 4rpx;
}

.input-flex {
  display: flex;
  align-items: center;
  flex: 1;
}

.dropdown {
  background: #fff;
  border: 1px solid #eee;
  position: absolute;
  top: 75px;
  right: 15px;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  margin-top: -10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  z-index: 99;
}

.dropdown-item {
  padding: 18rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  cursor: pointer;
}

.dropdown-item:hover {
  background: #f5f5f5;
}

.expense-row {
  margin-top: 70rpx;
  border-top: 1px solid #eee;
  padding: 10rpx 0;
}

.expense-row-inner {
  display: flex;
  align-items: flex-start;
}

.delete-icon {
  margin-top: 30rpx; // 垂直居中
  margin-right: 20rpx; // 图标和内容间距
  cursor: pointer;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

.expense-content {
  flex: 1;
}

.add-expense {
  display: flex;
  align-items: center;
  color: #1976d2;
  font-size: 28rpx;
  margin: 18rpx 0 0 0;
  cursor: pointer;
}

.add-text {
  margin-left: 8rpx;
}

.total-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 28rpx;
  margin-top: 10rpx;
  margin-right: 30rpx;
  color: #e22;
}

.popup-footer {
  padding: 18rpx 24rpx;
}
</style>
