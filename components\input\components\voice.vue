<template>
	<view style="height: 100%;">
		<!-- 原有的录音组件 -->
		<view :class="longPress == '1' ? 'record-layer' : 'record-layer1'">
			<view :class="longPress == '1' ? 'record-box' : 'record-box1'">
				<view class="record-btn-layer">
					<button class="record-btn" :class="longPress == '1' ? 'record-btn-1' : 'record-btn-2'"
						@longtap="longpressBtn" @touchend="touchendBtn()" @touchmove="handleTouchMove"
						@touchstart="longpressBtn">
						<text v-if="longPress == '1'" style="color:#6b96c9">{{ VoiceText }}</text>
						<text v-if="longPress == '2' || longPress == '4'">{{ VoiceText_inProgress }}</text>
						<text v-if="longPress == '3'">{{ VoiceText_complete }}</text>
					</button>

				</view>
				<!-- 语音音阶动画 -->
				<view
					:class="VoiceTitle != '松开手指,取消发送' ? 'prompt-layer prompt-layer-1' : 'prompt-layer1 prompt-layer-1'"
					v-if="longPress == '2' || '3' || '4'">
											<view class="prompt-loader" v-if="longPress == '2'">
							<view class="prompt-loader-em" v-for="(item, index) in 15" :key="index"></view>
					</view>
					<textarea class="voice-textarea" style="width: 100%;font-size: 20px;" auto-height
						v-model="resultContent" v-if="istextRecognition"/>
					<text v-if="longPress == '4'">{{ cancelRecordingPrompt }}</text>
					<u-loading-icon style="margin-bottom: 15rpx;" text="加载中" textSize="18" v-if="isIdentify"></u-loading-icon>
				</view>
				<view class="operationButton">
					<view class="btn" v-if="longPress == 3" @click="cancel">
						<view class="cencel"><i-return theme="outline" size="24" fill="#fff" /><text
								style="color: #fff;">取消</text></view>
					</view>
					<view class="btn btn-backcolor submit" v-if="longPress == 3" @click="submit">
						<i-check-small theme="outline" size="30" fill="#347bc6" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue';

// 定义语音消息接口
interface VoiceMessage {
  tempFilePath: string;
  duration: number;
  createTime: number;
}

// 获取 RecorderManager 实例，该实例用于管理音频录制
const recorderManager = uni.getRecorderManager();
// 创建 InnerAudioContext 实例，该实例用于管理音频播放
const innerAudioContext = uni.createInnerAudioContext();

// 定义 Props
const props = defineProps({
  type: {
    type: String,
    default: '',
  },
});

// 定义 Emits
const emit = defineEmits(['sendVoiceText']);

// 响应式数据
const longPress = ref<'1' | '2' | '3' | '4'>('1'); // 用于标识长按操作，可能用于控制录音的开始和结束
const delShow = ref(false); // 控制删除按钮的显示
const time = ref(0);
const duration = ref(60000);
const tempFilePath = ref('');
const startPoint = ref<{ clientX: number; clientY: number }>({ clientX: 0, clientY: 0 });
const sendLock = ref(true);
const VoiceTitle = ref('');
const VoiceText = ref('按住说话');
const VoiceText_inProgress = ref('松手结束录音');
const VoiceText_complete = ref('语音识别完成，请核对！');
const isIdentify = ref(false); // 是否开始识别文字
const istextRecognition = ref(false); // 是否已经识别出文字
const resultContent = ref(''); // 语音识别出来的内容
const cancelRecordingPrompt = ref(''); // 取消录音时候的提示
const types = ref('');
const voiceList = ref<VoiceMessage[]>([]); // 存储语音消息的数组
const currentPlaying = ref(''); // 当前正在播放的语音文件路径
const remainingTime = ref(60); // 剩余时间，初始值为60秒
const debounceTimer = ref<ReturnType<typeof setTimeout> | null>(null); // 防抖定时器
const countdownInterval = ref<ReturnType<typeof setInterval> | null>(null); // 倒计时定时器

// 初始化音频播放器
innerAudioContext.autoplay = false;

// 监听音频播放完成事件
innerAudioContext.onEnded(() => {
  currentPlaying.value = '';
});

// 监听音频播放错误事件
innerAudioContext.onError((res) => {
  console.log(res.errMsg);
  console.log(res.errCode);
  uni.showToast({
    title: '播放失败',
    icon: 'none',
  });
  currentPlaying.value = '';
});

// Watcher for remainingTime
watch(remainingTime, (newVal) => {
  if (newVal <= 10) {
    VoiceText_inProgress.value = `还剩${newVal}秒结束`;
  }
});

// Watcher for props.type
watch(() => props.type, (newVal) => {
  types.value = newVal;
  console.log(newVal);
}, { immediate: true });

// 组件卸载时清除定时器
onUnmounted(() => {
  VoiceText.value = "按住说话";
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value);
  }
  if (debounceTimer.value) {
    clearTimeout(debounceTimer.value);
  }
});

/**
 * 长按录音事件
 * @param {TouchEvent} e - 触摸事件对象
 */
const longpressBtn = (e: TouchEvent) => {
  if (debounceTimer.value) {
    clearTimeout(debounceTimer.value);
    debounceTimer.value = null;
  }

  startPoint.value = e.touches[0] as { clientX: number; clientY: number };
  longPress.value = '2';
  VoiceText.value = '说话中...';
  remainingTime.value = 60;
  isIdentify.value = false;

  if (countdownInterval.value) {
    clearInterval(countdownInterval.value);
  }
  countdownInterval.value = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value -= 1;
    } else {
      recorderManager.stop();
      if (countdownInterval.value) {
        clearInterval(countdownInterval.value);
      }
      touchendBtn();
    }
  }, 1000);

  recorderManager.onStop((res: UniApp.RecorderManagerStopOption) => {
    tempFilePath.value = res.tempFilePath;
    if (countdownInterval.value) {
      clearInterval(countdownInterval.value);
    }
    touchendBtn();
  });

  const options: UniApp.RecorderManagerStartOption = {
    duration: 60000,
    sampleRate: 16000,
    numberOfChannels: 1,
    encodeBitRate: 96000,
    format: 'mp3',
    frameSize: 10,
  };
  recorderManager.start(options);
  sendLock.value = false;
};

/**
 * 长按松开录音事件
 */
const touchendBtn = () => {
  console.log(111);

  longPress.value = '3';
  VoiceText.value = '按住 说话';
  VoiceTitle.value = '松手结束录音';
  isIdentify.value = true;

  debounceTimer.value = setTimeout(() => {
    console.log(222);

    if (countdownInterval.value) {
      clearInterval(countdownInterval.value);
      countdownInterval.value = null;
    }
    console.log(sendLock.value);
    if (sendLock.value) {
      console.log(sendLock.value);
      longPress.value = '1';
      isIdentify.value = false;
    }
    recorderManager.onStop((res: UniApp.RecorderManagerStopOption) => {
      console.log(res);

      if (sendLock.value) {
        // 取消发送
      } else {
        if (res.duration < 1000) {
          longPress.value = '1';
          uni.showToast({
            title: "录音时间太短",
            icon: "none",
            duration: 1000,
          });
        } else {
          tempFilePath.value = res.tempFilePath;
          const duration = Math.round(res.duration / 1000);
          const createTime = new Date().getTime();

          console.log(tempFilePath.value);
          uni.uploadFile({
            url: 'http://192.168.1.55:8000/api/asr', // 替换为你的后端上传接口地址
            filePath: tempFilePath.value,
            name: 'file',
            formData: {
              'uid': '111',
            },
            success: (uploadRes: UniApp.UploadFileSuccessData) => {
              const data = JSON.parse(uploadRes.data);
              console.log('上传成功:', data);
              resultContent.value = data.result.payload_msg.result[0].text;
              istextRecognition.value = true;
              isIdentify.value = false;
            },
            fail: (err: UniApp.GeneralCallbackResult) => {
              console.error('上传失败:', err);
              isIdentify.value = false;
            },
          });
          voiceList.value.unshift({
            tempFilePath: res.tempFilePath,
            duration: duration,
            createTime: createTime,
          });
          console.log(voiceList.value);
        }
      }
    });

    recorderManager.stop();
  }, 2000);
};

/**
 * 触摸移动事件
 * @param {TouchEvent} e - 触摸事件对象
 */
const handleTouchMove = (e: TouchEvent) => {
  const moveLenght = e.touches[e.touches.length - 1].clientY - startPoint.value.clientY;
  if (Math.abs(moveLenght) > 70) {
    longPress.value = '4';
    cancelRecordingPrompt.value = "松开手指,取消发送";
    VoiceText_inProgress.value = '松开手指,取消发送';
    delBtn();
    sendLock.value = true;
  } else {
    longPress.value = '2';
    VoiceText_inProgress.value = "松手结束录音";
    VoiceText_inProgress.value = '松手结束录音';
    sendLock.value = false;
  }
};

/**
 * 删除按钮事件
 */
const delBtn = () => {
  delShow.value = false;
  time.value = 0;
  tempFilePath.value = '';
};

/**
 * 播放语音
 * @param {VoiceMessage} item - 语音消息对象
 */
const playVoice = (item: VoiceMessage) => {
  if (currentPlaying.value === item.tempFilePath) {
    innerAudioContext.stop();
    currentPlaying.value = '';
    return;
  }

  innerAudioContext.stop();

  currentPlaying.value = item.tempFilePath;
  innerAudioContext.src = item.tempFilePath;
  innerAudioContext.play();
};

/**
 * 格式化时间显示
 * @param {number} timestamp - 时间戳
 * @returns {string}
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * 格式化语音时长
 * @param {number} seconds - 秒数
 * @returns {string}
 */
const formatDuration = (seconds: number): string => {
  return `${seconds}"`;
};

/**
 * 取消上传语音识别出来的文字
 */
const cancel = () => {
  longPress.value = '1';
  resultContent.value = '';
};

/**
 * 提交到后端进行ai处理，并跳转至订单页面
 */
const submit = () => {
  longPress.value = '1';
  emit('sendVoiceText', resultContent.value); // 发送语音识别结果给父组件
  resultContent.value = '';

  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const currentPagePath = currentPage.route;

  console.log('当前页面路径:', currentPagePath);
  if (currentPagePath === 'pages/index/index') {
    uni.navigateTo({
      url: '/pages/order/order',
    });
  } else if (currentPagePath === 'pages/order/order') {
    console.log(111);
  }
};
</script>


<style lang="less" scoped>
	.voice-list {
		padding: 20rpx;
	}

	.voice-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f1f1f1;
	}

	.voice-content {
		display: flex;
		align-items: center;
	}

	.voice-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.voice-time {
		color: #999;
		font-size: 24rpx;
	}

	/* 语音录制开始--------------------------------------------------------------------- */
	.record-layer {
		// width: 91vw;
		// padding: 300px 0;
		box-sizing: border-box;
		height: 100%;
		// position: fixed;
		// margin-left: 4vw;
		z-index: 10;
		// bottom: 3vh;
		display: flex;
		align-items: center;
	}

	.record-layer1 {
		width: 100vw;
		// padding: 300px 0;
		box-sizing: border-box;
		height: 100vh;
		position: fixed;
		background-color: rgba(0, 0, 0, .6);
		// padding: 0 4vw;
		z-index: 10;
		bottom: 0vh;
		left: 0;
	}

	.record-box {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.record-box1 {
		width: 100%;
		position: relative;
		bottom: -83vh;
		height: 17vh;
	}

	.record-btn-layer {
		width: 100%;
		height: 100%;
	}

	.record-btn-layer .record-btn::after {
		border: none;
		transition: all 0.1s;
	}

	.record-btn-layer .record-btn {
		font-size: 14px;
		// line-height: 35px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;
		border-radius: 8px;
		text-align: center;
		background: #efefef;
		transition: all 0.1s;
	}

	

	.record-btn-layer .record-btn-1 {
		// background-image: linear-gradient(to right, #f1f1f1 0%, #f1f1f1 100%);
		color: #000000 !important;
	}

	.record-btn-layer .record-btn-2 {
		border-radius: 168rpx 168rpx 0 0;
		height: 17vh;
		line-height: 17vh;
		transition: all 0.3s;
	}

	/* 提示小弹窗 */
	.prompt-layer {
		border-radius: 15px;
		background: #dae8fc;
		padding: 8px 16px;
		box-sizing: border-box;
		position: absolute;
		left: 50%;
		// height: 11vh;
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	.prompt-layer::after {
		content: '';
		display: block;
		border: 12px solid rgba(0, 0, 0, 0);
		border-radius: 10rpx;
		border-top-color: #dae8fc;
		position: absolute;
		bottom: -46rpx;
		left: 50%;
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	//取消动画
	.prompt-layer1 {
		border-radius: 15px;
		background: #FB5353;
		padding: 8px 16px;
		box-sizing: border-box;
		position: absolute;
		left: 50%;
		height: 11vh;
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	.prompt-layer1::after {
		content: '';
		display: block;
		border: 12px solid rgba(0, 0, 0, 0);
		border-radius: 10rpx;
		border-top-color: #FB5353;
		position: absolute;
		bottom: -46rpx;
		left: 50%;
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	.prompt-layer-1 {
		font-size: 12px;
		width: 90%;
		
    height: 60%;

		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		align-content: center;

	}

	.prompt-layer-1 .prompt-layer-p {
		color: #000000;
	}

	.prompt-layer-1 .prompt-layer-span {
		color: rgba(0, 0, 0, .6);
	}

	

	/* 语音音阶------------- */
	.prompt-loader {
		width: 96px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 6px;
	}

	.prompt-loader-em {
		display: block;
		background: #333333;
		width: 1px;
		height: 10%;
		margin-right: 2.5px;
		float: left;
	}

	.prompt-loader .prompt-loader-em:last-child {
		margin-right: 0px;
	}

	.prompt-loader .prompt-loader-em:nth-child(1) {
		animation: load 2.5s 1.4s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(2) {
		animation: load 2.5s 1.2s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(3) {
		animation: load 2.5s 1s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(4) {
		animation: load 2.5s 0.8s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(5) {
		animation: load 2.5s 0.6s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(6) {
		animation: load 2.5s 0.4s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(7) {
		animation: load 2.5s 0.2s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(8) {
		animation: load 2.5s 0s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(9) {
		animation: load 2.5s 0.2s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(10) {
		animation: load 2.5s 0.4s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(11) {
		animation: load 2.5s 0.6s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(12) {
		animation: load 2.5s 0.8s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(13) {
		animation: load 2.5s 1s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(14) {
		animation: load 2.5s 1.2s infinite linear;
	}

	.prompt-loader .prompt-loader-em:nth-child(15) {
		animation: load 2.5s 1.4s infinite linear;
	}

	@keyframes load {
		0% {
			height: 10%;
		}

		50% {
			height: 100%;
		}

		100% {
			height: 10%;
		}
	}

	/* 语音音阶-------------------- */
	.prompt-layer-2 {
		top: -40px;
	}

	.prompt-layer-2 .prompt-layer-text {
		color: rgba(0, 0, 0, 1);
		font-size: 12px;
	}

	/* 语音录制结束---------------------------------------------------------------- */
	.agree {
		width: 50rpx;
		height: 50rpx;
		/* background-color: #000000; */
		border-radius: 50%;

	}

	/* 操作按钮---------------------------------------------------------------- */
	.operationButton {
		display: flex;
		width: 100%;
		height: 200rpx;
		position: absolute;
		top: -270rpx;
		align-items: center;
		justify-content: space-around;
	}

	.btn {
		width: 100rpx;
		height: 100rpx;
		margin-top: 20rpx;
		border-radius: 50%;
	}

	.btn-backcolor {
		background-color: #d8d8d8;
	}

	.cencel {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		align-content: center;
	}

	.submit {
		display: flex;
		align-items: center;
		justify-content: center;
		align-content: center;
	}


	::v-deep .u-button--small {
		// height: 30px ; // 优先级更高，确保覆盖子组件的样式
		min-width: 35px !important;
		border-radius: 50% !important;
		// border-style: solid;
	}
</style>