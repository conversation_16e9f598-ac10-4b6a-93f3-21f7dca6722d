<template>
  <view>
    <u-popup :show="addShow" mode="bottom" @close="closePopup">
      <view class="container">
        <!-- 头部区域 -->
        <view class="header">
          <text class="cancel" @click="closePopup">取消</text>
          <text class="confirm" @click="add">确认</text>
        </view>

        <u-form :model="accountForm" class="form-container">
          <!-- 账户编码 -->
          <u-form-item label="账户编码" :label-width="150" borderBottom>
            <u--input v-model="accountForm.code" placeholder="自动生成" disabled disabledColor="#fff" border="none"
              inputAlign="right" />
          </u-form-item>

          <!-- 账户名称 -->
          <u-form-item label="账户名称" :label-width="150" borderBottom required>
            <u--input v-model="accountForm.account_name" placeholder="请输入账户名称" border="none" inputAlign="right" />
          </u-form-item>

          <!-- 账户类型 -->
          <u-form-item label="账户类型" :label-width="150" borderBottom required @click="openSelect(0)">
            <u--input v-model="accountForm.account_type_name" placeholder="请选择账户类型" disabled disabledColor="#fff"
              border="none" inputAlign="right" />
          </u-form-item>

          <!-- 开户银行 -->
          <u-form-item label="开户银行" :label-width="150" borderBottom v-if="accountForm.account_type == 'bank'">
            <u--input v-model="accountForm.bank_name" placeholder="请输入开户银行" border="none" inputAlign="right" />
          </u-form-item>

          <!-- 银行账号 -->
          <u-form-item label="银行账号" :label-width="150" borderBottom v-if="accountForm.account_type == 'bank'">
            <u--input v-model="accountForm.accountForm.bank_account" placeholder="请输入银行账号" border="none"
              inputAlign="right" />
          </u-form-item>

          <!-- 银行账号全称 -->
          <u-form-item label="银行账号全称" :label-width="150" borderBottom v-if="accountForm.account_type == 'bank'">
            <u--input v-model="accountForm.bank_account_name" placeholder="请输入银行账号全称" border="none"
              inputAlign="right" />
          </u-form-item>

          <!-- 默认结算方式 -->
          <u-form-item label="默认结算方式" :label-width="150" borderBottom required @click="openSelect(1)"
            v-if="accountForm.account_type !== 'other'">
            <u--input v-model="accountForm.default_settlement_name" placeholder="请选择默认结算方式" disabled
              disabledColor="#fff" border="none" inputAlign="right" />
          </u-form-item>

          <!-- 余额 -->
          <u-form-item label="期初余额" :label-width="150" borderBottom>
            <u--input v-model="accountForm.current_balance" type="number" placeholder="请输入期初余额" border="none" inputAlign="right" />
          </u-form-item>
        </u-form>

        <!-- 账户类型 -->
        <view class="select-popup" v-if="selectShow" :style="{ top: isPaymentMethod ? '440rpx' : '355rpx' }">
          <view class="select-option" v-for="item in selectOption" :key="item.id" @click="onSelect(item)">
            {{ item.name }}
          </view>
        </view>
      </view>
    </u-popup>

    <u-modal :show="promptShow" :title="promptTitle" :content='promptContent' :showCancelButton="true"
      :closeOnClickOverlay="true" @confirm="handleConfirm" @cancel="handleCancel" @close="handleCancel"></u-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { addPaymethods } from "@/api/settlementAccount";

interface AccountForm {
  code: string;
  account_name: string;
  account_type: string;
  account_type_name: string;
  bank_name: string;
  bank_account: string;
  bank_account_name: string;
  default_settlement_name: string;
  default_settlement: string;
  current_balance: number;
}

interface SelectOptionItem {
  id: string;
  name: string;
}

const props = defineProps({
  addShow: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close', 'refresh']);

const selectShow = ref(false); // 是否展现选择框
const isPaymentMethod = ref(false); // 是否为结算方式
const accountForm = reactive<AccountForm>({
  code: "", // 账户编码
  account_name: "", // 账户名称
  account_type: "", // 账户类型
  account_type_name: "", // 账户类型名称
  bank_name: "", // 开户银行
  bank_account: "", // 银行账号
  bank_account_name: "", // 银行账号全称
  default_settlement_name: "", // 结算方式名称
  default_settlement: "", // 默认结算方式
  current_balance: 0, // 当前余额
});

const accountTypeArrList: SelectOptionItem[] = [
  { id: "cash", name: "现金" },
  { id: "alipay", name: "支付宝" },
  { id: "wechat", name: "微信" },
  { id: "bank", name: "银行" },
  { id: "other", name: "其他" },
];

const acountTypeFilterList = ref<SelectOptionItem[]>([]);

const paymentMethodList: SelectOptionItem[] = [
  { id: "cash", name: "现金" },
  { id: "alipay", name: "支付宝" },
  { id: "wechat", name: "微信" },
  { id: "credit_card", name: "信用卡" },
  { id: "bank_draft", name: "银行汇票" },
  { id: "bank_note", name: "银行本票" },
  { id: "check", name: "支票" },
];

const paymentMethodFilterList = ref<SelectOptionItem[]>([]);
const selectOption = ref<SelectOptionItem[]>([]); //选择框的选项内容
const promptTitle = ref("温馨提示");
const promptContent = ref("保存后后续起初余额将不允许修改，是否确认保存？");
const promptShow = ref(false);

watch(() => accountForm.account_type, (val) => {
  // 现金
  if (val === 'cash') {
    paymentMethodFilterList.value = paymentMethodList.filter(item => item.id === 'cash');
  }
  // 支付宝
  else if (val === 'alipay') {
    paymentMethodFilterList.value = paymentMethodList.filter(item => item.id === 'alipay');
  }
  // 微信
  else if (val === 'wechat') {
    paymentMethodFilterList.value = paymentMethodList.filter(item => item.id === 'wechat');
  }
  // 银行
  else if (val === 'bank') {
    paymentMethodFilterList.value = paymentMethodList.filter(item => ['credit_card', 'bank_draft', 'bank_note', 'check'].includes(item.id));
  }
  // 其他
  else if (val === 'other') {
    paymentMethodFilterList.value = paymentMethodList;
    acountTypeFilterList.value = accountTypeArrList;
    // 选了其他，清空默认结算方式
    accountForm.default_settlement = '';
    accountForm.default_settlement_name = '';
  }
});

watch(() => accountForm.default_settlement, (val) => {
  // 现金
  if (val === 'cash') {
    acountTypeFilterList.value = accountTypeArrList.filter(item => item.id === 'cash' || item.id === 'other');
  }
  // 支付宝
  else if (val === 'alipay') {
    acountTypeFilterList.value = accountTypeArrList.filter(item => item.id === 'alipay' || item.id === 'other');
  }
  // 微信
  else if (val === 'wechat') {
    acountTypeFilterList.value = accountTypeArrList.filter(item => item.id === 'wechat' || item.id === 'other');
  }
  // 银行
  else if (['credit_card', 'bank_draft', 'bank_note', 'check'].includes(val)) {
    acountTypeFilterList.value = accountTypeArrList.filter(item => item.id === 'bank' || item.id === 'other');
  }
});

onMounted(() => {
  acountTypeFilterList.value = accountTypeArrList;
  paymentMethodFilterList.value = paymentMethodList;
});

// 打开选择框，id=0为账户类型，id=1为结算方式
const openSelect = (id: number) => {
  if (id == 0) {
    // 账户类型
    selectOption.value = acountTypeFilterList.value;
    selectShow.value = !selectShow.value;
    isPaymentMethod.value = false; // 切换为账户类型选择
  } else if (id == 1) {
    // 结算方式
    selectOption.value = paymentMethodFilterList.value;
    selectShow.value = !selectShow.value;
    isPaymentMethod.value = !isPaymentMethod.value; // 切换为结算方式选择
  }
};

// 选择账户类型或结算方式
const onSelect = (item: SelectOptionItem) => {
  // 处理选择
  console.log(item);
  if (isPaymentMethod.value) {
    // 选择结算方式
    accountForm.default_settlement = item.id;
    accountForm.default_settlement_name = item.name;
    selectShow.value = false;
    isPaymentMethod.value = false;
  } else {
    // 选择账户类型
    accountForm.account_type = item.id;
    accountForm.account_type_name = item.name;
    selectShow.value = false;
  }
};

// 关闭弹窗
const closePopup = () => {
  // 关闭按钮逻辑
  emit("close");
  promptShow.value = false;
};

// 新增按钮逻辑
const add = () => {
  if (accountForm.account_name == '') {
    uni.showToast({
      title: '请填写账户名称',
      icon: 'none',
      mask: true
    });
    return;
  }
  if (accountForm.account_type == '') {
    uni.showToast({
      title: '请选择账户类型名称',
      icon: 'none',
      mask: true
    });
    return;
  }
  if (accountForm.default_settlement_name == '' && accountForm.account_type !== 'other') {
    uni.showToast({
      title: '请填写默认结算方式',
      icon: 'none',
      mask: true
    });
    return;
  }
  promptShow.value = true;
};

const handleConfirm = () => {
  if (accountForm.current_balance == '') {
    accountForm.current_balance = 0;
  }
  addPaymethods(accountForm).then((res: any) => {
    if (res.code == 0) {
      uni.showToast({
        title: '新增成功',
        icon: 'success',
        mask: true
      });
      emit('refresh');
      // 清空表单数据
      Object.assign(accountForm, {
        code: "",
        account_name: "",
        account_type: "",
        account_type_name: "",
        bank_name: "",
        bank_account: "",
        bank_account_name: "",
        default_settlement_name: "",
        default_settlement: "",
        current_balance: 0,
      });
      closePopup();
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        mask: true
      });
    }
  }).catch((err: any) => {
    console.error(err);

    uni.showToast({
      title: "请求失败", // Assuming REQUEST_ERROR is a constant or global variable
      icon: 'none',
      mask: true
    });
  });
};

const handleCancel = () => {
  promptShow.value = false;
};

</script>

<style scoped lang="scss">
.container {
  padding: 20rpx;
  background-color: #fff;
  height: 800rpx;
  width: 90%;
    margin: 0 auto;
}

.header {
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
}

.cancel {
  color: #666;
  font-size: 28rpx;
  padding: 0 20rpx;
}

.confirm {
  color: #7fafff;
  font-size: 28rpx;
  padding: 0 20rpx;
}

.title-bar {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.form-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0 20rpx;
}

.icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

.current_balance-tag {
  position: absolute;
  right: 80rpx;
  top: 50%;
  transform: translateY(-50%);
  background-color: #ff4d4f;
  color: #fff;
  padding: 2rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.btn-group {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}

.btn-group u-button {
  width: 45%;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
}

/*
  .select-popup 弹出选择框，动态top值根据isPaymentMethod切换
  .current_balance-tag 余额标签样式
  */
.select-popup {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #ccc;
  width: 290rpx;
  max-height: 300rpx;
  padding: 0;
  overflow: auto;
  position: absolute;
  /* top值根据isPaymentMethod动态切换，见模板部分 */
  top: 355rpx;
  right: 28rpx;
  z-index: 99;
}

.select-option {
  text-align: center;
  font-size: 30rpx;
  color: #222;
  padding: 10rpx 0;
  border-bottom: 1rpx dashed #e0e0e0;
  background: #fff;
  transition: background 0.2s;
}

.select-option:last-child {
  border-bottom: none;
}

.select-option:active {
  background: #f5f7fa;
}
</style>