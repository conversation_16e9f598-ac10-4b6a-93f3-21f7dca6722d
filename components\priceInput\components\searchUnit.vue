<template>
  <view>
    <u--input
      v-model="unitInfo.unit_type_name"
      border="none"
      inputAlign="right"
      placeholder="请选择单位"
      :suffixIcon="unitIcon"
      suffixIconStyle="color:#000"
      disabled
      disabledColor="#fff"
      @tap="expandUnitArea"
      :autoBlur="false"
    ></u--input>
    <view class="unitArea" v-if="isUnitAreaShow">
      <view class="unitArea_search">
        <u-search
          placeholder="搜索关键词"
          v-model="unitKeyword"
          :shape="square"
          :showAction="false"
          @change="searchUnit"
        ></u-search>
      </view>
      <scroll-view
        class="unitArea_content"
        scroll-y="true"
        @scrolltolower="loadMore"
        :style="{ height: filteredUnitList.length === 0 && isFiltered ? 0 : '220rpx' }"
      >
        <view
          class="unitArea_item"
          v-for="(item, index) in isFiltered ? filteredUnitList : unitList"
          :key="index"
          :data-item="item"
          @tap="selectUnit"
        >
          <text>{{ item.name }}</text>
        </view>
      </scroll-view>
      <view class="noResults" v-if="isFiltered && filteredUnitList.length === 0">
        <view class="unitArea_item" @click="addUnit">
          <text
            ><text style="font-size: 20px">+</text> 新增单位{{
              unitKeyword
            }}</text
          >
        </view>
      </view>
      <u-loadmore
        :status="loadStatus"
        :loading-text="loadingText"
        :loadmore-text="loadmoreText"
        :nomore-text="unitList.length > 0 ? nomoreText : noResultText"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue';
import {
  getUnitOfMeasurement,
  addUnitOfMeasurement,
} from "@/api/unitOfMeasurement";

// 定义接口
interface UnitItem {
  id: string;
  name: string;
}

// 定义 Props
const props = defineProps({
  isResetName: {
    type: Boolean,
    default: false,
  },
  isRefresh: {
    type: Boolean,
    default: false,
  },
  editPriceName: {
    type: String,
    default: '',
  },
});

// 定义 Emits
const emit = defineEmits(['closeUnitArea', 'selectedUnit']);

// 响应式数据
const isUnitAreaShow = ref(false); // 单位选择区域是否展示
const unitIcon = ref("arrow-down-fill"); // 单位选择图标
const unitKeyword = ref(""); // 单位搜索关键字
const unitList = ref<UnitItem[]>([]); // 单位列表
const filteredUnitList = ref<UnitItem[]>([]); // 搜索后的单位列表
const isFiltered = ref(false); // 是否展示过滤后的单位列表
const unitInfo = reactive({
  unit_type_name: "",
  unit_type_id: "",
});
const pageParams = reactive({
  page: 1,
  page_size: 300,
});
const loadStatus = ref<"loadmore" | "loading" | "nomore">("loadmore"); // 加载状态
const loadingText = ref("努力加载中..."); // 加载中的提示文字
const loadmoreText = ref("轻轻上拉"); // 加载更多的提示文字
const nomoreText = ref("已经到最底了"); // 没有更多的提示文字
const noResultText = ref("没有找到相关单位"); // 没有找到相关单位的提示文字
const isLoading = ref(false); // 是否正在加载
const hasMore = ref(true); // 是否还有更多数据

// Watchers
watch(() => isUnitAreaShow.value, (newVal) => {
  if (!newVal) {
    emit("closeUnitArea");
  }
});

watch(() => props.isRefresh, (newVal) => {
  if (newVal) {
    unitInfo.unit_type_name = "";
    unitInfo.unit_type_id = "";
    isUnitAreaShow.value = false;
    unitIcon.value = "arrow-down-fill";
    unitKeyword.value = "";
    isFiltered.value = false;
  }
});

watch(() => props.editPriceName, (newVal) => {
  if (newVal) {
    unitInfo.unit_type_name = newVal;
    isUnitAreaShow.value = false;
    unitIcon.value = "arrow-down-fill";
  }
}, { immediate: true });

// 生命周期钩子
onMounted(() => {
  loadUnitList();
  if (props.isResetName) {
    unitInfo.unit_type_name = "";
  }
});

onBeforeUnmount(() => {
  isUnitAreaShow.value = false;
});

// 方法
/**
 * 展开/收起单位选择区域
 */
const expandUnitArea = (): void => {
  isUnitAreaShow.value = !isUnitAreaShow.value;
  if (isUnitAreaShow.value) {
    unitIcon.value = "arrow-up-fill";
  } else {
    unitIcon.value = "arrow-down-fill";
  }
};

/**
 * 选择单位
 * @param e - 事件对象，包含选中的单位信息
 */
const selectUnit = (e: any): void => {
  const item = e.currentTarget.dataset.item;

  unitInfo.unit_type_name = item.name;
  unitInfo.unit_type_id = item.id;
  emit("selectedUnit", unitInfo);
  unitIcon.value = "arrow-down-fill";
  isUnitAreaShow.value = false;
  unitKeyword.value = "";
};

/**
 * 新增单位
 */
const addUnit = async (): Promise<void> => {
  try {
    const res: any = await addUnitOfMeasurement({ name: unitKeyword.value });
    const newUnit: UnitItem = {
      id: res.data.id,
      name: res.data.name || unitKeyword.value,
    };
    unitList.value.unshift(newUnit);

    unitInfo.unit_type_name = newUnit.name;
    unitInfo.unit_type_id = newUnit.id;

    emit("selectedUnit", unitInfo);

    unitKeyword.value = "";
    isFiltered.value = false;
    isUnitAreaShow.value = false;
    unitIcon.value = "arrow-down-fill";

    uni.showToast({
      title: "添加成功",
      icon: "success",
    });
  } catch (error: any) {
    console.error("新增单位失败:", error);
    uni.showToast({
      title: error.msg || "新增单位失败",
      icon: "none",
    });
  }
};

/**
 * 搜索单位
 * @param value - 搜索关键词
 */
const searchUnit = (value: string): void => {
  if (!value) {
    isFiltered.value = false;
    filteredUnitList.value = unitList.value;
  } else {
    const keyword = value.toString().toLowerCase();
    isFiltered.value = true;
    filteredUnitList.value = unitList.value.filter((unit: UnitItem) => {
      return unit.name && unit.name.toLowerCase().includes(keyword);
    });
  }
};

/**
 * 加载单位列表
 */
const loadUnitList = async (): Promise<void> => {
  if (isLoading.value || !hasMore.value) return;

  isLoading.value = true;
  loadStatus.value = "loading";

  try {
    const response: any = await getUnitOfMeasurement(pageParams);
    console.log(response);

    if (pageParams.page === 1) {
      unitList.value = response.data.results;
    } else {
      unitList.value = [...unitList.value, ...response.data.results];
    }
    console.log(unitList.value);

    if (response.data.results.length < pageParams.page_size) {
      hasMore.value = false;
    }
    loadStatus.value = hasMore.value ? "loadmore" : "nomore";

    pageParams.page++;
  } catch (error) {
    console.error("加载单位列表失败:", error);
    loadStatus.value = "loadmore";
  } finally {
    isLoading.value = false;
  }
};

/**
 * 触底加载更多
 */
const loadMore = (): void => {
  if (hasMore.value && !isLoading.value) {
    loadUnitList();
  }
};
</script>

<style lang="scss" scoped>
.unitArea {
  max-height: 270rpx;
  width: 300rpx;
  background-color: #fff;
  border: 1px solid #cacaca;
  border-radius: 20rpx;
  overflow: auto;
  position: fixed;
  z-index: 999;
  right: 20rpx;

  .unitArea_search {
    position: sticky;
    top: 0;
    width: 90%;
    border-bottom: 1px solid #cacaca;
    padding: 10rpx;
    background-color: #fff;
    z-index: 999;
  }

  .unitArea_item {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
    border-bottom: 1px solid #cacaca;
    font-size: 25rpx;
    font-weight: 700;
    cursor: pointer; // 添加指针样式
    position: relative; // 确保层级
    z-index: 1; // 确保在最上层
    pointer-events: auto; // 确保可点击
    background-color: #f0f0f0; // 临时背景色，便于调试
  }

  .noResults {
    width: 100%;
    height: 50rpx;
    line-height: 50rpx;
    display: flex;
    justify-content: center;
  }

  .unitArea_content {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .unitArea_item {
    background-color: #fff;
    border-radius: 4rpx;
    font-size: 28rpx;
    color: #333;

    &:active {
      background-color: #e0e0e0;
    }
  }
}
</style>