<template>
    <view>
        <!-- 日期选择器 -->
        <u-datetime-picker v-model="expectedDeliveryDateRaw" :show="showDatePicker" mode="date" @close="close"
            @cancel="close" @confirm="handleDateConfirm"></u-datetime-picker>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 定义 Props
const props = defineProps({
  showDatePicker: {
    type: Boolean,
    default: false
  }
});

// 定义 Emits
const emit = defineEmits(['confirm', 'close', 'update:showDatePicker']);

// 响应式数据
const expectedDeliveryDateRaw = ref<number>(Number(new Date())); // 预计到货日期原始值（时间戳或原始字符串）
const date = ref<string>('');

/**
 * 处理日期确认
 * @param e - 事件对象，包含选中的日期值
 */
const handleDateConfirm = (e: any): void => {
  // 兼容 uview2/uview3
  const val = e.value || e; // 有的版本直接是字符串，有的是对象
  expectedDeliveryDateRaw.value = val;
  date.value = formatDate(val);
  console.log(date.value);

  emit('confirm', date.value);
  emit('update:showDatePicker', false); // 通知父组件关闭日期选择器
};

/**
 * 格式化日期
 * @param val - 日期值
 * @returns 格式化后的日期字符串
 */
const formatDate = (val: any): string => {
  if (!val) return "";
  let dateObj: Date;
  if (typeof val === "string") {
    // 2024-07-05
    if (val.includes("-")) {
      const arr = val.split("-");
      if (arr.length === 3) {
        return `${arr[0]}-${arr[1]}-${arr[2]}`;
      }
      return val;
    }
    // 可能是时间戳字符串
    dateObj = new Date(Number(val));
  } else if (typeof val === "number") {
    dateObj = new Date(val);
  } else if (val instanceof Date) {
    dateObj = val;
  } else {
    return ""; // 无法识别的日期类型
  }

  if (dateObj) {
    const y = dateObj.getFullYear();
    const m = String(dateObj.getMonth() + 1).padStart(2, '0'); // 补零
    const d = String(dateObj.getDate()).padStart(2, '0'); // 补零
    return `${y}-${m}-${d}`;
  }
  return "";
};

/**
 * 关闭日期选择器
 */
const close = (): void => {
  emit("close");
  emit('update:showDatePicker', false); // 通知父组件关闭日期选择器
};
</script>

<style lang="scss" scoped></style>