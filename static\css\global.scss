  .noborder{
    border: none;
  }

  .blueFont{
    color: #368be5;
  }
  
  .redFont{
    color: #e25245;
  }
  .popup_title{
    font-size: 30rpx;
    font-weight: bold;
    color: #000000;
  }

  .prompt_font{
    font-size: 20rpx;
    color: #cccccf;
  }

  .attribute_font{
    font-size: 24rpx;
    color: #333333;
  }

  // 功能页标题样式
  .info {
    width: 90%;
    margin: 10rpx auto;
    padding: 10rpx 20rpx;
    background-color: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }
    
  .info_title {
    font-size: 30rpx;
    font-weight: 700;
    color: #0f40f5;
    padding: 20rpx;
    border-bottom: 1px solid #cacaca;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .price_title {
    display: flex;
    align-items: flex-start;
  }

  .info_title text {
    border-left: 3px solid #0f40f5;
    padding-left: 10rpx;
  }

  //标签样式
  .orange-tag {
    position: absolute;
    top: 12rpx;
    right: 140rpx;
    /* padding: 0 -1rpx; */
    height: 35rpx;
    line-height: 35rpx;
    border: 2rpx solid #e99129;
    color: #e99129;
    border-radius: 8rpx;
    font-size: 22rpx;
    background: #fff;
    font-weight: 500;
    box-sizing: border-box;
    text-align: center;
    width: 110rpx;
  }
  
  .red-tag {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    height: 35rpx;
    line-height: 35rpx;
    border: 2rpx solid #ff4d4f;
    color: #ff4d4f;
    border-radius: 8rpx;
    font-size: 22rpx;
    background: #fff;
    font-weight: 500;
    box-sizing: border-box;
    text-align: center;
    width: 110rpx;
  }

  // 订单快捷加减数量
.goods_qty {
  display: flex;
  align-items: center;
  position: absolute;
  top: 160rpx;
  right: 160rpx;
  width: 140rpx;
}