<template>
  <view class="basic-info">
    <view class="info">
      <view class="info_title">
        <text>基本信息</text>
      </view>
      <view class="form">
        <u--form labelPosition="left" ref="form" :model="localInfo" :rules="rules">
          <!-- 采购单 -->
          <template v-if="type === 'purchase' || type === 'purchaseGoods'">
            <u-form-item :label-width="'180rpx'" prop="supplier_name" label="供应商" borderBottom required>
              <u--input v-model="localInfo.supplier_name" border="none" placeholder="请选择供应商" inputAlign="right" disabled
                disabledColor="#fff" @tap="openSelector(0)"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据日期" borderBottom>
              <u--input :value="localInfo.in_date || localInfo.expected_arrival_date" border="none" placeholder="请选择日期" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
              <u--input v-model="localInfo.order_id" border="none" placeholder="自动生成" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="预计到货日期" borderBottom required v-if="type == 'purchase'">
              <u--input v-model="localInfo.expected_arrival_date" border="none" placeholder="请选择到货日期" inputAlign="right"
                disabled disabledColor="#fff" @tap="openDatePicker"></u--input>
            </u-form-item>

            <!-- 进货单 -->
            <u-form-item :label-width="'180rpx'" label="关联订单" borderBottom v-if="type == 'purchaseGoods'">
              <u-row>
                <u-col :span="localInfo.purchase_order_name && !isDisabled ? 10.5 : 12">
                  <u--input v-model="localInfo.purchase_order_name" border="none" placeholder="请选择关联单"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openRelatedOrders"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePurchaseOrder">
                    <i-close-one theme="filled" size="20" fill="#d13b3b"
                      v-if="localInfo.purchase_order_name && !isDisabled" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" prop="warehouse_name" label="仓库" borderBottom required
              v-if="type == 'purchaseGoods'">
              <u-row>
                <u-col :span="localInfo.warehouse_name && !isDisabled ? 10.5 : 12">
                  <u--input v-model="localInfo.warehouse_name" border="none" placeholder="请选择仓库" inputAlign="right"
                    disabled disabledColor="#fff" @tap="openInventorySelection"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeWarehouse">
                    <i-close-one theme="filled" size="20" fill="#d13b3b"
                      v-if="localInfo.warehouse_name && !isDisabled" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
          </template>

          <!-- 零售订单 -->
          <template v-if="type === 'retail'">
            <u-form-item :label-width="'180rpx'" label="客户" prop="customer_name" borderBottom required>
              <u--input v-model="localInfo.customer_name" border="none" placeholder="请选择客户" inputAlign="right"
                disabled disabledColor="#fff" @tap="openSelector(1)">
              </u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据日期" borderBottom>
              <u--input v-model="localInfo.out_date" border="none" placeholder="请选择日期" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
              <u--input v-model="localInfo.order_no" border="none" placeholder="提交后自动生成" inputAlign="right"
                disabled disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="仓库" prop="warehouse_name" borderBottom required>
              <u-row>
                <u-col :span="localInfo.warehouse_name ? 10.5 : 12">
                  <u--input v-model="localInfo.warehouse_name" border="none" placeholder="请选择仓库"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openInventorySelection()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeWarehouse">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="localInfo.warehouse_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
          </template>

          <!-- 商品信息 -->
          <template v-if="type === 'product'">
            <u-form-item :label-width="'180rpx'" prop="name" label="商品名称" borderBottom required>
              <u--input v-model="localInfo.name" border="none" placeholder="请输入商品名称" inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="序列号" borderBottom>
              <u--input v-model="localInfo.serial_number" border="none" placeholder="请输入序列号"
                inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="商品编号" borderBottom>
              <u--input v-model="localInfo.code" border="none" placeholder="请输入商品编号" inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" prop="item_type_name" label="商品类别" borderBottom
              v-if="isExpandProductInfo" required>
              <u--input v-model="localInfo.item_type_name" border="none" placeholder="请选择商品类别" inputAlign="right"
                disabled disabledColor="#fff" @tap="openProductCategory"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="品牌" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.brand" border="none" placeholder="请输入品牌" inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="型号" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.model" border="none" placeholder="请输入型号" inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="规格" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.specification" border="none" placeholder="请输入规格"
                inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="颜色" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.color" border="none" placeholder="请输入颜色" inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="制造商" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.manufacturer" border="none" placeholder="请输入制造商"
                inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'220rpx'" label="基础重量(kg)" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.base_weight" border="none" placeholder="请输入基础重量" inputAlign="right"
                type="digit"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="仓位货架" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.location" border="none" placeholder="请输入仓位货架" inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="保质期(天)" borderBottom v-if="isExpandProductInfo">
              <u--input v-model="localInfo.expiry_days" border="none" placeholder="请输入有效期" inputAlign="right"
                type="digit"></u--input>
            </u-form-item>

            <view class="expandBtn" @click="isExpandProductInfo = !isExpandProductInfo">
              <view class="expand_text" v-if="!isExpandProductInfo">
                <text>展开</text>
                <i-down theme="outline" size="24" fill="#333" />
              </view>
              <view class="expand_text" v-if="isExpandProductInfo">
                <i-up theme="outline" size="24" fill="#333" />
              </view>
            </view>
          </template>
        </u--form>
      </view>
    </view>
    <!-- 供应商选择弹窗 -->
    <searchSelector :selectorShow.sync="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectPartyA" @close="closePopup" @newSupplier="newSupplier"
      @supplierScrollToLower="getSupplierList" />
    <!-- 日期选择器 -->
    <datePickers :showDatePicker="showDatePicker" @close="closeDatePicker" @confirm="handleDateConfirm" />
    <!-- 商品类别弹出层 -->
    <category :CategoryShow="CategoryShow" :queryCategoryName="'item'" @update:CategoryShow="updateProductCategoryShow"
      @update:productCategory="updateProductCategory" />

    <!-- 库存弹出层 -->
    <inventorySelection :inventorySelectionShow="inventorySelectionShow" :isSelect="true"
      @close="closeInventorySelection" @save="confirmInventory" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />

    <!-- 其他费用弹出层 -->
    <otherExpenses :otherExpensesShow="otherExpensesShow" :otherExpensesContent="localInfo.allocated_cost"
      :otherExpensesPay="otherExpensesPay" :prohibitModification="isDisabled" @close="closeOtherCosts"
      @confirm="handleExpensesConfirm" />
  </view>
</template>

<script>
import searchSelector from '@/components/searchSelector.vue';
import datePickers from '@/components/datePickers.vue';
import category from '@/components/category/category.vue';
import inventorySelection from '@/components/inventorySelection/inventorySelection.vue';
import settlementAccount from '@/components/settlementAccount/settlementAccount.vue';
import otherExpenses from '@/pages/purchaseGoods/components/otherExpenses.vue';
import { getSupplier } from '@/api/supplier';
import dataFn from './data/basicInfo.data';
import methodsObj from './method/basicInfo.methods';
import productMethodsObj from './method/productBasicInfo.methods';
import purchaseMethodsObj from './method/purchaseBasicInfo.methods';
import retailMethodsObj from './method/retailBasicInfo.methods';
export default {
  name: 'BasicInfo',
  components: { searchSelector, datePickers, category, inventorySelection, settlementAccount, otherExpenses },
  props: {
    basicInfo: {
      type: Object,
      required: true
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'purchase' // 默认为采购类型
    }
  },
  data() {
    return {
      ...dataFn.call(this),
      // 采购相关数据
      otherExpensesContent: [], // 其他费用内容列表
      accountList: [], // 结算账户列表
      inventoryList: [], // 仓库列表
    };
  },
  mounted() {
    console.log(this.localInfo);
    
  },
  onReady() {
    //onReady 为uni-app支持的生命周期之一
    this.$refs.form.setRules(this.rules)
  },
  methods: {
    ...methodsObj,
    ...productMethodsObj,
    ...purchaseMethodsObj,
    ...retailMethodsObj,

    // 表单验证方法
    validateForm() {
      console.log(111);
      return new Promise((resolve) => {
        if (this.$refs.form) {
          this.$refs.form.validate().then(res => {
            resolve(true);
          }).catch(err => {
            console.log('err', err);
            resolve(false);
          })
        } else {
          resolve(true);
        }
      })
    },

    // 采购相关方法
    openRelatedOrders() {
      // 打开关联订单选择器
      if (this.isDisabled) return;
      // 这里需要实现关联订单选择逻辑
    },

    removePurchaseOrder() {
      // 移除关联订单
      if (this.isDisabled) return;
      this.localInfo.order_no = '';
    },

    removePayAccount() {
      // 移除结算账户
      if (this.isDisabled) return;
      this.localInfo.settlement_account = '';
    },

    removeOtherExpenses() {
      // 移除其他费用
      if (this.isDisabled) return;
      this.localInfo.other_expenses = '';
    }
  },
  watch: {
    basicInfo: {
      handler(newVal) {
        this.localInfo = JSON.parse(JSON.stringify(newVal));
      },
      deep: true
    },
    localInfo: {
      handler(newVal) {
        this.$emit('update:basicInfo', newVal);
      },
      deep: true
    }
  },
  mounted() {
    this.getSupplierList();
  }
}
</script>

<style lang="scss" scoped>
.basic-info {
  width: 100%;
}

.info {
  background-color: #fff;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
}

.form {
  padding: 0 20rpx;
}

.expandBtn {
  width: 100%;

  .expand_text {
    width: 100%;
    font-size: 25rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10rpx;
  }
}

.expand_text {
  width: 100%;
  font-size: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10rpx;
}
</style>