<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="2" @search-result="handleSearchResult" />

    <!-- 退货订单列表 -->
    <scroll-view scroll-y class="purchase-list" :style="{ height: scrollViewHeight }" @scrolltolower="onScrollToLower">
      <view v-for="(item, index) in returnGoodsList" :key="index" @tap="selectPurchaseGoodsOrder(item)"
        :class="{ 'purchase-item-disabled': !item.isWarehouse && !item.isSupplier }">
        <view class="purchase-item attribute_font" v-if="item.in_status == 'completed' && item.isWarehouse && item.isSupplier">
          <!-- 已审核标签 -->
          <view class="status-order-tag" v-if="item.in_status != 'cancelled'">
            {{
              item.in_status == "draft"
                ? "暂存"
                : item.in_status == "completed"
                  ? "已提交"
                  : ""
            }}
          </view>
          <view class="status-payment-tag">
            {{ getInTypeText(item.in_type) }}
          </view>
          <!-- 右侧添加按钮 -->
          <view class="item-content-container">
            <view class="item-content">
              <view class="info-row">
                <text class="label">供应商</text>
                <text class="value">：{{ item.supplier_name }}</text>
              </view>
              <view class="info-row">
                <text class="label">商品</text>
                <text class="value">：{{ item.short_desc }}</text>
              </view>
              <view class="info-row">
                <view class="info-row-item">
                  <text class="label">操作员</text>
                  <text class="value">：{{ item.handler_name || `操作员#${item.handler}` }}</text>
                </view>
                <view>
                  <text class="sub-label">数量</text>
                  <text class="value">：{{ item.items_count }}</text>
                </view>
              </view>
              <view class="info-row">
                <view class="info-row-item">
                  <text class="label">金额合计</text>
                  <text class="value">：{{ item.actual_total_cost }}</text>
                </view>
              </view>
            </view>
            <view class="add-btn" @click.stop="selectPurchaseGoodsOrder(item)">
              <i-add-one theme="outline" size="20" fill="#378ce5" v-if="!item.selected" />
            </view>
          </view>
          <view class="info-bottom">
            <view class="info-row" style="margin-bottom: 10rpx">
              <text class="label">单据日期</text>
              <text class="value">：{{ item.in_date }}</text>
            </view>
            <view class="info-row" style="margin-bottom: 0">
              <text class="label">单据编号</text>
              <text class="value">：{{ item.order_id }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载更多 -->
      <u-loadmore :status="loadStatus" :loading-text="'加载中...'" :loadmore-text="'上拉加载更多'" :nomore-text="'没有更多数据了'" />
    </scroll-view>

    <!-- 购物车遮罩层 -->
    <view v-if="shoppingCartShow" class="cart_mask" @click="expandShoppingCart"></view>

    <!-- 购物车面板 -->
    <view v-if="shoppingCartShow" class="cart_popup" :animation="cartAnimation">
      <view class="cart_container">
        <view class="categoryConfirmationBtn">
          <view>已选商品</view>
          <view class="blueFont" @click="clearSelectGoodsList">清空</view>
        </view>
        <view class="divider"></view>
        <view class="goods">
          <view class="goods_item" v-for="(item, index) in selectGoodsList" :key="index">
            <view class="goods_left">
              <image class="goods_img" :src="item.imgurl || '/static/img/logo.png'" mode=""></image>
              <view class="goods_info">
                <view class="item_name">{{ item.item_name }}</view>
                <view class="goods_code">{{ item.order_id }}</view>
                <view class="goods_price">
                  <text class="price">￥{{ item.price || '未填写' }}</text>
                  <text class="unit">/个</text>
                </view>
              </view>
            </view>
            <view class="goods_num">
              <view class="goods_num_reduce" @click.stop="reduceNum(item)">
                <i-reduce-one theme="outline" size="20" fill="#3894ff" />
              </view>
              <view class="goods_num_input">{{ item.quantity || 0 }}</view>
              <view class="goods_num_add" @click.stop="addNum(item)">
                <i-add-one theme="filled" size="20" fill="#3894ff" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <Input style="height: 100%" />
    </view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="false" :jumpToId="3" />


  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import { getPurchaseGoods, getPurchaseGoodsDetail, getPurchaseGoodsStock } from "@/api/purchaseGoods";
import eventBus from "@/utils/eventBus";

interface PurchaseGoodsItem {
  id: number;
  in_status: string;
  isWarehouse: boolean;
  isSupplier: boolean;
  supplier_name: string;
  short_desc: string;
  handler_name: string;
  handler: number;
  items_count: number;
  actual_total_cost: number;
  in_date: string;
  order_id: string;
  in_type: string;
  selected?: boolean;
  warehouse?: number;
  supplier?: number;
  imgurl?: string;
  item_name?: string;
  price?: number;
  quantity?: number;
  item?: string;
}

interface SelectGoodsItem {
  imgurl?: string;
  item_name: string;
  order_id: string;
  price: number | string;
  quantity: number;
}

// 响应式数据
const returnGoodsList = ref<PurchaseGoodsItem[]>([]);
const shoppingCartShow = ref(false);
const cartAnimation = ref<any>(null);
const selectGoodsList = ref<SelectGoodsItem[]>([]);
const productDetailsShow = ref("");
const productData = ref<PurchaseGoodsItem>({} as PurchaseGoodsItem);
const returnGoodsListParams = reactive({
  page: 1,
  page_size: 10,
});
const hasMore = ref(true);
const loading = ref(false);
const scrollViewHeight = ref("500px");
const loadStatus = ref<"loadmore" | "loading" | "nomore">("loadmore");
const warehouse = ref<number | null>(null);
const supplier = ref<number | null>(null);

// 生命周期钩子
onLoad((options: any) => {
  if (options.returnGoodsInfo) {
    const data = JSON.parse(options.returnGoodsInfo);
    warehouse.value = data.warehouse;
    supplier.value = data.supplier;
  }
});

onMounted(() => {
  initScrollViewHeight();
  getPurchaseGoodsList();
});

// 方法
/**
 * 获取进货单列表
 * @param isLoadMore - 是否为加载更多
 */
const getPurchaseGoodsList = async (isLoadMore = false) => {
  if (loading.value || (!hasMore.value && isLoadMore)) return;
  loading.value = true;
  loadStatus.value = "loading";
  try {
    const res: any = await getPurchaseGoods(returnGoodsListParams);
    if (res.code === 0) {
      const results = res.data.results || [];
      const newResults = results.map((item: PurchaseGoodsItem) => ({
        ...item,
        isWarehouse: true,
        isSupplier: true
      }));
      if (isLoadMore) {
        returnGoodsList.value = returnGoodsList.value.concat(newResults);
      } else {
        returnGoodsList.value = newResults;
      }
      filterItemsForWarehouse();
      if (returnGoodsList.value.length < (res.data.count || 0)) {
        hasMore.value = true;
        loadStatus.value = "loadmore";
        returnGoodsListParams.page++;
      } else {
        hasMore.value = false;
        loadStatus.value = "nomore";
      }
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
      loadStatus.value = "loadmore";
    }
  } catch (err) {
    console.error('获取进货单列表失败:', err);
    uni.showToast({
      title: "请求失败",
      icon: "none",
    });
    loadStatus.value = "loadmore";
  } finally {
    loading.value = false;
  }
};

/**
 * 获取入库类型文本
 * @param type - 入库类型
 * @returns 入库类型文本
 */
const getInTypeText = (type: string): string => {
  const inTypeMap: { [key: string]: string } = {
    purchase_in: "采购入库",
    return_in: "退货入库",
    adjustment_in: "调拨入库",
    initial_in: "期初入库",
    assembly_in: "组装入库",
    other_in: "其他入库",
    unpaid: "未付款",
  };
  return inTypeMap[type] || type;
};

/**
 * 处理搜索结果
 * @param newVal - 新的搜索结果
 */
const handleSearchResult = (newVal: any) => {
  if (newVal === null || newVal === undefined) {
    resetData();
  } else if (Array.isArray(newVal)) {
    const convertedData = newVal.map((item: any) => ({
      ...item,
      in_status: item.in_status,
      in_type: item.in_type,
      in_date: item.in_date,
      items_count: item.items_count,
      actual_total_cost: item.actual_total_cost,
      warehouse: item.warehouse || null,
      supplier: item.supplier,
      handler_name: `操作员#${item.handler}`,
      isWarehouse: true,
      isSupplier: true
    }));
    returnGoodsList.value = convertedData;
    filterItemsForWarehouse();
    hasMore.value = false;
    loadStatus.value = 'nomore';
    const visibleItems = returnGoodsList.value.filter(item =>
      item.in_status === 'completed' && item.isWarehouse && item.isSupplier
    );
    if (visibleItems.length === 0) {
      if (!warehouse.value && !supplier.value) {
        uni.showToast({
          title: '未找到匹配的采购订单',
          icon: 'none'
        });
      } else {
        uni.showToast({
          title: '未找到匹配的采购订单或仓库/供应商不匹配',
          icon: 'none'
        });
      }
    } else {
      uni.showToast({
        title: `搜索到${visibleItems.length}条结果`,
        icon: 'none'
      });
    }
  } else if (newVal && typeof newVal === 'object') {
    if (newVal.results && Array.isArray(newVal.results)) {
      const convertedData = newVal.results.map((item: any) => ({
        ...item,
        in_status: 'completed',
        in_type: 'purchase_in',
        in_date: item.order_date,
        items_count: item.item_count,
        actual_total_cost: item.total_amount,
        warehouse: item.warehouse || null,
        supplier: item.supplier,
        handler_name: `操作员#${item.handler}`,
        isWarehouse: true,
        isSupplier: true
      }));
      returnGoodsList.value = convertedData;
      filterItemsForWarehouse();
    } else {
      console.warn('未知的搜索结果格式:', newVal);
      returnGoodsList.value = [];
    }
    hasMore.value = false;
    loadStatus.value = 'nomore';
  } else {
    resetData();
  }
};

/**
 * 重置数据
 */
const resetData = () => {
  returnGoodsListParams.page = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  getPurchaseGoodsList();
};

/**
 * 选择采购商品订单
 * @param item - 采购商品订单项
 */
const selectPurchaseGoodsOrder = async (item: PurchaseGoodsItem) => {
  if (!item.isWarehouse) {
    uni.showToast({
      title: '选择的仓库与当前物品仓库不一致',
      icon: 'none'
    });
    return;
  }
  try {
    const res: any = await getPurchaseGoodsDetail(item.id);
    eventBus.emit('selectReturnOrder', res.data);
    if (res.code === 0) {
      const stockRes: any = await getPurchaseGoodsStock(item.id);
      const allItems = [...res.data.items, ...stockRes.data];
      const mergedItems = allItems.reduce((acc: any[], currentItem: any) => {
        const existingItemIndex = acc.findIndex(i => String(i.item) === String(currentItem.item));
        if (existingItemIndex !== -1) {
          acc[existingItemIndex] = {
            ...acc[existingItemIndex],
            ...currentItem
          };
        } else {
          acc.push({ ...currentItem });
        }
        return acc;
      }, []);
      const combinedData = {
        items: mergedItems
      };
      eventBus.emit('selectReturnOrderList', combinedData.items);
      uni.navigateTo({
        url:
          "/components/productSelection?data=" +
          JSON.stringify(combinedData.items) + "&type=4" + "&warehouse=" + res.data.warehouse + "&warehouse_name=" + res.data.warehouse_name,
      });
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  } catch (err) {
    console.error('获取采购订单详情失败:', err);
    uni.showToast({
      title: "请求失败",
      icon: "none",
    });
  }
};

/**
 * 初始化滚动视图高度
 */
const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44;
    const searchHeight = (screenWidth * 80) / 750;
    const inputHeight = (screenWidth * 90) / 750;
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};

/**
 * 滚动到底部加载更多
 */
const onScrollToLower = () => {
  if (loadStatus.value === "loadmore") {
    getPurchaseGoodsList(true);
  }
};

/**
 * 过滤仓库和供应商
 */
const filterItemsForWarehouse = () => {
  if (!warehouse.value && !supplier.value) {
    returnGoodsList.value.forEach(item => {
      item.isWarehouse = true;
      item.isSupplier = true;
    });
    return;
  }
  const matchedItems: PurchaseGoodsItem[] = [];
  const unmatchedItems: PurchaseGoodsItem[] = [];
  returnGoodsList.value.forEach((item) => {
    const warehouseMatch = !warehouse.value || !item.warehouse || item.warehouse === warehouse.value;
    const supplierMatch = !supplier.value || !item.supplier || item.supplier === supplier.value;
    if (warehouseMatch && supplierMatch) {
      item.isWarehouse = true;
      item.isSupplier = true;
      matchedItems.push(item);
    } else {
      item.isWarehouse = false;
      item.isSupplier = false;
      unmatchedItems.push(item);
    }
  });
  returnGoodsList.value = [...matchedItems, ...unmatchedItems];
};

/**
 * 展开/收起购物车
 */
const expandShoppingCart = () => {
  shoppingCartShow.value = !shoppingCartShow.value;
};

/**
 * 清空已选商品列表
 */
const clearSelectGoodsList = () => {
  selectGoodsList.value = [];
};

/**
 * 减少商品数量
 * @param item - 商品项
 */
const reduceNum = (item: SelectGoodsItem) => {
  if (item.quantity > 0) {
    item.quantity--;
  }
};

/**
 * 增加商品数量
 * @param item - 商品项
 */
const addNum = (item: SelectGoodsItem) => {
  item.quantity++;
};

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.purchase-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.purchase-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.status-order-tag {
  position: absolute;
  top: 12rpx;
  right: 140rpx;
  /* padding: 0 -1rpx; */
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #e99129;
  color: #e99129;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}

.status-payment-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}

.item-content-container {
  display: flex;
  align-items: center;
}

.item-content {
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
  width: 90%;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 400rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 80rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.info-bottom {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.status-audit-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}

.payAttentionTo {
  display: flex;
  align-items: center;
}

.cart_container {
  height: 800rpx;
  background-color: #fff;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.goods_item {
  width: 90%;
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin: 0 auto;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.goods_left {
  display: flex;
  align-items: center;
  flex: 1;
}

.goods_img {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.goods_info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_code {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.goods_num {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.goods_num_input {
  width: 28px;
  text-align: center;
  font-size: 15px;
  margin: 0 6px;
  color: #222;
}

.cart_mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.cart_popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1001;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
}

.cart_container {
  overflow-y: auto;
  max-height: 60vh;
  background-color: #fff;
}

.popup-btn {
  width: 100%;
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 0 5%;
}

.selectGoods {
  width: 45%;
}

::v-deep .operatingButton .u-button.data-v-3bf2dba7 {
  height: 35px;
  width: 90%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

.purchase-item-disabled {
  opacity: 0.7;
  background: #f5f5f5;
}
</style>


