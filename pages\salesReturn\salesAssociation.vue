<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="1" @search-result="handleSearchResult" />

    <!-- 展示销售退货订单列表区域 -->
    <orderList
      :orderResults="orderResults"
      :isOrderListRefresh="isSalesReturnRefresh"
      :isSelect="true"
      @update:isOrderListRefresh="isSalesReturnRefresh = $event"
      @orderDetail="handleOrderDetail"
    />

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="false" :jumpToId="3" />


  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import orderList from "@/components/orderList.vue";
import { getSalesOrderList, getSalesOrderDetail } from "@/api/salesOrder";

const retailOrderList = ref<any[]>([]);
// 购物车相关
const shoppingCartShow = ref(false);
const cartAnimation = ref(null);
const selectGoodsList = ref<any[]>([]); //被选中的商品
const productDetailsShow = ref(""); //是否展示商品详情弹出层
const productData = ref({}); //商品详情数据
const retailOrderListParams = reactive({
  page: 1,
  page_size: 10,
});
const hasMore = ref(true);
const loading = ref(false);
const scrollViewHeight = ref("500px");
const loadStatus = ref("loadmore"); // 'loadmore' | 'loading' | 'nomore'
const warehouse = ref(null); //仓库id
const customer = ref(null); //客户id

//获取零售订单列表
const getRetailOrderListFunc = (isLoadMore = false) => {
  if (loading.value || (!hasMore.value && isLoadMore)) return;
  loading.value = true;
  loadStatus.value = "loading";

  // 构建请求参数，如果选择了客户则添加customer_id筛选
  const params: any = {
    ...retailOrderListParams,
    exclude_draft: true  // 默认排除暂存状态的订单
  };
  if (customer.value) {
    params.customer_id = customer.value;
  }

  console.log('请求零售订单列表参数:', params);
  getRetailOrders(params).then((res: any) => {
    console.log('获取零售订单列表响应:', res);
    if (res.code == 0) {
      const results = res.data.results || [];
      console.log('原始订单数据:', results);
      const newResults = results.map((item: any) => ({
        ...item,
        isWarehouse: true,
        isCustomer: true
      }));
      if (isLoadMore) {
        retailOrderList.value = retailOrderList.value.concat(newResults);
      } else {
        retailOrderList.value = newResults;
      }
      console.log('设置订单列表后长度:', retailOrderList.value.length);
      filterItemsForWarehouse();
      // 判断是否有更多
      if (retailOrderList.value.length < (res.data.count || 0)) {
        hasMore.value = true;
        loadStatus.value = "loadmore";
        retailOrderListParams.page++;
      } else {
        hasMore.value = false;
        loadStatus.value = "nomore";
      }
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
      loadStatus.value = "loadmore";
    }
    loading.value = false;
  }).catch((err: any) => {
    console.log(err);
    uni.showToast({
      title: "请求失败", // Assuming REQUEST_ERROR is a global constant or needs to be defined
      icon: "none",
    });
    loading.value = false;
    loadStatus.value = "loadmore";
  });
};

//收款状态映射（保留以备后用）
const getReceiptStatusText = (status: string) => {
  const statusMap: { [key: string]: string } = {
    "未收款": "未收款",
    "部分收款": "部分收款",
    "已收款": "已收款",
  };
  return statusMap[status] || status;
};

const handleSearchResult = (newVal: any) => {
  retailOrderList.value = newVal;
};

// 选择关联零售订单
const selectRetailOrder = (item: any) => {
  if (!item.isWarehouse || !item.isCustomer) {
    uni.showToast({
      title: '选择的订单与当前条件不匹配',
      icon: 'none'
    });
    return;
  }

  // 检查是否为暂存状态
  if (item.is_draft) {
    uni.showToast({
      title: '暂存状态的单据不能用于退货',
      icon: 'none'
    });
    return;
  }

  console.log('选择的零售订单:', item);

  // 构建退货订单数据
  const returnOrderData = {
    id: item.id,
    order_id: item.id, // 零售单使用ID作为订单号
    customer: item.customer,
    customer_name: item.customer_name,
    warehouse: item.warehouse,
    warehouse_name: item.warehouse_name,
    handler: item.handler,
    handler_name: item.handler_name,
    controller: item.controller,
    controller_name: item.controller_name,
    out_date: item.out_date,
    total_amount: item.total_amount,
    total_sale_price: item.total_sale_price
  };

  eventBus.$emit('selectReturnOrder', returnOrderData);

  // 由于零售订单API可能不包含详细的商品列表，需要获取详情
  getRetailOrderDetailFunc(item.id);
};

// 获取零售订单详情
const getRetailOrderDetailFunc = async (orderId: string) => {
  try {
    uni.showLoading({ title: '获取商品详情...' });

    const res: any = await getRetailOrdersDetail(orderId);

    if (res && res.code === 0 && res.data) {
      const orderDetail = res.data;

      // 处理商品数据，为退货做准备
      const processedItems = (orderDetail.items || []).map((orderItem: any) => ({
        ...orderItem,
        // 添加退货相关字段
        returnable_quantity: parseFloat(orderItem.out_quantity || orderItem.quantity || 0),
        remaining_quantity: 0, // 零售单通常已全部出库
        return_price: orderItem.price || orderItem.sale_price,
        stock_quantity: orderItem.quantity,
        originalQuantity: orderItem.quantity
      }));

      const combinedData = {
        items: processedItems
      };

      console.log('处理后的商品数据:', combinedData);

      eventBus.$emit('relatedOrderItemsSelected', combinedData.items);

      uni.hideLoading();

      // 跳转到商品选择页面
      uni.navigateTo({
        url:
          "/components/productSelection?data=" +
          JSON.stringify(combinedData.items) + "&type=5" +
          "&warehouse=" + (orderDetail.warehouse || '') +
          "&warehouse_name=" + (orderDetail.warehouse_name || ''),
      });
    } else {
      uni.hideLoading();
      uni.showToast({
        title: res?.msg || '获取订单详情失败',
        icon: 'none'
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('获取零售订单详情失败:', error);
    uni.showToast({
      title: '获取订单详情失败，请重试',
      icon: 'none'
    });
  }
};
// scrollViewHeight 动态计算
const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44;
    const searchHeight = (screenWidth * 80) / 750;
    const inputHeight = (screenWidth * 90) / 750;
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};
const onScrollToLower = () => {
  if (loadStatus.value === "loadmore") {
    getRetailOrderListFunc(true);
  }
};

const filterItemsForWarehouse = () => {
  console.log('开始过滤订单，当前仓库ID:', warehouse.value, '客户ID:', customer.value);
  console.log('订单列表长度:', retailOrderList.value.length);

  const matchedItems: any[] = [];
  const unmatchedItems: any[] = [];

  retailOrderList.value.forEach((item: any, index: number) => {
    // 仓库匹配逻辑：如果没有设置仓库条件，或者订单没有仓库字段，或者仓库匹配，则认为匹配
    const warehouseMatch = !warehouse.value || !item.warehouse || item.warehouse == warehouse.value;

    // 客户匹配逻辑：由于API请求时已经根据customer_id筛选，这里的客户都应该匹配
    // 但为了保持兼容性，仍然进行检查
    const customerMatch = !customer.value || item.customer == customer.value;

    console.log(`订单${index} (${item.order_id}):`, {
      warehouse: item.warehouse,
      customer: item.customer,
      warehouseMatch,
      customerMatch,
      note: customer.value ? 'API已筛选客户' : '未设置客户筛选'
    });

    if (warehouseMatch && customerMatch) {
      item.isWarehouse = true;
      item.isCustomer = true;
      matchedItems.push(item);
    } else {
      item.isWarehouse = warehouseMatch;
      item.isCustomer = customerMatch;
      unmatchedItems.push(item);
    }
  });

  console.log('匹配的订单数量:', matchedItems.length, '不匹配的订单数量:', unmatchedItems.length);
  retailOrderList.value = [...matchedItems, ...unmatchedItems];
};

// 格式化日期（保留以备后用）
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 购物车展开/收起动画
const expandShoppingCart = () => {
  // 实现动画逻辑，这里简化为直接切换显示状态
  shoppingCartShow.value = !shoppingCartShow.value;
};

// 清空已选商品列表
const clearSelectGoodsList = () => {
  selectGoodsList.value = [];
};

// 减少商品数量
const reduceNum = (item: any) => {
  const index = selectGoodsList.value.findIndex(goods => goods.id === item.id);
  if (index !== -1) {
    if (selectGoodsList.value[index].quantity > 1) {
      selectGoodsList.value[index].quantity--;
    } else {
      selectGoodsList.value.splice(index, 1);
    }
  }
};

// 增加商品数量
const addNum = (item: any) => {
  const index = selectGoodsList.value.findIndex(goods => goods.id === item.id);
  if (index !== -1) {
    selectGoodsList.value[index].quantity++;
  }
};

onLoad((options: any) => {
  console.log('onLoad options:', options);
  const data = JSON.parse(options.returnGoodsInfo);
  console.log('解析的退货信息:', data);
  warehouse.value = data.warehouse;
  customer.value = data.customer;
  console.log('设置的仓库ID:', warehouse.value, '客户ID:', customer.value);
});

onMounted(() => {
  initScrollViewHeight();
  // 重置分页参数，确保获取最新数据
  retailOrderListParams.page = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  getRetailOrderListFunc();
});

onBeforeUnmount(() => {
  // 清除事件监听器，防止内存泄漏
  // eventBus.$off('relatedOrderSelected'); // No need to off if not explicitly on this component
  // eventBus.$off('relatedOrderItemsSelected'); // No need to off if not explicitly on this component
});

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}



.cart_container {
  height: 800rpx;
  background-color: #fff;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.goods_item {
  width: 90%;
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin: 0 auto;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.goods_left {
  display: flex;
  align-items: center;
  flex: 1;
}

.goods_img {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.goods_info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_code {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.goods_num {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.goods_num_input {
  width: 28px;
  text-align: center;
  font-size: 15px;
  margin: 0 6px;
  color: #222;
}

.cart_mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.cart_popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1001;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
}

.cart_container {
  overflow-y: auto;
  max-height: 60vh;
  background-color: #fff;
}

.popup-btn {
  width: 100%;
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 0 5%;
}

.selectGoods {
  width: 45%;
}

::v-deep .operatingButton .u-button.data-v-3bf2dba7 {
  height: 35px;
  width: 90%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ffffff00;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.retail-item-disabled {
  opacity: 0.7;
  background: #f5f5f5;
}

.item-unmatched {
  opacity: 0.8;
  background: #fff3cd;
  border-left: 4px solid #ffc107;
}
</style>


