import request from '@/utils/request.ts';
import store from '@/store';

// 定义零售订单数据接口
interface RetailOrderData {
  id?: string; // 订单ID，可选，因为新增时可能没有
  // 其他零售订单相关字段
  [key: string]: any;
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取零售订单、销货列表信息
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getRetailOrders = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesouts/`, data);
};

/**
 * 搜索零售订单列表信息
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchRetailOrders = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesouts/search/`, data);
};

/**
 * 新增零售订单信息
 * @param {RetailOrderData} data - 零售订单数据
 * @returns {Promise<any>}
 */
export const addRetailOrders = (data: RetailOrderData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/salesouts/`, data);
};

/**
 * 修改零售订单信息
 * @param {RetailOrderData} data - 零售订单数据
 * @returns {Promise<any>}
 */
export const editRetailOrders = (data: RetailOrderData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/salesouts/`, data);
};

/**
 * 删除零售订单信息
 * @param {object} data - 包含零售订单ID的数据
 * @returns {Promise<any>}
 */
export const delRetailOrders = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/salesouts/`, data);
};

/**
 * 获取零售订单详情
 * @param {string} id - 零售订单ID
 * @returns {Promise<any>}
 */
export const getRetailOrdersDetail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesouts/${id}/`);
};

/**
 * 获取批发订单详情（使用与零售订单相同的API端点，通过区分document_type实现）
 * @param {string} id - 批发订单ID
 * @returns {Promise<any>}
 */
export const getWholesaleOrdersDetail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesouts/${id}/?document_type=wholesale`);
};
