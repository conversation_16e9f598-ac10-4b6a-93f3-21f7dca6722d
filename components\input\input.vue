<template>
	<view class="container" :style="{marginBottom: safeDistance + 'rpx'}">
		<view class="inputContainer">
			<!-- 语音键盘切换图标 -->
			<view class="changeIcon" @click="openKeyboard">
				<i-enter-the-keyboard theme="outline" size="24" fill="#6c97ca" v-if="!isInput" />
				<i-voice theme="outline" size="24" fill="#6c97ca" v-if="isInput" />
			</view>
			<!-- 输入区 -->
			<view class="inputArea">
				<voice v-show="!isInput" class="voice-btu" />
				<textarea v-show="isInput" class="textarea-with-scroll input-textarea" v-model="inputContent" :focus="isInput"
					auto-height></textarea>
			</view>
			<!-- 是否为键盘输入，如果是则显示的发送图标 -->
			<view class="sendIcon">
				<i-send-one theme="outline" size="24" fill="#6c97ca" v-show="isInput" />
			</view>
		</view>
		<!-- 拍照上传 -->
		<view class="camera" v-if="isShowCamera" @click="openCamera">
			<i-camera theme="outline" size="24" fill="#6c97ca" />
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import voice from '@/components/input/components/voice.vue';

/**
 * 定义组件属性
 */
const props = defineProps({
  /**
   * 是否显示相机图标
   */
  isShowCamera: {
    type: Boolean,
    default: false
  },
  /**
   * 安全距离，用于底部留白
   */
  safeDistance: {
    type: Number,
    default: 0
  }
});

/**
 * 输入框状态，true 为键盘输入，false 为语音输入
 */
const isInput = ref<boolean>(false);
/**
 * 输入框内容
 */
const inputContent = ref<string>("");

/**
 * 切换键盘/语音输入模式
 */
const openKeyboard = (): void => {
  isInput.value = !isInput.value;
};

/**
 * 打开相机功能
 */
const openCamera = (): void => {
  uni.chooseImage({
    count: 1, // 允许选择的图片数量
    sourceType: ["camera"], // 只允许使用相机
    success: (res: UniApp.ChooseImageSuccessCallbackResult) => {
      console.log("选择的图片路径：", res.tempFilePaths[0]);
      // 可以在这里处理图片路径，例如上传到服务器
    }
  });
};
</script>

<style lang="scss" scoped>
	.container {
		height: 90rpx;
		border-top: 1px dashed #999;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 10rpx 20rpx 0 20rpx;

		.inputContainer {
			height: 60%;
			width: 80%;
			background-color: #efefef;
			border-radius: 40rpx;
			display: flex;
			padding: 0 10rpx;
			margin-bottom: 20rpx;
			border: 2px #2d86e4 solid;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
		}

		.changeIcon {
			height: 100%;
			width: 10%;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.inputArea {
			width: 80%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #efefef;

			.voice-btu {
				width: 100%;
				height: 100%;
			}

						.input-textarea {
				height: 80%;
				line-height: 1.5;
				vertical-align: middle;
			}
		}

		.sendIcon {
			width: 10%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}

	/* 在样式文件中添加以下类 */
	.textarea-with-scroll {
		width: 100%;
	}

	.camera {
		width: 10%;
		height: 60%;
		border-radius: 50%;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 10rpx 20rpx 10rpx;
	}
</style>