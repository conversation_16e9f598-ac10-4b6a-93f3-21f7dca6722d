<template>
  <view class="info">
    <view class="info_title">
      <text>附件信息</text>
      <view class="sub-title">（图片最多四张，单张大小不超过1M）</view>
    </view>
    <view class="upload-area">
      <view class="img-list">
        <view v-for="(item, index) in images" :key="item.key" class="img-box">
          <image :src="item.url" class="img"
            :style="(item.uploading || item.uploadFailed) ? 'filter: grayscale(1);' : ''"
            @click="item.uploadFailed && !prohibitModification ? retryUpload(index) : null" />
          <!-- 上传中显示进度和转圈 -->
          <view v-if="item.uploading" class="progress-mask">
            <view class="spinner"></view>
            <view class="progress-text">{{ item.progress }}%</view>
          </view>
          <!-- 上传失败显示红色感叹号 -->
          <view v-else-if="item.uploadFailed" class="progress-mask" @click="!prohibitModification && retryUpload(index)">
            <i-attention theme="filled" size="24" fill="#f56c6c" />
            <text class="retry-text">{{ prohibitModification ? '上传失败' : '点击重试' }}</text>
          </view>
          <!-- 右上角删除按钮，禁用时不显示 -->
          <view v-if="!prohibitModification" class="iconfont icon-close delete-btn" @click.stop="deletePic(index, item)">
            <!-- <i-close-small theme="filled" size="16" fill="#f56c6c"/> -->
            <i-close-one theme="filled" size="16" fill="#f56c6c" />
          </view>
        </view>
        <!-- 添加图片按钮，禁用时不显示 -->
        <view v-if="images.length < 4 && !prohibitModification" class="img-box add-btn" @click="chooseImage">
          <!-- 你可以用SVG或者iconfont，这里用SVG示例 -->
          <i-camera theme="filled" size="24" fill="#3c4353" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { compressImageForUniapp } from "@/utils/imageCompression";
import { uploadImageFile, checkForDuplication } from "@/api/image";
import config from '@/app.json';

// 定义图片对象接口
interface ImageObject {
  key: number;
  url: string;
  filePath: string;
  md5: string;
  uploading: boolean;
  progress: number;
  uploadTask: UniApp.UploadTask | null;
  uploadFailed: boolean;
}

// 定义 Props
const props = defineProps({
  prohibitModification: {
    type: Boolean,
    default: false
  },
  images_urls: {
    type: Array as () => any[],
    default: () => []
  }
});

// 定义 Emits
const emit = defineEmits(['uploadSuccess', 'deletePic']);

// 响应式数据
const images = ref<ImageObject[]>([]);

// 监听 images_urls prop 的变化，初始化 images 列表
watch(() => props.images_urls, (newVal) => {
  if (newVal) {
    images.value = newVal.map(img => ({
      key: Date.now() + Math.random(), // 为现有图片生成一个key
      url: img.url || img.image_url, // 兼容不同字段名
      filePath: img.url || img.image_url, // 假设url就是filePath
      md5: img.md5 || '', // 如果有md5，则使用，否则为空
      uploading: false,
      progress: 100,
      uploadTask: null,
      uploadFailed: false
    }));
  }
}, { immediate: true, deep: true });

/**
 * 选择图片
 */
const chooseImage = (): void => {
  // 如果禁止修改，直接返回
  if (props.prohibitModification) return;

  uni.chooseImage({
    count: 1,
    success: (res) => {
      const filePath = res.tempFilePaths[0];
      // 先压缩图片
      compressImageForUniapp(filePath, {
        maxSize: 200, // 你可以根据需要调整
        quality: 80
      }).then(compressedPath => {
        uni.getFileInfo({
          filePath: compressedPath,
          digestAlgorithm: 'md5',
          success: (fileInfoRes) => {
            console.log(fileInfoRes);

            const md5 = fileInfoRes.digest;
            const imgObj: ImageObject = {
              key: Date.now() + Math.random(),
              url: compressedPath,
              filePath: compressedPath,
              md5: md5,
              uploading: true,
              progress: 0,
              uploadTask: null,
              uploadFailed: false
            };
            images.value.push(imgObj);
            uploadImage(imgObj);
          },
          fail: (err) => {
            uni.showToast({ title: '获取MD5失败', icon: 'none' });
          }
        });
      }).catch((err) => {
        console.log(err);
        uni.showToast({ title: '图片压缩失败', icon: 'none' });
      });
    }
  });
};

/**
 * 上传图片
 * @param imgObj - 图片对象
 */
const uploadImage = (imgObj: ImageObject): void => {
  const token = uni.getStorageSync('server_access_token');
  const userState = uni.getStorageSync('user_state');
  const LEDGER_TAG = userState ? JSON.parse(userState).ledger_name : '';
  const serverIndex = 0; // 假设默认使用第一个服务器地址
  const url = config.serverUrls[serverIndex];

  const uploadTask = uni.uploadFile({
    url: `${url}/ztx/${LEDGER_TAG}/images/`,
    filePath: imgObj.filePath,
    name: 'image',
    header: {
      'Authorization': `Bearer ${token}`
    },
    success: (res) => {
      console.log(res);
      const resData = JSON.parse(res.data);
      if (resData.code === 0) {
        emit('uploadSuccess', resData.data);
        imgObj.uploading = false;
        imgObj.progress = 100;
        imgObj.uploadTask = null;
        imgObj.uploadFailed = false;
      } else {
        imgObj.uploading = false;
        imgObj.uploadTask = null;
        imgObj.uploadFailed = true;
        imgObj.progress = 0;
        uni.showToast({ title: '上传失败', icon: 'none' });
      }
    },
    fail: (err) => {
      console.log(err);
      imgObj.uploading = false;
      imgObj.uploadTask = null;
      imgObj.uploadFailed = true;
      imgObj.progress = 0;
      uni.showToast({ title: '上传失败', icon: 'none' });
    }
  });

  imgObj.uploadTask = uploadTask;
  if (uploadTask && uploadTask.onProgressUpdate) {
    uploadTask.onProgressUpdate((res) => {
      imgObj.progress = res.progress;
    });
  }
};

/**
 * 重试上传
 * @param index - 图片索引
 */
const retryUpload = (index: number): void => {
  // 如果禁止修改，直接返回
  if (props.prohibitModification) return;

  const imgObj = images.value[index];
  if (!imgObj) return;
  imgObj.uploading = true;
  imgObj.uploadFailed = false;
  imgObj.progress = 0;
  uploadImage(imgObj);
};

/**
 * 删除图片
 * @param index - 图片索引
 * @param item - 图片对象
 */
const deletePic = (index: number, item: ImageObject): void => {
  // 如果禁止修改，直接返回
  if (props.prohibitModification) return;

  console.log(item);
  emit('deletePic', item);
  images.value.splice(index, 1);
};
</script>

<style lang="scss" scoped>
.info {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.info_title {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;

  .sub-title {
    font-size: 24rpx;
    color: #bdbdbd;
    margin-left: 16rpx;
  }
}

.upload-area {
  padding: 20rpx;

  .img-list {
    display: flex;
    flex-wrap: wrap;

    .img-box {
      width: 120rpx;
      height: 120rpx;
      background: #fff;
      border: 2rpx solid #e0e0e0;
      border-radius: 8rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .progress-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .spinner {
          width: 32rpx;
          height: 32rpx;
          border: 4rpx solid #eee;
          border-top: 4rpx solid #2468f2;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .progress-text {
          margin-top: 6rpx;
          color: #2468f2;
          font-size: 20rpx;
          font-weight: bold;
        }

        .retry-text {
          margin-top: 8rpx;
          color: #f56c6c;
          font-size: 20rpx;
        }
      }

      .delete-btn {
        position: absolute;
        top: 4rpx;
        right: 4rpx;
        color: #f56c6c;
        font-size: 28rpx;
        z-index: 2;
      }
    }

    .add-btn {
      background: #f5f5f5;
      border: 2rpx dashed #bdbdbd;

      .camera-icon {
        display: block;
        margin: auto;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>