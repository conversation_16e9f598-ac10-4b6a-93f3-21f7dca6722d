import request from '@/utils/request.ts';
import store from '@/store';

// 定义销售退货数据接口
interface SalesReturnData {
  id?: string; // 销售退货ID，可选，因为新增时可能没有
  // 其他销售退货相关字段
  [key: string]: any;
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取销售退货列表
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getSalesReturnList = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesreturns/`, data);
};

/**
 * 获取销售退货详细信息
 * @param {string} id - 销售退货ID
 * @returns {Promise<any>}
 */
export const getSalesReturnDetail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesreturns/${id}/`);
};

/**
 * 新建销售退货
 * @param {SalesReturnData} data - 销售退货数据
 * @returns {Promise<any>}
 */
export const createSalesReturn = (data: SalesReturnData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/salesreturns/`, data);
};

/**
 * 更新销售退货
 * @param {SalesReturnData} data - 销售退货数据
 * @returns {Promise<any>}
 */
export const updateSalesReturn = (data: SalesReturnData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/salesreturns/${data.id}/`, data);
};

/**
 * 删除销售退货
 * @param {object} data - 包含销售退货ID的数据
 * @returns {Promise<any>}
 */
export const deleteSalesReturn = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/salesreturns/${data.id}/`);
};

/**
 * 搜索销售退货
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchSalesReturn = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesreturns/`, data);
};

/**
 * 获取销售退货统计信息
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getSalesReturnStats = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesreturns/stats/`, data);
};

/**
 * 审核销售退货
 * @param {string} id - 销售退货ID
 * @param {object} data - 审核数据
 * @returns {Promise<any>}
 */
export const auditSalesReturn = (id: string, data: object): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/salesreturns/${id}/audit/`, data);
};

/**
 * 取消审核销售退货
 * @param {string} id - 销售退货ID
 * @returns {Promise<any>}
 */
export const cancelAuditSalesReturn = (id: string): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/salesreturns/${id}/cancel_audit/`);
};
