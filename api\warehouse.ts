import request from '@/utils/request.ts';
import store from '@/store';

// 定义仓库数据接口
interface WarehouseData {
  id?: string; // 仓库ID，可选，因为新增时可能没有
  name: string; // 仓库名称
  location?: string; // 仓库位置
  remark?: string; // 备注
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取仓库列表信息
 * @returns {Promise<any>}
 */
export const getWarehouses = (): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/warehouses/`);
};

/**
 * 修改仓库列表信息
 * @param {WarehouseData} data - 仓库数据
 * @returns {Promise<any>}
 */
export const editWarehouses = (data: WarehouseData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/warehouses/${data.id}/`, data);
};

/**
 * 新建仓库信息
 * @param {WarehouseData} data - 仓库数据
 * @returns {Promise<any>}
 */
export const addWarehouses = (data: WarehouseData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/warehouses/`, data);
};

/**
 * 删除仓库信息
 * @param {object} data - 包含仓库ID的数据
 * @returns {Promise<any>}
 */
export const delWarehouses = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/warehouses/${data.id}/`);
};
