<template>
  <view class="goodsList">
    <scroll-view scroll-y class="scroll-Y" @scrolltolower="loadMore" :style="{ height: scrollViewHeight }">
      <view class="goods_item" v-for="(item, index) in goodsList" :key="index" @tap="navigateToEdit(item)"
        :class="{ goods_item_disabled: !item.is_active || !item.isWarehouse }">
        <view v-if="
          type === 2 ||
          type === 6 ||
          ((item.is_active || item.isWarehouse) && selectGoods) ||
          type === 9
        ">
    <scroll-view
      scroll-y
      class="scroll-Y"
      @scrolltolower="loadMore"
      :style="{ height: scrollViewHeight }"
    >
      <view
        class="goods_item"
        v-for="(item, index) in goodsList"
        :key="index"
        @tap="navigateToEdit(item)"
        :class="{ goods_item_disabled: !item.is_active || !item.isWarehouse }"
      >
        <view
          v-if="
            type === 2 ||
            type === 6 ||
            type === 10 ||
            ((item.is_active || item.isWarehouse) && selectGoods) ||
            type === 9
          "
        >
          <view class="goods_theme">
            <view class="goods_img">
              <i-label
                v-if="item.selected"
                class="corner-bg"
                theme="filled"
                size="22"
                fill="#e78615"
              />
              <image
                :src="
                  (item.images_urls && item.images_urls.length > 0 ? item.images_urls[0].thumbnail : '/static/img/default-goods.png')
                "
                mode=""
              />
            </view>
            <view class="goods_title">
              <view class="goods_name">
                <u-tag text="序" size="mini" :type="!item.is_active || !item.isWarehouse ? 'info' : 'primary'
                  "></u-tag>
                <text style="margin-left: 10rpx" :class="{
                  'text-disabled': !item.is_active || !item.isWarehouse,
                }">{{ item.name || item.item_name }}</text>
              </view>
              <view class="goods_id" :class="{
                'text-disabled': !item.is_active || !item.isWarehouse,
              }">{{ item.code || item.item }}</view>
            </view>
            <!-- 添加禁用标识 -->
            <view class="disabled-tag" v-if="!item.is_active">
              <u-icon name="lock-fill" color="#999" size="14"></u-icon>
              <text class="disabled-tag-text">已禁用</text>
            </view>
            <!-- 选择商品 -->
            <view class="select-goods" v-if="selectGoods" @click.stop="selectProduct(item)">
              <i-add-one theme="outline" size="20" fill="#509ae8" />
            </view>
          </view>
          <view class="goods_info">
            <!-- 左 -->
            <view class="goods_attributes">
              <view class="goods_attribute">
                <view class="goods_attribute_name" :class="{
                  'text-disabled': !item.is_active || !item.isWarehouse,
                }">单位
                </view>
                <view class="goods_attribute_value" :class="{
                  'text-disabled': !item.is_active || !item.isWarehouse,
                }">
                  ：{{ item.units && item.units.length > 0 ? item.units[0].unit_type_name : item.unit_name }}</view>
              </view>
              <view
                class="goods_attribute"
                v-if="
                  type == 0 ||
                  type == 3 ||
                  type == 7 ||
                  type == 8 ||
                  type == 4 ||
                  type == 5 ||
                  type == 10
                ">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">单价</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">：{{
                  $toFixed2(
                    item.units && item.units[0]
                      ? item.units[0].price
                      : item.price
                  )
                }}
                </view>
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">进货价</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">：{{
                  $toFixed(
                    item.units && item.units[0]
                      ? item.units[0].total_cost
                      : item.total_cost
                  )
                }}</view>
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">批发价</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{
                    $toFixed(
                      item.units && item.units[0]
                        ? item.units[0].wholesale_price
                        : item.wholesale_price
                    )
                  }}</view>
              </view>

              <!-- 退货单字段 -->
              <view
                class="goods_attribute"
                v-if="type == 4 || type == 5 || type == 7 || type == 8 || type == 10">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">金额</view>

                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                  >：{{  $toFixed2(item.total_cost) ||
                  $toFixed2(item.quantity * item.price) }}
                </view>
              </view>
            </view>
            <!-- 右 -->
            <view class="goods_attributes">
              <view class="goods_attribute" v-if="type == 2 || type == 6">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">库存</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">：{{ item.total_stock
                  }}
                </view>
              </view>
              <view class="goods_attribute" v-if="
                type == 0 || type == 3 || type == 4 || type == 7 || type == 8
              ">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }" v-if="type !== 4">数量
                </view>
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }" v-else>原数量</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{ $toFixed(item.quantity) }}</view>
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  v-else
                  >原数量</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{ $toFixed2(item.quantity) }}</view
                >
              </view>
              <view class="goods_attribute" v-if="type == 0 || type == 3">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">金额</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{  $toFixed2(item.total_cost) }}</view>
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">零售价</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{
                    $toFixed(
                      item.units && item.units[0]
                        ? item.units[0].retail_price
                        : item.retail_price
                    )
                  }}
                </view>
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">最低售价</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{
                    $toFixed(
                      item.units && item.units[0]
                        ? item.units[0].min_price
                        : item.min_price
                    )
                  }}</view>
              </view>

              <!-- 退货单字段 -->
              <view class="goods_attribute" v-if="type == 4 || type == 5">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">仓库</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{ item.warehouse_name }}</view>
              <view class="goods_attribute" v-if="type == 4 || type == 5 || type == 10">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >仓库</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{ item.warehouse_name || warehouseData.warehouse_name }}</view
                >
              </view>
              <view class="goods_attribute" v-if="type == 4 || type == 5 || type == 10">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">库存余量</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{ $toFixed2(item.remaining_quantity) || $toFixed2(item.stock_quantity) }}</view>
                <view style="margin-left: 10rpx" @click.stop="record(item)"
                  v-if="item.remaining_quantity !== item.quantity">
                  <i-attention theme="outline" size="16" fill="#d13b3b" />
                </view>
              </view>

              <!-- 零售退货单字段 -->
              <view class="goods_attribute" v-if="type == 7 || type == 8">
                <view class="goods_attribute_name" :class="{ 'text-disabled': !item.is_active }">已退货</view>
                <view class="goods_attribute_value" :class="{ 'text-disabled': !item.is_active }">
                  ：{{ item.remaining_quantity }}</view>
                <view style="margin-left: 10rpx" @click.stop="record(item)"
                  v-if="item.remaining_quantity !== item.quantity">
                  <i-attention theme="outline" size="16" fill="#d13b3b" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载更多 -->
      <view class="loading-more" v-if="loading">
        <u-loading-icon></u-loading-icon>
        <text class="status-text">加载中...</text>
      </view>
      <view class="no-more" v-if="!hasMore && goodsList.length > 0">
        <text class="status-text">没有更多数据了</text>
      </view>
      <view class="empty" v-if="!loading && goodsList.length === 0">
        <text class="status-text">暂无数据</text>
      </view>
    </scroll-view>

    <productDetails :productDetailsShow="productDetailsShow" :productData="productData" :isShowDelBtn="false"
      :type="type" :isShowUnit="isShowUnit" :selectedGoodsQuantity="selectedGoodsQuantity" @close="closeSelectPopup"
      @confirm="confirmSelectProduct" />

    <!-- 销货记录框 -->
    <view>
      <u-popup :show="isShowPayAttentionTo" mode="bottom" @close="close" @open="handlePayAttentionTo">
        <!-- 标题栏 -->
        <view class="popup-title-container">
          <view class="popup-title">
            <text class="popup-title-text" style="font-size: 32rpx; font-weight: bold">出库记录</text>
          </view>
          <view style="position: absolute; right: 30rpx" @click.stop="close">
            <i-close-one theme="outline" size="24" fill="#df7777" />
          </view>
        </view>
        <!-- 出入库记录列表 -->
        <view class="sales-list">
          <view v-for="(item, index) in goodsExitRecord" :key="index">
            <view class="sales-item">
              <text class="sales-item-text">单号：{{ item.history_order_id }}</text>
              <text class="sales-item-text">{{ getHistoryTypeText(item.history_type) }}：{{
                item.quantity
              }}</text>
              <text class="sales-item-text">日期：{{ item.history_date }}</text>
            </view>
          </view>
          <view class="cumulative">
            累计出货：{{ cumulativeReturnGoods }}
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import productDetails from "@/components/productDetails.vue";
import { getGoods, getGoodsDetail, getGoodsPages } from "@/api/goods";
import { entranceAndExitRecord } from "@/api/inventory";
import eventBus from "@/utils/eventBus.ts";

interface ProductUnit {
  unit_type_name: string;
  unit_type_id: string;
  wholesale_price: number;
  retail_price: number;
  min_price: number;
  conversion_rate: number;
  numberOfOldUnits?: number;
  numberOfNewUnits?: number;
}

interface ProductStock {
  warehouse_id: string;
  warehouse_name: string;
  stock_quantity: number;
}

interface GoodsItem {
  id: string;
  item?: string;
  code: string;
  name: string;
  item_name?: string;
  is_active: boolean;
  isWarehouse: boolean;
  selected: boolean;
  units?: ProductUnit[];
  unit_name?: string;
  price?: number;
  total_cost?: number;
  wholesale_price?: number;
  retail_price?: number;
  min_price?: number;
  total_stock?: number;
  quantity?: number;
  remaining_quantity?: number;
  warehouse?: string;
  warehouse_name?: string;
  images_urls?: { thumbnail: string }[];
}

interface PageData {
  extra_param: { include_inactive: boolean; warehouse_id: string };
  nextPage: () => void;
  firstPage: () => void;
}

interface Props {
  isGoodsManageRefresh?: boolean;
  searchResults?: GoodsItem[];
  selectGoods?: boolean;
  deselectItem?: GoodsItem;
  deselectItemList?: string[];
  relatedOrderGoods?: GoodsItem[];
  type?: number;
  includeInactive?: boolean;
  warehouseData?: {
    warehouse: string;
    warehouse_name: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  isGoodsManageRefresh: false,
  searchResults: () => [],
  selectGoods: false,
  deselectItem: () => ({}),
  deselectItemList: () => [],
  relatedOrderGoods: () => [],
  type: 0,
  includeInactive: false,
  warehouseData: () => ({
    warehouse: '',
    warehouse_name: ''
  }),
});

const emit = defineEmits(['update:isGoodsManageRefresh', 'selectedGoodsList', 'selectType']);

const goodsListParams = reactive({
  page: 1,
  page_size: 10,
  include_inactive: props.includeInactive,
  warehouse_id: "",
});

const pageData: PageData = getGoodsPages(
  {
    include_inactive: goodsListParams.include_inactive,
    warehouse_id: goodsListParams.warehouse_id,
  },
  {
    fakeLoadCallback: (data: any) => fakeLoad(data),
    pageOneCallback: (data: any) => pageOne(data),
    expandCallback: (data: any, page: number) => expandPage(data, page),
  }
);

const goodsList = ref<GoodsItem[]>([]); //商品列表
const loading = ref(false);
const hasMore = ref(true);
const scrollViewHeight = ref("500px");
const selectGoodsList = ref<GoodsItem[]>([]); //被选中的商品
const productDetailsShow = ref(false); //是否展示商品详情弹出层
const productData = ref<any>({
  purchase_order_item: "", //关联订单的id
}); //商品详情数据
const returnWarehouse = reactive({
  warehouse: "",
  warehouse_name: "",
}); //退货时选择的仓库
const goodsExitRecord = ref<any[]>([]); //商品出库记录
const cumulativeReturnGoods = ref(0); //商品出库记录
const isShowPayAttentionTo = ref(false); //销货记录框
const selectedGoodsQuantity = ref<any[]>([]); // [{id: 商品id, quantity: 总数量}]
const isShowUnit = ref(true); //在详细信息弹出层是否显示单位选择器

const filterItemsForWarehouse = () => {
  const data = uni.getStorageSync("returnWarehouse");
  if (data) {
    console.log(data);

    returnWarehouse.warehouse = data.warehouse;
    returnWarehouse.warehouse_name = data.warehouse_name;
    const matchedItems: GoodsItem[] = [];
    const unmatchedItems: GoodsItem[] = [];
    goodsList.value.forEach((item) => {
      if (
        item.warehouse &&
        item.warehouse !== returnWarehouse.warehouse
      ) {
        item.isWarehouse = false;
        unmatchedItems.push(item);
      } else {
        matchedItems.push(item);
      }
    });
    goodsList.value = [...matchedItems, ...unmatchedItems];
    console.log(goodsList.value);
  }
};

watch(() => props.includeInactive, (newVal) => {
  goodsListParams.include_inactive = newVal;
  pageData.extra_param.include_inactive = newVal;
  if (props.type === 1 || props.type === 2 || props.type === 6 || props.type === 5 || props.type === 10) {
    goodsListParams.page = 1;
    goodsList.value = [];
    hasMore.value = true;
    getGoodsList();
  }
}, { immediate: true });

watch(() => props.warehouseData, (newVal) => {
  if (newVal && newVal.warehouse) {
    console.log('仓库数据变化，新值:', newVal);
    goodsListParams.warehouse_id = newVal.warehouse;
    pageData.extra_param.warehouse_id = newVal.warehouse;

    // 如果是销货单类型，更新所有商品的仓库信息
    if (props.type === 10 && goodsList.value && goodsList.value.length > 0) {
      console.log("更新销货单商品的仓库信息");
      goodsList.value.forEach(item => {
        if (!item.warehouse) item.warehouse = newVal.warehouse;
        if (!item.warehouse_name) item.warehouse_name = newVal.warehouse_name;
      });
      console.log("更新后的第一个商品:", goodsList.value[0]);
    }
    // 其他类型则重新加载数据
    else if (props.type === 1 || props.type === 2 || props.type === 6 || props.type === 5) {
      goodsListParams.page = 1;
      goodsList.value = [];
      hasMore.value = true;
      getGoodsList();
    }
  }
}, { deep: true, immediate: false });

watch(() => props.searchResults, (newVal) => {
    console.log(newVal);
    if (
      props.relatedOrderGoods.length > 0 ||
      (props.type !== 1 && props.type !== 2 && props.type !== 6 && props.type !== 10)
    ) {
      return;
    }
  if (newVal.length > 0) {
    const filteredItems = newVal.filter(
      (item) =>
        !props.relatedOrderGoods?.some(
          (relatedItem) => relatedItem.item === item.id
        )
    );
    goodsList.value = mapGoodsItems(filteredItems);
  } else {
    goodsListParams.page = 1;
    getGoodsList();
  }
});

watch(() => props.relatedOrderGoods, (newVal) => {
  console.log("relatedOrderGoods 变化:", newVal ? newVal.length : '无数据', "类型:", props.type);

  if (newVal && newVal.length > 0) {
    console.log("relatedOrderGoods 有数据，长度:", newVal.length);

    // 销货单特殊处理
    if (props.type === 10) {
      console.log("relatedOrderGoods监听到销货单类型数据变化");
      initSalesOrderData(); // 使用专门的方法处理销货单数据
    } else {
      goodsList.value = mapGoodsItems(newVal);
    }

    if (props.type === 4 || props.type === 5) {
      filterItemsForWarehouse();
    }
  }
}, { immediate: true });

watch(() => props.deselectItem, (newVal) => {
  console.log(newVal);
  console.log(selectGoodsList.value);
  if (goodsList.value && goodsList.value.length > 0) {
    console.log("goodsList 设置成功，长度:", goodsList.value.length, "第一个元素:", goodsList.value[0].name || goodsList.value[0].item_name);
  }
  goodsList.value.forEach((g) => {
    if (g.item === newVal.item) {
      const hasOtherSameItem = selectGoodsList.value.some(
        (selectedItem) =>
          selectedItem.item === newVal.item &&
          !(selectedItem.item === newVal.item && selectedItem.unit === newVal.unit)
      );
      if (!hasOtherSameItem) {
        g.selected = false;
      }
    }
  });
  if (props.type === 4 || props.type === 5) {
    filterItemsForWarehouse();
  }
}, { immediate: true, deep: true });

watch(() => props.deselectItemList, (newVal) => {
  console.log(newVal);
  selectGoodsList.value = [];
  goodsList.value.forEach((g) => {
    if (newVal.includes(g.item as string)) {
      const hasOtherSameItem = selectGoodsList.value.some(
        (selectedItem) => selectedItem.item === g.item
      );

      if (!hasOtherSameItem) {
        g.selected = false;
      }
    }
  });
}, { immediate: true });

watch(() => props.isGoodsManageRefresh, (newVal) => {
  console.log(newVal);

  if (newVal) {
    if (
      props.type !== 0 &&
      props.type !== 3 &&
      props.type !== 4 &&
      props.type !== 5
    ) {
      getGoodsList();

      emit("update:isGoodsManageRefresh", false);
    }
  }
});

watch(() => returnWarehouse, (newVal) => {
  console.log(newVal);

  filterItemsForWarehouse();
}, { deep: true, immediate: true });
        this.filterItemsForWarehouse();
      },
      immediate: true,
    },
  },
// 销货记录框关闭弹窗
const close = () => {
  isShowPayAttentionTo.value = false;
};

//打开销货记录
const handlePayAttentionTo = (item: GoodsItem) => {
  isShowPayAttentionTo.value = true;
};

// 获取历史记录类型文本
const getHistoryTypeText = (type: string): string => {
  const typeMap: { [key: string]: string } = {
    pi: "采购入库",
    so: "销售出库",
    ai: "调拨入库",
    ao: "调拨出库",
    ami: "组装入库",
    amo: "组装出库",
    rpi: "维修入库",
    rpo: "维修出库",
    rin: "退货入库",
    rot: "退货出库",
    srt: "库存退货",
    ini: "初始入库",
  };
  return typeMap[type] || type;
};

onMounted(() => {
  console.log("goodsList mounted, type:", props.type, "relatedOrderGoods:", JSON.stringify(props.relatedOrderGoods));

  initScrollViewHeight();

  //如果是进货单关联订单进入，商品详细层不展示单位选择器
  if (props.type === 0 || props.type === 3 || props.type === 7 || props.type === 8) {
    isShowUnit.value = false;
  }
  //如果是零售单进入，需要传入仓库id
  if (props.type === 6) {
    goodsListParams.warehouse_id = uni.getStorageSync("retailWarehouseId");
    pageData.extra_param.warehouse_id = uni.getStorageSync("retailWarehouseId");
  }

  // 如果传入了仓库数据，设置仓库ID参数（适用于批发订单等场景）
  if (props.warehouseData && props.warehouseData.warehouse) {
    console.log('设置仓库ID参数:', props.warehouseData.warehouse);
    goodsListParams.warehouse_id = props.warehouseData.warehouse;
    pageData.extra_param.warehouse_id = props.warehouseData.warehouse;
  }

  // 销货单类型(10)特殊处理
  if (props.type === 10) {
    console.log("销货单类型，检查relatedOrderGoods数据:", props.relatedOrderGoods ? props.relatedOrderGoods.length : '无数据');
    if (props.relatedOrderGoods && props.relatedOrderGoods.length > 0) {
      initSalesOrderData();
    } else {
      console.warn("销货单类型但relatedOrderGoods为空");
      // 尝试通过正常途径加载数据
      getGoodsList();
    }
  } else if (props.type === 1 || props.type === 2 || props.type === 6 || props.type === 5) {
    getGoodsList();
  }
});

onShow(() => {
  filterItemsForWarehouse();
});
    }
    //如果是进货单、采购单、零售单、批发订单关联进入，需要请求数据
    else if (props.type === 1 || props.type === 2 || props.type === 6 || props.type === 5) {
      getGoodsList();

    // 确保类型和数据的设置正确
    setTimeout(() => {
      console.log("延迟检查 - type:", props.type, "goodsList数据:", goodsList.value.length);
    }, 500);
  },

// 初始化销货单数据方法
const initSalesOrderData = (): void => {
      console.log("初始化销货单数据, 原始数据长度:", props.relatedOrderGoods.length);
      console.log("当前仓库信息:", props.warehouseData);

      try {
        goodsList.value = props.relatedOrderGoods.map((item: GoodsItem) => ({
          ...item,
          selected: false,
          isWarehouse: true,
          is_active: true,
          // 确保必要的字段存在
          name: item.name || item.item_name,
          code: item.code || item.item,
          unit_name: item.unit_name || '个',
          price: item.price || 0,
          quantity: item.quantity || 0,
          // 确保仓库信息
          warehouse: item.warehouse || props.warehouseData?.warehouse,
          warehouse_name: item.warehouse_name || props.warehouseData?.warehouse_name,
          // 计算总金额
          total_cost: item.amount || (parseFloat(String(item.quantity || 0)) * parseFloat(String(item.price || 0)))
        }));
        console.log("销货单数据初始化完成, 处理后数据长度:", goodsList.value.length);
        console.log("第一个商品数据:", goodsList.value[0]);
      } catch (error) {
        console.error("初始化销货单数据出错:", error);
      }
    };


//出入库记录
const record = (item: GoodsItem) => {
  console.log(item);

  cumulativeReturnGoods.value = 0;
  entranceAndExitRecord({ stock_id: item.id })
    .then((res: any) => {
      console.log(res);
      if (res.code === 0) {
        goodsExitRecord.value = res.data.results || res.data;
        cumulativeReturnGoods.value = 0;
        goodsExitRecord.value.forEach((e: any) => {
          if (parseFloat(e.quantity) < 0) {
            cumulativeReturnGoods.value += Math.abs(parseFloat(e.quantity));
          }
        });
        isShowPayAttentionTo.value = true;
      } else {
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    })
    .catch((err: any) => {
      console.error("获取出入库记录失败:", err);
      uni.showToast({
        title: "获取记录失败",
        icon: "none",
      });
    });
};

        returnWarehouse.warehouse = data.warehouse;
        returnWarehouse.warehouse_name = data.warehouse_name;
        const matchedItems = [];
        const unmatchedItems = [];
        goodsList.value.forEach((item) => {
          if (
            item.warehouse &&
            item.warehouse !== returnWarehouse.warehouse
          ) {
            item.isWarehouse = false;
            unmatchedItems.push(item);
          } else {
            matchedItems.push(item);
          }
        });
        goodsList.value = [...matchedItems, ...unmatchedItems];
        console.log(goodsList.value);
      }
    },

    // 映射商品项，处理选中状态等
    const mapGoodsItems = (items: GoodsItem[]): GoodsItem[] => {
      if (!items || !Array.isArray(items)) {
        console.warn('要处理的商品列表无效');
        return [];
      }

      console.log(`处理${items.length}个商品的选中状态`);

      return items.map((item) => {
        // 销货单类型(10)直接返回原数据，确保包含必要字段
        if (props.type === 10) {
          return {
            ...item,
            selected: false,
            isWarehouse: true,
            // 确保id字段正确映射到item字段
            item: item.id || item.item || item.code,
            item_name: item.name || item.item_name || ''
          };
        }

        // 创建新对象而不是修改原始对象
        const mappedItem = {
          ...item,
          selected: false,
          isWarehouse: true,
          ...(items === relatedOrderGoods.value ? { is_active: true } : {}),
          // 确保id字段正确映射到item字段
          item: item.id || item.item || item.code,
          item_name: item.name || item.item_name || ''
        };

        // 如果是销售订单详情数据（type=5），进行字段适配
        if (props.type === 5 && items === relatedOrderGoods.value) {
          // 确保必要字段存在
          mappedItem.name = mappedItem.name || mappedItem.item_name;
          mappedItem.code = mappedItem.code || mappedItem.item;
          mappedItem.unit_name = mappedItem.unit_name || '个';
          mappedItem.price = mappedItem.price || 0;
          mappedItem.quantity = mappedItem.quantity || 0;
          mappedItem.stock_quantity = mappedItem.stock_quantity || 0;

          // 计算金额（如果没有amount字段）
          if (!mappedItem.amount && !mappedItem.total_cost) {
            mappedItem.amount = parseFloat(mappedItem.quantity || 0) * parseFloat(mappedItem.price || 0);
          }
        }

        // 检查是否已被选中
        let isSelected = false;
        let selectedItem = null;

        if (selectGoodsList.value && selectGoodsList.value.length > 0) {
          selectedItem = selectGoodsList.value.find(
            (selectItem) =>
              // 匹配逻辑：任何ID标识符和单位匹配都视为同一个商品
              ((selectItem.id && selectItem.id === mappedItem.id) ||
               (selectItem.item && selectItem.item === mappedItem.item) ||
               (selectItem.code && selectItem.code === mappedItem.code)) &&
              selectItem.unit === mappedItem.unit
          );

          isSelected = !!selectedItem;
        }

        // 设置选中状态和数量
        mappedItem.isSelected = isSelected;

        if (isSelected && selectedItem) {
          mappedItem.quantity = selectedItem.quantity || 1;
        } else if (!mappedItem.quantity) {
          mappedItem.quantity = 1; // 默认数量
        }

        // 输出处理后的第一个商品进行调试
        if (item === items[0]) {
          console.log('处理后的第一个商品:', {
            id: mappedItem.id,
            item: mappedItem.item,
            name: mappedItem.name,
            item_name: mappedItem.item_name
          });
        }

        return mappedItem;
      });
    };
    // 修改处理API返回数据的逻辑，确保id字段被正确保存
    processApiResponseData(data) {
      if (!data || !data.results) {
        console.warn('API返回的数据无效或格式不正确');
        return [];
      }

      console.log('处理API返回的商品数据，共', data.results.length, '条');

      // 确保关键字段的存在，特别是id字段
      const processedData = data.results.map(item => {
        // 打印每个商品的id和item字段，帮助调试
        console.log(`商品: ${item.name || '未命名'}, id=${item.id}, code=${item.code}`);

        return {
          ...item,
          // 确保item字段存在，优先使用id字段
          item: item.id, // 直接使用id作为item
          item_name: item.name, // 确保item_name字段
        };
      });

      return processedData;
    },
    initScrollViewHeight() {
      try {
        const info = uni.getSystemInfoSync();
        const screenWidth = info.screenWidth;
const mapGoodsItems = (items: GoodsItem[]) => {
  return items.map((item) => {
    const mappedItem: GoodsItem = {
      ...item,
      selected: false,
      isWarehouse: true,
      ...(items === props.relatedOrderGoods ? { is_active: true } : {}),
    };

    if (props.type === 5 && items === props.relatedOrderGoods) {
      mappedItem.name = mappedItem.name || mappedItem.item_name;
      mappedItem.code = mappedItem.code || mappedItem.item;
      mappedItem.unit_name = mappedItem.unit_name || '个';
      mappedItem.price = mappedItem.price || 0;
      mappedItem.quantity = mappedItem.quantity || 0;
      mappedItem.stock_quantity = mappedItem.stock_quantity || 0;

      if (!mappedItem.amount && !mappedItem.total_cost) {
        mappedItem.amount = parseFloat(String(mappedItem.quantity || 0)) * parseFloat(String(mappedItem.price || 0));
      }

      console.log('适配后的销售订单商品数据:', mappedItem);
    }

    return mappedItem;
  });
};

const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;

    const navBarHeight = 44; // 导航栏高度（标准高度）
    const statusBarHeight = info.statusBarHeight; // 状态栏高度
    const searchHeight = (screenWidth * Number.parseInt(String(80))) / 750; // 搜索框高度 80rpx
    const inputHeight = (screenWidth * Number.parseInt(String(90))) / 750; // 底部输入区域高度 90rpx
    const selectBtnHeight = (screenWidth * Number.parseInt(String(100))) / 750; // 底部输入区域高度 100rpx
    let totalHeight = 0;
    if (props.type === 0 ||
      props.type === 4 ||
      props.type === 5 ||
      props.type === 7 ||
      props.type === 8
    ) {
      totalHeight = navBarHeight + inputHeight + selectBtnHeight;
    } else if (props.selectGoods) {
      totalHeight =
        navBarHeight + searchHeight + inputHeight + selectBtnHeight;
    } else {
      totalHeight = navBarHeight + searchHeight + inputHeight;
    }
    console.log(totalHeight);

        // 计算最终的滚动区域高度
        const scrollHeight = info.windowHeight - totalHeight;
        this.scrollViewHeight = `${scrollHeight}px`;
      } catch (e) {
        console.error("获取系统信息失败：", e);
      }
    },
    //获取商品信息
    const getGoodsList = (isLoadMore = false): void => {
      // 只要有 relatedOrderGoods 或 searchResults，就不请求
      if (relatedOrderGoods.value.length > 0 && props.type === 0) {
        return;
      }
      // 销货单有关联商品数据时不需要请求
      if (relatedOrderGoods.value.length > 0 && props.type === 10) {
        return;
      }
      if (isLoadMore) {
        pageData.value.nextPage();
      } else {
        pageData.value.firstPage();
      }
      loading.value = true;
      // getGoods(this.goodsListParams)
      //   .then((res) => {
      //     // 统一过滤逻辑：排除已在关联订单中的商品的单位
      //     // if (this.relatedOrderGoods.length > 0) {
      //     //   this.goodsList = [];
      //     //   this.relatedOrderGoods.forEach(related => {
      //     //     const match = res.data.results.find(item => item.id === related.item);
      //     //     if (match && match.units) {
      //     //       match.units = match.units.filter(unit => unit.id !== related.unit);
      //     //       res.data.results.push(match);
      //     //     }
      //     //   });
      //     // }
      //     if (isLoadMore) {
      //       goodsList.value = [...goodsList.value, ...res.data.results];
      //     } else {
      //       goodsList.value = res.data.results;
      //     }
      //     // 统一处理选中状态
      //     goodsList.value = mapGoodsItems(goodsList.value);
      //     console.log(goodsList.value);
      //     if (res.code !== 0) {
      //       uni.showToast({
      //         title: res.msg,
      //         icon: "none",
      //       });
      //
      //     }
      //     //判断是否有更多数据
      //     if (goodsList.value.length >= res.data.count) {
        hasMore.value = false;
      }
    }   }
      //   })
      //   .catch((err) => {
      //     console.error("获取商品列表失败：", err);
      //     uni.showToast({
      //       title: res.msg,
      //       icon: "none",
      //     });
      //   })
      //   .finally(() => {
      //     this.loading = false;
      //   });
    },
    //触底加载
    const loadMore = (): void => {
      if (
        (relatedOrderGoods.value && props.type == 0) ||
        (relatedOrderGoods.value && props.type == 10) ||
        props.searchResults ||
        !hasMore.value ||
        loading.value
      )
        return;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};

const getGoodsList = (isLoadMore = false) => {
  if (props.relatedOrderGoods.length > 0 && props.type == 0) {
    return;
  }
  if (isLoadMore) {
        pageData.nextPage();
      } else {
        pageData.firstPage();
      }
      loading.value = true;
    };

    const loadMore = () => {
      if (
        (props.relatedOrderGoods && props.type === 0) ||
        props.searchResults || // This seems like a bug, should be related to searchResults.length
        !hasMore.value ||
        loading.value
      )
    return;

  console.log('触发加载更多，当前页码:', goodsListParams.page);
  goodsListParams.page += 1;
  getGoodsList(true);
};

const navigateToEdit = (item: GoodsItem) => {
  if (props.selectGoods) {
    selectProduct(item);
    return;
  }
  getGoodsDetail(item.id).then((res: any) => {
    if (res.code === 0) {
      uni.navigateTo({
        url:
          "/pages/commodityManagement/editProduct/editProduct?item=" +
          encodeURIComponent(JSON.stringify(res.data)),
      });
    }
  });
};

const selectProduct = (item: GoodsItem) => {
  console.log(item);
  console.log(selectGoodsList.value);
  selectGoodsList.value.forEach((selectedItem) => {
    goodsList.value.forEach((goodsItem) => {
      if (goodsItem.item === selectedItem.item) {
        goodsItem.remaining_stock = selectedItem.remaining_stock;
      }
    });
  });
  productData.value = item;
  console.log(productData.value);
  if (!item.isWarehouse) {
    uni.showToast({
      title: "此商品所在仓库与当前仓库不一致",
      icon: "none",
    });
    return;
  }
    //选择商品
    const selectProduct = (item: GoodsItem): void => {
      console.log('用户选择商品:', item);

      // 确保商品对象包含必要的字段
      const processedItem: GoodsItem = {
        ...item,
        // 确保item字段存在，优先使用id
        item: item.id || item.item || item.code,
        // 确保item_name字段
        item_name: item.name || item.item_name || ''
      };

      console.log('处理后的商品数据:', {
        id: processedItem.id,
        item: processedItem.item,
        name: processedItem.name,
        item_name: processedItem.item_name
      });

      // 同步选中商品的remaining_stock到goodsList
      selectGoodsList.value.forEach((selectedItem: GoodsItem) => {
        // 将selectGoodsList中的remaining_stock同步到goodsList中相同item的元素
        goodsList.value.forEach((goodsItem: GoodsItem) => {
          if (goodsItem.item === selectedItem.item) {
            goodsItem.remaining_stock = selectedItem.remaining_stock;
          }
        });
      });

      // 仓库检查
      if (!processedItem.isWarehouse) {
        uni.showToast({
          title: "此商品所在仓库与当前仓库不一致",
          icon: "none",
        });
        return;
      }

      // 退货特殊处理
      if (props.type === 4 || props.type === 5) {
        returnWarehouse.value.warehouse = returnWarehouse.value.warehouse || "";
        returnWarehouse.value.warehouse = processedItem.warehouse || "";
        returnWarehouse.value.warehouse_name = processedItem.warehouse_name || "";
      }

      let id = "";
      if ((productData.value.id && props.type === 0) || props.type === 3) {
        productData.value.purchase_order_item = productData.value.id;
        id = item.item as string;
      }
      if (props.type === 1 || props.type === 2 || props.type === 6) {
        item.item = item.id;
        id = item.id as string;
      }
      if (props.type === 4 || props.type === 5) {
        id = item.item as string;
        returnWarehouse.value.warehouse = item.warehouse as string;
        returnWarehouse.value.warehouse_name = item.warehouse_name as string;
      }
      if (props.type === 7 || props.type === 8) {
        id = item.item as string;
      }
      // 销货单类型特殊处理
      if (props.type === 10) {
        console.log('销货单选择商品', processedItem);
        // 确保仓库信息正确
        if (warehouseData.value && warehouseData.value.warehouse) {
          processedItem.warehouse = processedItem.warehouse || warehouseData.value.warehouse;
          processedItem.warehouse_name = processedItem.warehouse_name || warehouseData.value.warehouse_name;
        }
        // 记录原始数量和价格
        const savedQuantity = processedItem.quantity;
        const savedPrice = processedItem.price;
        console.log('保留销售订单中的原始数量:', savedQuantity, '和价格:', savedPrice);
      }

  getGoodsDetail(id).then((res: any) => {
    if (res.code === 0) {
      productData.value = { ...item, ...res.data };
      productData.value.id = item.id;
      productData.value.unit = item.unit;
      console.log(productData.value);
      // 获取商品详情，确保使用正确的ID
      const itemId = processedItem.id || processedItem.item || processedItem.code;
      console.log(`获取商品详情，使用ID: ${itemId}`);

      getGoodsDetail(itemId).then((res) => {
        if (res.code === 0) {
          // 合并API返回的详细数据与当前商品数据
          const detailedItem = {
            ...processedItem,
            ...res.data,
            // 确保保留原始的ID和item值
            id: processedItem.id,
            item: processedItem.item
          };

          // 检查商品是否已在选择列表中
          const existingIndex = selectGoodsList.value.findIndex(
            selectItem =>
              ((selectItem.id && selectItem.id === detailedItem.id) ||
               (selectItem.item && selectItem.item === detailedItem.item) ||
               (selectItem.code && selectItem.code === detailedItem.code)) &&
              selectItem.unit === detailedItem.unit
          );

      productDetailsShow.value = true;
    }
  });
};
          if (existingIndex !== -1) {
            // 如果已在选中列表中，更新详情
            console.log('商品已在选择列表中，更新详情');
            selectGoodsList.value[existingIndex] = {
              ...selectGoodsList.value[existingIndex],
              ...detailedItem
            };
          } else {
            // 如果不在选中列表中，添加到列表
            console.log('添加商品到选择列表');
            detailedItem.isSelected = true;
            detailedItem.quantity = detailedItem.quantity || 1;

            // 销货单特殊处理
            if (props.type === 10) {
              detailedItem.price = detailedItem.price || detailedItem.retail_price || detailedItem.wholesale_price || 0;
              detailedItem.total = detailedItem.quantity * detailedItem.price;
            }

            selectGoodsList.value.push(detailedItem);
          }

          // 打开商品详情弹窗
          productData.value = detailedItem;
          console.log('设置商品详情数据:', productData.value);
          productDetailsShow.value = true;

          // 发送更新后的商品列表
          emitSelectedGoods(selectGoodsList.value);

          // 更新商品列表中的选择状态
          const updateIndex = goodsList.value.findIndex(
            goodsItem =>
              ((goodsItem.id && goodsItem.id === detailedItem.id) ||
               (goodsItem.item && goodsItem.item === detailedItem.item) ||
               (goodsItem.code && goodsItem.code === detailedItem.code)) &&
              goodsItem.unit === detailedItem.unit
          );

          if (updateIndex !== -1) {
            goodsList.value[updateIndex].isSelected = true;
          }
        } else {
          console.error('获取商品详情失败:', res.msg);
          uni.showToast({
            title: res.msg || '获取商品详情失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('调用商品详情API错误:', err);
        uni.showToast({
          title: '系统错误',
          icon: 'none'
        });
      });
    },

const fakeLoad = (data: any): void => {
  console.log("fakeLoad", data);
  goodsList.value = data.results;
  goodsList.value = mapGoodsItems(goodsList.value);
  hasMore.value = data.count > data.results.length;
  loading.value = false;
};

const pageOne = (data: any): void => {
  console.log("pageOne", data);
  
  // 检查数据有效性
  if (!data || !data.results) {
    console.error('接收到的数据无效或格式不正确');
    goodsList.value = [];
    loading.value = false;
    return;
  }

  try {
    // 直接处理API返回的数据，确保id字段被正确保存和传递
    const processedData = processApiResponseData(data);

    // 使用处理后的数据
    const result = mapGoodsItems(processedData);
    goodsList.value = result;

    console.log(`商品列表首页加载成功，处理后数量: ${goodsList.value.length}`);

    // 检查第一个商品的数据结构
    if (goodsList.value.length > 0) {
      const firstItem = goodsList.value[0];
      console.log(`首个商品数据: id=${firstItem.id}, item=${firstItem.item}, name=${firstItem.name || firstItem.item_name}`);
    }

    // 更新是否有更多数据
    hasMore.value = data.next !== null;
  } catch (err) {
    console.error('处理商品数据出错:', err);
    goodsList.value = [];
  }

  loading.value = false;
};

const expandPage = (data: any, page: number): void => {
  console.log("expandPage 收到数据:", data, "页码:", page);

  // 检查数据有效性
  if (!data || !data.results || data.results.length === 0) {
    console.log('没有更多数据或数据格式不正确');
    hasMore.value = false;
    loading.value = false;
    return;
  }

  try {
    // 直接处理API返回的数据，确保id字段被正确保存和传递
    const processedData = processApiResponseData(data);

    // 将处理后的数据添加到列表
    const result = mapGoodsItems(processedData);

    // 合并逻辑：避免重复项
    result.forEach((newItem: GoodsItem) => {
      const existingIndex = goodsList.value.findIndex(
        (existingItem: GoodsItem) => existingItem.id === newItem.id && existingItem.unit === newItem.unit
      );
    fakeLoad(data) {
      console.log("fakeLoad", data);
      this.goodsList = data.results;
      this.goodsList = this.mapGoodsItems(this.goodsList);
      this.hasMore = data.count > data.results.length;
      this.loading = false;
    },
    pageOne(data) {
      console.log("pageOne 收到数据:", data);

      // 检查数据有效性
      if (!data || !data.results) {
        console.error('接收到的数据无效或格式不正确');
        this.goodsList = [];
        this.loading = false;
        return;
      }

      try {
        // 直接处理API返回的数据，确保id字段被正确保存和传递
        const processedData = this.processApiResponseData(data);

        // 使用处理后的数据
        this.goodsList = this.mapGoodsItems(processedData);

        console.log(`商品列表首页加载成功，处理后数量: ${this.goodsList.length}`);

        // 检查第一个商品的数据结构
        if (this.goodsList.length > 0) {
          const firstItem = this.goodsList[0];
          console.log(`首个商品数据: id=${firstItem.id}, item=${firstItem.item}, name=${firstItem.name || firstItem.item_name}`);
        }

        // 更新是否有更多数据
        this.hasMore = data.next !== null;
      } catch (err) {
        console.error('处理商品数据出错:', err);
        this.goodsList = [];
      }

      this.loading = false;
    },

    expandPage(data, page) {
      console.log("expandPage 收到数据:", data, "页码:", page);

      // 检查数据有效性
      if (!data || !data.results || data.results.length === 0) {
        console.log('没有更多数据或数据格式不正确');
        this.hasMore = false;
        this.loading = false;
        return;
      }

      try {
        // 直接处理API返回的数据，确保id字段被正确保存和传递
        const processedData = this.processApiResponseData(data);

        // 将处理后的数据添加到列表
        const newItems = this.mapGoodsItems(processedData);

        // 合并逻辑：避免重复项
        newItems.forEach((newItem) => {
          const existingIndex = this.goodsList.findIndex(
            (existingItem) =>
              existingItem.id === newItem.id && existingItem.unit === newItem.unit
          );

    if (existingIndex !== -1) {
      goodsList.value[existingIndex] = newItem;
    } else {
      goodsList.value.push(newItem);
    }
  });

  hasMore.value = data.count > goodsList.value.length;
  loading.value = false;
};
          if (existingIndex !== -1) {
            // 找到相同元素，覆盖现有元素
            this.$set(this.goodsList, existingIndex, newItem);
          } else {
            // 不同元素，添加到列表后面
            this.goodsList.push(newItem);
          }
        });

        console.log(`加载更多商品成功，本次处理: ${newItems.length}, 累计: ${this.goodsList.length}`);

        // 更新是否有更多数据
        this.hasMore = data.next !== null;
      } catch (err) {
        console.error('处理更多商品数据出错:', err);
      }

      this.loading = false;
    },

const closeSelectPopup = (item: GoodsItem) => {
  productDetailsShow.value = false;
};

const confirmSelectProduct = (item: GoodsItem) => {
  console.log(item);
  item.id = "";
  const index = selectGoodsList.value.findIndex(
    (g) => g.item === item.item && g.unit === item.unit
  );

  if (index > -1) {
    selectGoodsList.value.splice(index, 1, {
      ...selectGoodsList.value[index],
      ...item,
      selected: true,
    });
  } else {
    selectGoodsList.value.push({ ...item, selected: true });
  }
  console.log(selectGoodsList.value);
  const indexGoods = goodsList.value.findIndex((g) => {
    if (props.type === 0 || props.type === 3) {
      return g.item === item.item && g.unit === item.unit;
    }
    return g.item === item.item;
  });
  console.log(indexGoods);

  if (indexGoods > -1) {
    goodsList.value[indexGoods] = {
      ...goodsList.value[indexGoods],
      selected: true,
    };
  }
  productDetailsShow.value = false;

  if (props.type === 6) {
    const totalMap: { [key: string]: number } = {};

    selectGoodsList.value.forEach((item) => {
      const itemId = item.item;
      const quantity = Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      if (totalMap[itemId as string]) {
        totalMap[itemId as string] += quantity;
      } else {
        totalMap[itemId as string] = quantity;
      }
    });
      if (indexGoods > -1) {
        this.$set(this.goodsList, indexGoods, {
          ...this.goodsList[indexGoods],
          selected: true,
        });
      }
      this.productDetailsShow = false;

      // 销货单类型(10)也需要处理库存和金额计算
      if (this.type === 10) {
        // 计算商品的总价
        item.total_cost = parseFloat(item.quantity || 0) * parseFloat(item.price || 0);

        // 确保商品有仓库信息
        if (this.warehouseData && this.warehouseData.warehouse) {
          item.warehouse = item.warehouse || this.warehouseData.warehouse;
          item.warehouse_name = item.warehouse_name || this.warehouseData.warehouse_name;
        }

        console.log('销货单选择商品完成:', item);
      }
      // 只有当type为6时才计算selectedGoodsQuantity
      else if (this.type === 6) {
        // 初始化一个对象存储每个item的总量
        const totalMap = {};

        // 第一次循环，计算每个item的总量
        this.selectGoodsList.forEach((item) => {
          const itemId = item.item;
          const quantity =
            Number(item.quantity || 0) * Number(item.conversion_rate || 1);
          if (totalMap[itemId]) {
            totalMap[itemId] += quantity;
          } else {
            totalMap[itemId] = quantity;
          }
        });

        // 第二次循环，更新每个item的remaining_stock
        this.selectGoodsList.forEach((item) => {
          item.remaining_stock = totalMap[item.item] || 0;
        });
        this.goodsList.forEach((item) => {
          item.remaining_stock = totalMap[item.item] || 0;
        });
        console.log(this.selectGoodsList);
      }
    selectGoodsList.value.forEach((item) => {
      item.remaining_stock = totalMap[item.item as string] || 0;
    });
    goodsList.value.forEach((item) => {
      item.remaining_stock = totalMap[item.item as string] || 0;
    });
    console.log(selectGoodsList.value);
  }

  emit("selectedGoodsList", selectGoodsList.value);
  console.log(selectGoodsList.value);

  emit("selectType", props.type);
};

onMounted(() => {
  console.log(props.type);

  initScrollViewHeight();

  if (props.type === 0 || props.type === 3 || props.type === 7 || props.type === 8) {
    isShowUnit.value = false;
  }
  if (props.type === 0) {
    goodsListParams.warehouse_id =
      uni.getStorageSync("retailWarehouseId");
    pageData.extra_param.warehouse_id =
      uni.getStorageSync("retailWarehouseId");
  }

  if (props.warehouseData && props.warehouseData.warehouse) {
    console.log('设置仓库ID参数:', props.warehouseData.warehouse);
    goodsListParams.warehouse_id = props.warehouseData.warehouse;
    pageData.extra_param.warehouse_id = props.warehouseData.warehouse;
  }

  if (props.type === 1 || props.type === 2 || props.type === 6 || props.type === 5) {
    getGoodsList();
  }
});

      this.emitSelectedGoods(this.selectGoodsList);
      console.log(this.selectGoodsList);
onShow(() => {
  filterItemsForWarehouse();
});

      this.$emit("selectType", this.type);
    },
    // 添加检查并确保item字段存在的辅助方法
    ensureItemField(goods) {
      if (!goods) return goods;

      // 如果是数组，处理数组中的每一项
      if (Array.isArray(goods)) {
        return goods.map(item => this.ensureItemField(item));
      }

      // 处理单个商品对象
      const processedItem = { ...goods };

      // 确保item字段存在，优先使用id字段
      if (!processedItem.item) {
        processedItem.item = processedItem.id || processedItem.code;
        console.log(`商品 ${processedItem.item_name || processedItem.name} 缺少item字段，已设置为: ${processedItem.item}`);
      }

      return processedItem;
    },

    // 在发送选中商品前确保数据完整性
    emitSelectedGoods(goodsList) {
      // 确保所有商品都有item字段
      const processedList = this.ensureItemField(goodsList);

      // 检查处理后的数据
      const missingItemCount = processedList.filter(item => !item.item).length;
      if (missingItemCount > 0) {
        console.warn(`警告: 发送前仍有${missingItemCount}个商品缺少item字段`);
      }

      // 发送处理后的数据
      this.$emit("selectedGoodsList", processedList);
    },
  },
};

</script>

<style lang="scss" scoped>
.goods_item_disabled {
  opacity: 0.7;
  background: #f5f5f5;
}

.text-disabled {
  color: #999 !important;
}

.disabled-tag {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  display: flex;
  align-items: center;
  padding: 4rpx 10rpx;
  background: #f0f0f0;
  border-radius: 4rpx;

  text {
    font-size: 24rpx;
    color: #999;
    margin-left: 4rpx;
  }
}

.goods_theme {
  position: relative;
}

.corner-bg {
  position: absolute;
  top: -10px;
  left: -14px;
}

.goodsList {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

  .scroll-Y {
    width: 100%;
  }

  .goods_item {
    width: 90%;
    background-color: #fff;
    margin: 10rpx auto;
    padding: 20rpx;
    cursor: pointer;
    position: relative;

    .goods_theme {
      width: 100%;
      // height: 40%;
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: row;

      .select-goods {
        position: absolute;
        right: 0;

        top: 0;
      }

      .goods_img {
        width: 100rpx;
        height: 100rpx;
        margin: 10rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .goods_title {
        width: 70%;
        height: 100%;
        display: flex;
        // align-items: center;
        // justify-content: center;
        flex-direction: column;

        .goods_name {
          font-size: 30rpx;
          font-weight: bold;
          display: flex;
          align-items: center;

          ::v-deep .u-tag--primary {
            background-color: #62c8ee;
            border-width: 1px;
            border-color: #62c8ee;
          }

          ::v-deep .u-tag--mini {
            height: 18px;
            /* line-height: 22px; */
            padding: 0 5px;
          }

          .goods_id {
            font-size: 24rpx;
            font-weight: bold;
          }
        }
      }
    }

    .goods_info {
      display: flex;

      .goods_attributes {
        width: 50%;
        font-size: 24rpx;
        display: flex;
        align-content: flex-start;
        align-items: flex-start;
        flex-direction: column;

        .goods_attribute {
          display: flex;
          align-items: center;
          margin-top: 10rpx;
        }
      }
    }
  }

  .loading-more,
  .no-more,
  .empty {
    text-align: center;
    padding: 20rpx;
    color: #999;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .status-text {
      margin-left: 10rpx;
    }
  }
}

.goods_attribute_name {
  width: 60px;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.popup-title-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  position: relative;
}

.popup-title {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .popup-title-text {
    font-size: 32rpx;
    font-weight: bold;
  }
}

.sales-item {
  width: 95%;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  margin: 0 auto;
  border-bottom: 1px solid #eee;

  text {
    margin-bottom: 8rpx;
    font-size: 28rpx;
  }

  text:first-child {
    font-weight: bold;
    color: #333;
  }

  text:nth-child(2) {
    color: #666;
  }

  text:last-child {
    color: #999;
    font-size: 24rpx;
  }
}

.cumulative {
  height: 50rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: red;
  font-size: 28rpx;
}
</style>
