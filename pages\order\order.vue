<template>
	<view class="container">
		<!-- 聊天区 -->
		<view class="chatArea">
			<!-- 订单信息展示 -->
			<view class="orderInformation">
				<view class="basicInformation">
					<span class="text">基本信息</span>
				</view>
				<view class="auditStatus">
					<span class="text">审核情况</span>
				</view>
				<view class="handleButton">
					<u-button type="primary" plain shape="circle" class="but">提交</u-button>
					<u-button type="info" plain shape="circle" class="but">编辑</u-button>
					<u-button type="error" plain shape="circle" class="but">删除</u-button>
				</view>
			</view>
			<!-- 助手聊天记录 -->
			<view class="assistant">
				<!-- ai记录 -->
				<view class="aiChatRecord chatRecord">
					<view class="avatar">
						<img src="@/static/img/aiAssistant.png" alt="" />
					</view>
					<view class="assistantTextContent">
						<text>根据“苹果”，“现在有多少个”等关键词为你生成表单</text>
						<view class="openHistoricalDialogue" @click="openHistory">
							<i-history theme="outline" size="11" fill="#ff0000" />
							<text>查看对话历史</text>
						</view>
					</view>

				</view>
			</view>
		</view>
		<!-- 输入区 -->
		<view class="inputArea">
			<Input class="inputContent" :isShowCamera="true" />

		</view>
		<!-- 本次对话记录弹出框 -->
		<historyConversationRecord v-model:show="show" :dialogRecords="dialogRecords"/>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Input from '@/components/input/input.vue';
import historyConversationRecord from '@/components/historyConversationRecord/historyConversationRecord.vue';

const show = ref(false);
const dialogRecords = ref([
	{
		userRecord:'亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
		aiRecord:'根据“总共”“商品”等关键词为你生成表单'
	},
	{
		userRecord:'亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
		aiRecord:'根据“总共”“商品”等关键词为你生成表单'
	},
	{
		userRecord:'亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
		aiRecord:'根据“总共”“商品”等关键词为你生成表单'
	},
	{
		userRecord:'亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
		aiRecord:'根据“总共”“商品”等关键词为你生成表单'
	},
]);

const openHistory = () => {
	show.value = true;
};

const updateParentShow = (newMessage: boolean) => {
	show.value = newMessage;
};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.chatArea {
		height: 100%;
		position: relative;
	}

	.text {
		color: #0f40f5;
		border-bottom: #0f40f5 3px solid;
	}

	.orderInformation {
		margin: 20rpx auto;
		padding: 20rpx;
		border-radius: 20rpx;
		width: 80%;
		height: 80%;
		background-color: #efefef;
		border: #a2a2a2 1px solid;

		.basicInformation {
			height: 50%;
		}

		.auditStatus {
			height: 50%;
		}
	}

	.auditStatus {
		margin-top: 30rpx;
	}

	.handleButton {
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-direction: row;
		margin-top: 20rpx;
	}

	.but {
		width: 200rpx;
		margin: 3rpx;
	}

	::v-deep .u-size-default {
		height: 60rpx !important; // 优先级更高，确保覆盖子组件的样式
		line-height: 60rpx; // 同步调整行高
	}

	::v-deep .u-button--plain.u-button--info {
		color: #000000 !important;
	}

	::v-deep .u-button {
		height: 30px !important; // 优先级更高，确保覆盖子组件的样式
		width: 30% !important;
	}

	::v-deep .u-button--info {
		border-color: #323335 !important;
		border-width: 1px;
		border-style: solid;
	}


	.avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		overflow: hidden;

		img {
			width: 100%;
			height: 100%;
		}
	}

	.aiChatRecord {
		margin: 20rpx auto;
		padding: 20rpx;
		width: 80%;
		position: absolute;
		bottom: 14%;
		left: 0;
	}

	.chatRecord {
		display: flex;
		margin: 10rpx;
	}

	.assistantTextContent {
		width: 70%;
		margin-left: 20rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		background-color: #ecf5ff;
		font-size: 28rpx;

		.openHistoricalDialogue {
			display: flex;
			align-items: center;
			margin-top: 10rpx;
			color: red;
			font-size: 23rpx;
			font-weight: 400;
			flex-direction: row-reverse
		}
	}

	.inputArea {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;


		input {
			width: 90%;
			height: 100%;
		}
	}
</style>