import request from '@/utils/request.ts';
import store from '@/store';

// 定义销售订单数据接口
interface SalesOrderData {
  id?: string; // 销售订单ID，可选，因为新增时可能没有
  // 其他销售订单相关字段
  [key: string]: any;
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取销售订单列表
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getSalesOrderList = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesorders/`, data);
};

/**
 * 获取销售订单详情
 * @param {string} id - 销售订单ID
 * @param {object} params - 查询参数
 * @returns {Promise<any>}
 */
export const getSalesOrderDetail = (id: string, params: object = {}): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesorders/${id}/`, params);
};

/**
 * 新增销售订单
 * @param {SalesOrderData} data - 销售订单数据
 * @returns {Promise<any>}
 */
export const createSalesOrder = (data: SalesOrderData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/salesorders/`, data);
};

/**
 * 更新销售订单
 * @param {SalesOrderData} data - 销售订单数据
 * @returns {Promise<any>}
 */
export const updateSalesOrder = (data: SalesOrderData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/salesorders/${data.id}/`, data);
};

/**
 * 删除销售订单
 * @param {SalesOrderData} data - 包含销售订单ID的数据
 * @returns {Promise<any>}
 */
export const deleteSalesOrder = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/salesorders/${data.id}/`);
};

/**
 * 搜索销售订单
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchSalesOrder = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesorders/search/`, data);
};

/**
 * 获取销售订单库存信息
 * @param {string} id - 销售订单ID
 * @returns {Promise<any>}
 */
export const getSalesOrderStock = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/salesorders/${id}/stock/`);
};
