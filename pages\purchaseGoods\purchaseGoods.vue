<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="2" @search-result="handleSearchResult" />

    <!-- 进货商品单列表 -->
    <scroll-view scroll-y class="purchase-list" :style="{ height: scrollViewHeight }" @scrolltolower="loadPurchaseGoodsOrders">
      <view class="purchase-item attribute_font" v-for="(item, index) in purchaseGoodsList" :key="index"
        @click="navigatorEdit(item)">
        <!-- 状态标签等可复用 -->
        <view class="orange-tag" v-if="item.in_status != 'cancelled'">
          {{
            item.in_status == "draft"
              ? "暂存"
              : item.in_status == "completed"
                ? "已提交"
                : ""
          }}
        </view>
        <view class="red-tag">
          {{ getInTypeText(item.in_type) }}
        </view>
        <view class="item-content">
          <view class="info-row">
            <text class="label">供应商</text>
            <text class="value">：{{ item.supplier_name }}</text>
          </view>
          <view class="info-row">
            <text class="label">商品</text>
            <text class="value">：{{
              item.short_desc
            }}</text>
            <!-- 其它字段可根据实际调整 -->
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">操作员</text>
              <text class="value">：{{ item.handler }}</text>
            </view>
            <view>
              <text class="sub-label">数量</text>
              <text class="value">：{{ item.items_count }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">金额合计</text>
              <text class="value">：{{ $toFixed(item.actual_total_cost, 2) }}</text>
            </view>
            <view>
              <text class="sub-label">预付款</text>
              <text class="value">：{{ $toFixed(item.deposit_deduction, 2) }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">已付款</text>
              <text class="value">：{{ $toFixed(item.pay_amount, 2) }}</text>
            </view>
            <view>
              <text class="sub-label">欠款</text>
              <text class="value">：{{ $toFixed(item.unpaid_amount, 2) }}</text>
            </view>
          </view>
        </view>
        <view class="info-bottom">
          <view class="info-row" style="margin-bottom: 0">
            <text class="label">申请日期</text>
            <text class="value">：{{ item.in_date }}</text>
          </view>
          <button class="share-btn" open-type="share" :data-item="item">
            分享
          </button>
        </view>
      </view>
    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="true" :jumpToId="3" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { onLoad, onShow, onShareAppMessage } from '@dcloudio/uni-app';
import search from "../../components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "../../components/interactive.vue";
import { getPurchaseGoods } from "@/api/purchaseGoods";

const purchaseGoodsList = ref<any[]>([]);
const purchaseGoodsListParams = reactive({
  page: 1,
  page_size: 10,
});
const hasMore = ref(true);
const scrollViewHeight = ref("500px"); // 默认值，后面会动态设置
const loadStatus = ref('loadmore'); // 新增：加载更多状态

const getInTypeText = (type: string) => {
  const inTypeMap: { [key: string]: string } = {
    purchase_in: "采购入库",
    return_in: "退货入库",
    adjustment_in: "调拨入库",
    initial_in: "期初入库",
    assembly_in: "组装入库",
    other_in: "其他入库",
    unpaid: "未付款",
  };
  return inTypeMap[type] || type;
};

const handleSearchResult = (newVal: any) => {
  console.log(newVal);

  if (newVal !== null) {
    purchaseGoodsList.value = newVal;
    uni.showToast({
      title: "搜索到" + newVal.length + "条结果",
      icon: "none",
    });
  } else {
    uni.showToast({
      title: "搜索到0条结果",
      icon: "none",
    });
    loadPurchaseGoodsOrders();
  }
};

const navigatorEdit = (item: any) => {
  uni.navigateTo({
    url:
      "/pages/purchaseGoods/editPurchaseGoods/editPurchaseGoods?data=" +
      JSON.stringify(item),
  });
};

const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44; // 导航栏高度
    const statusBarHeight = info.statusBarHeight; // 状态栏高度
    const searchHeight = (screenWidth * 80) / 750; // 搜索框高度 80rpx
    const inputHeight = (screenWidth * 90) / 750; // 底部输入区域高度 90rpx
    // 没有 selectBtnHeight
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};

const loadPurchaseGoodsOrders = () => {
  if (!hasMore.value) return;
  const params = {
    page: purchaseGoodsListParams.page,
    page_size: purchaseGoodsListParams.page_size,
  };
  getPurchaseGoods(params).then((res: any) => {
    console.log(res);

    if (res.code == 0) {
      if (purchaseGoodsListParams.page === 1) {
        purchaseGoodsList.value = res.data.results;
      } else {
        purchaseGoodsList.value = purchaseGoodsList.value.concat(
          res.data.results
        );
      }
      hasMore.value = purchaseGoodsList.value.length < (res.data.count || 0);
      purchaseGoodsListParams.page++;
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  });
};

onLoad(() => {
  initScrollViewHeight();
  // this.loadPurchaseGoodsOrders();
});

onShow(() => {
  purchaseGoodsListParams.page = 1;
  hasMore.value = true;
  loadPurchaseGoodsOrders();
});

onShareAppMessage((res) => {
  if (res.from === "button") {
    // 来自页面内分享按钮
    const item = res.target.dataset.item;
    return {
      title: `进货商品单分享`,
      path: "/pages/purchaseGoods/purchaseGoods",
      imageUrl: "", // 可以设置自定义分享图片
    };
  }
  // 来自右上角分享
  return {
    title: "进货商品单分享",
    path: "/pages/purchaseGoods/purchaseGoods",
    imageUrl: "", // 可以设置自定义分享图片
  };
});

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.purchase-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.purchase-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.item-content {
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 400rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 80rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.sub-value {
  color: #333;
  margin-right: 20rpx;
}

.info-bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.share-btn {
  color: #4080e8;
  font-size: 28rpx;
  text-align: right;
  padding: 10rpx 0;
  background: none;
  border: none;
  line-height: 1;
  margin: 0;
}

.share-btn::after {
  border: none;
}
</style>