import request from '@/utils/request.ts';
import store from '@/store';

// 定义商品类别数据接口
interface GoodsTypeData {
  id?: string; // 商品类别ID，可选，因为新增时可能没有
  name: string; // 商品类别名称
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

console.log('当前账套名称：', getLedgerName());

/**
 * 获取商品类别信息
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getGoodsType = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/itemtypes/`, data);
};

/**
 * 添加商品类别信息
 * @param {GoodsTypeData} data - 商品类别数据
 * @returns {Promise<any>}
 */
export const addGoodsType = (data: GoodsTypeData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/itemtypes/`, data);
};

/**
 * 删除商品类别信息
 * @param {object} data - 包含商品类别ID的数据
 * @returns {Promise<any>}
 */
export const deleteGoodsType = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/itemtypes/${data.id}/`);
};

/**
 * 编辑商品类别信息
 * @param {GoodsTypeData} data - 商品类别数据
 * @returns {Promise<any>}
 */
export const editGoodsType = (data: GoodsTypeData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/itemtypes/${data.id}/`, data);
};
