export default {
  // 打开选择器
  openSelector(index) {
    if (this.isDisabled) {
      uni.showToast({ title: '信息禁止修改', icon: 'none' });
      return;
    }
    this.selectType = index;
    this.selectorShow = true;
  },
  // 关闭选择器
  closePopup() {
    this.selectorShow = false;
  },
  // 选择供应商或者客户
  // 方法说明：用于处理客户选择逻辑，当selectType为1时更新客户信息，并关闭选择器
  // 参数item: 包含客户信息的对象，应包含name和id字段
  // 返回值：无
  selectPartyA(item) {
    if (this.selectType === 0) {
      //选择供应商
      this.localInfo.supplier = item.id;
      this.localInfo.supplier_name = item.name;
    } else if (this.selectType === 1) {
      //选择客户
      this.localInfo.customer = item.id;
      this.localInfo.customer_name = item.name;
    }
    this.closePopup();
  },

  // 打开日期选择器
  openDatePicker() {
    if (this.isDisabled) {
      uni.showToast({ title: '信息禁止修改', icon: 'none' });
      return;
    }
    this.showDatePicker = true;
  },
  // 关闭日期选择器
  closeDatePicker() {
    this.showDatePicker = false;
  },
  // 确认选择的日期
  handleDateConfirm(date) {
    this.localInfo.expected_arrival_date = date;
    this.showDatePicker = false;
  },

  // 打开库存选择弹出层
  openInventorySelection() {
    if (this.isDisabled) {
      uni.showToast({ title: '信息禁止修改', icon: 'none' });
      return;
    }
    this.inventorySelectionShow = true;
  },

  // 关闭库存选择弹出层
  closeInventorySelection() {
    this.inventorySelectionShow = false;
  },

  // 接收库存弹出层的数据
  confirmInventory(data) {
    this.localInfo.warehouse = data.id;
    this.localInfo.warehouse_name = data.name;
  },

  // 删除仓库
  removeWarehouse() {
    this.localInfo.warehouse_name = "";
    this.localInfo.warehouse = "";
  },

}