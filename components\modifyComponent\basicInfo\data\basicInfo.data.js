export default function() {
  return {
    localInfo: JSON.parse(JSON.stringify(this.basicInfo)),
    selectorShow: false,
    selectType: 0,
    showDatePicker: false,
    CategoryShow: false, // 商品类别弹出层是否展示
    isExpandProductInfo: false,
    // 采购相关弹出层控制
    inventorySelectionShow: false, // 仓库选择弹出层显示状态
    accountSelectShow: false, // 结算账户弹出层
    otherExpensesShow: false, // 是否弹出其他费用弹出层
    // 表单验证规则
    rules: {
      name: [{
        required: true,
        message: '请输入商品名称',
        trigger: ['blur', 'change']
      }],
      item_type_name: [{
        required: true,
        message: '请选择商品类型',
        trigger: ['blur', 'change']
      }],
      "localInfo.supplier_name": {
        type: "string",
        required: true,
        message: "请选择供应商",
        trigger: ["blur", "change"],
      },
      "localInfo.warehouse_name": {
        type: "string",
        required: true,
        message: "请选择仓库",
        trigger: ["blur", "change"],
      },
      "localInfo.customer_name": {
        type: "string",
        required: true,
        message: "请选择客户",
        trigger: ["blur", "change"],
      }
    }
  };

}