import enIndex from './module/index/en.json';
import zhHansIndex from './module/index/zh-Hans.json';
import zhHantIndex from './module/index/zh-Hant.json';
import jaIndex from './module/index/ja.json';
import enApi from './module/api/en.json';
import zhHansApi from './module/general/zh-Hans.json'; // 注意：原文件这里是 zhHansApi，但路径指向 general/zh-Hans.json，我将保留原路径，并将其视为通用中文API翻译
import zhGeneral from './module/general/zh-Hans.json';
import zhHantApi from './module/api/zh-Hant.json';
import jaApi from './module/api/ja.json';
import enSchema from './module/schema/en.json';
import zhHansSchema from './module/schema/zh-Hans.json';
import zhHantSchema from './module/schema/zh-<PERSON>t.json';
import jaSchema from './module/schema/ja.json';
import enLocale from './module/locale/en.json';
import zhHansLocale from './module/locale/zh-Hans.json';
import zhHantLocale from './module/locale/zh-Hant.json';
import jaLocale from './module/locale/ja.json';

// 定义消息对象的类型
interface Messages {
  [key: string]: any;
}

const messages: Messages = {
  en: {
    ...enIndex,
    ...zhGeneral, // 这里的 zhGeneral 可能是笔误，如果需要英文通用翻译，应该有对应的 en.json
    ...enApi,
    ...enSchema,
    ...enLocale,
  },
  'zh-Hans': {
    ...zhHansIndex,
    ...zhHansApi,
    ...zhHansSchema,
    ...zhHansLocale,
  },
  'zh-Hant': {
    ...zhHantIndex,
    ...zhHantApi,
    ...zhHantSchema,
    ...zhHantLocale,
  },
  ja: {
    ...jaIndex,
    ...jaApi,
    ...jaSchema,
    ...jaLocale,
  },
};

export default messages;
