<template>
  <view class="a-icon-svg" :style="[boxStyle]">
    <!-- #ifdef APP-NVUE -->
    <image class="a-icon-pack-image" :style="[boxStyle]" :src="png" mode="scaleToFill"></image>
    <web-view v-if="isShow" class="__wh-0" :ref="_uid"  @onPostMessage="changeMessage" 
    src="/uni_modules/icon-park/hybrid/html/index.html" ></web-view>
    <!-- #endif -->
    <!-- #ifndef APP-NVUE -->
    <image class="a-icon-pack-image" :src="url" mode="aspectFit"></image>
    <!-- #endif -->
  </view>
</template>

<script>
import { IconWrapper , genContent } from '../runtime'
export default genContent('i-bless', IconWrapper('i-bless',(props)=>(
    '<?xml version="1.0" encoding="UTF-8"?>'
    + '<svg width="' + props.sizeWithUnit + '" height="' + props.sizeWithUnit + '" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">'
        + '<path d="M21 24.713V5.9354C21 4.86651 20.1335 4 19.0646 4C18.152 4 17.3634 4.63752 17.1722 5.52987L13.0582 24.7286C13.0196 24.9087 12.9563 25.0826 12.8701 25.2453L5.55423 39.0642C4.84906 40.3962 5.81466 42 7.32181 42H15.4056C16.3384 42 17.1474 41.3551 17.3553 40.4456L20.4803 26.7739L20.9193 25.2754C20.9728 25.0927 21 24.9034 21 24.713Z" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
        + '<path d="M13 25L20 28" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
        + '<path d="M27 24.713V5.9354C27 4.86651 27.8665 4 28.9354 4C29.848 4 30.6366 4.63752 30.8278 5.52987L34.9418 24.7286C34.9804 24.9087 35.0437 25.0826 35.1299 25.2453L42.4458 39.0642C43.1509 40.3962 42.1853 42 40.6782 42H32.5944C31.6616 42 30.8526 41.3551 30.6447 40.4456L27.5197 26.7739L27.0807 25.2754C27.0272 25.0927 27 24.9034 27 24.713Z" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
        + '<path d="M35 25L28 28" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
    + '</svg>'
)))
</script>

<style lang="scss" scoped>
@import '../../style/index.css';
</style>
