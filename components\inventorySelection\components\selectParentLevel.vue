<template>
  <view>
    <u-popup :show="selectShow" @close="onClose" @open="onOpen">
      <view class="select-parent-popup">
        <view class="header">
          <view class="cancel" @click="onClose">取消</view>
          <view class="title">选择上级目录</view>
          <view class="confirm" @click="onConfirm">确定</view>
        </view>
        <scroll-view class="list" scroll-y @scrolltolower="onReachBottom" style="height: 600rpx">
          <view v-for="item in options" :key="item.id" class="radio-item" @click="selectItem(item)">
            <view class="radio-outer">
              <view class="radio-inner" v-if="selectedValue.id === item.id"></view>
            </view>
            <view class="radio-label">{{ item.name }}</view>
          </view>
          <u-loadmore :status="loadingStatus" />
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { getWarehouses } from "@/api/warehouse";

/**
 * 仓库项接口定义
 */
interface WarehouseItem {
  id: number;
  name: string;
  is_parent: boolean;
}

/**
 * 定义组件属性
 */
const props = defineProps({
  /**
   * 控制弹窗显示隐藏
   */
  selectShow: {
    type: Boolean,
    default: false
  }
});

/**
 * 定义组件事件
 */
const emit = defineEmits(['close', 'confirm']);

/**
 * 当前选中的上级目录值
 */
const selectedValue = ref<WarehouseItem | ''>('');
/**
 * 上级目录选项列表
 */
const options = ref<WarehouseItem[]>([]);
/**
 * 分页参数
 */
const parentParam = reactive({
  page: 1,
  page_size: 10,
});
/**
 * 加载状态：'loadmore' | 'loading' | 'nomore'
 */
const loadingStatus = ref<'loadmore' | 'loading' | 'nomore'>('loadmore');

/**
 * 监听 selectShow 属性变化，控制弹窗打开和重置数据
 */
watch(() => props.selectShow, (val: boolean) => {
  if (val) {
    onOpen();
  } else {
    selectedValue.value = '';
    options.value = [];
    parentParam.page = 1;
    loadingStatus.value = 'loadmore';
  }
});

/**
 * 弹窗打开时的处理函数，重置分页并加载数据
 */
const onOpen = (): void => {
  parentParam.page = 1;
  options.value = [];
  loadingStatus.value = 'loading';
  getParentWarehouses();
};

/**
 * 获取上级仓库列表
 */
const getParentWarehouses = (): void => {
  loadingStatus.value = 'loading';
  getWarehouses(parentParam).then((res: { data: { results: WarehouseItem[] } }) => {
    const list: WarehouseItem[] = (res.data.results || []).filter((item: WarehouseItem) => item.is_parent);

    if (parentParam.page === 1) {
      options.value = list;
    } else {
      options.value = options.value.concat(list);
    }
    if (list.length < parentParam.page_size) {
      loadingStatus.value = 'nomore';
    } else {
      loadingStatus.value = 'loadmore';
      parentParam.page++;
    }
  }).catch(() => {
    loadingStatus.value = 'loadmore';
  });
};

/**
 * 滚动到底部加载更多数据
 */
const onReachBottom = (): void => {
  if (loadingStatus.value === 'loadmore') {
    getParentWarehouses();
  }
};

/**
 * 关闭弹窗
 */
const onClose = (): void => {
  emit('close');
};

/**
 * 选择列表项
 * @param item 选中的仓库项
 */
const selectItem = (item: WarehouseItem): void => {
  selectedValue.value = item;
};

/**
 * 确认选择
 */
const onConfirm = (): void => {
  if (!selectedValue.value) {
    uni.showToast({ title: '请选择上级目录', icon: 'none' });
    return;
  }
  emit('confirm', selectedValue.value);
  emit('close');
};
</script>

<style lang="scss" scoped>
.select-parent-popup {
  background: #fff;
  width: 100%;
  height: 800rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx 10rpx 30rpx;
  border-bottom: 1px solid #eee;

  .cancel {
    font-size: 28rpx;
    display: flex;
    align-items: center;
  }

  .title {
    font-size: 30rpx;
    font-weight: bold;
    color: #222;
  }

  .confirm {
    color: #3982cf;
    font-size: 28rpx;
    display: flex;
    align-items: center;
  }
}

.list {
  padding: 20rpx 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
  font-size: 28rpx;
  cursor: pointer;
}

.radio-outer {
  width: 28rpx;
  height: 28rpx;
  border: 2rpx solid #3982cf;
  border-radius: 50%;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  background: #3982cf;
  border-radius: 50%;
}

.radio-label {
  color: #222;
}
</style>
