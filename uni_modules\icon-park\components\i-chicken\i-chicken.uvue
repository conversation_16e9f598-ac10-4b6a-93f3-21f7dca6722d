<template>
  <view class="a-icon-svg" :style="[boxStyle]">
    <web-view v-if="isShow" class="__wh-0" :id="_uid" @message="changeMessage"
      src="/uni_modules/icon-park/hybrid/html/index.html" />
    <image :src="png"  class="a-icon-pack-image" :style="[boxStyle]" :mode="mode" :fade-show="fadeShow"
      :lazy-load="lazyLoad" :show-menu-by-longpress="showMenuByLongpress" :draggable="draggable" @error="changeError"
      @load="changeLoad" />
  </view>
</template>

<script>
  export default {
    data() {
      return {
        _uid: this.uuid(32) as string,
        png: "" as any | null,
        webviewContext: null as WebviewContext | null,
        isShow: true as boolean,
        rtl: false,
        colors1: {
          outline: {
            fill: '#333',
            background: 'transparent'
          },
          filled: {
            fill: '#333',
            background: '#FFF'
          },
          twoTone: {
            fill: '#333',
            twoTone: '#2F88FF'
          },
          multiColor: {
            outStrokeColor: '#333',
            outFillColor: '#2F88FF',
            innerStrokeColor: '#FFF',
            innerFillColor: '#43CCF8'
          }
        },
        prefix: 'i'

      }
    },
    props: {
      size: {
        type: String,
        default: '24',
        desc: '图标的大小，即宽高的值'
      },
      strokeWidth: {
        type: String,
        default: '4',
        desc: '线条宽度'
      },
      theme: {
        type: String,
        default: 'outline',
        validator(value : String) : boolean {
          return ['outline', 'filled', 'two-tone', 'multi-color'].includes(
            value
          )
        }
      },
      strokeLinecap: {
        type: String,
        default: 'round',
        validator(value : String) : boolean {
          return ['butt', 'round', 'square'].includes(value)
        },
        desc: '描边端点类型'
      },
      strokeLinejoin: {
        type: String,
        default: 'round',
        validator(value : String) : boolean {
          return ['miter', 'round', 'bevel'].includes(value)
        },
        desc: '描边连接类型'
      },
      fill: {
        type:  Array as PropType<string[]>,
        default: ['#000000']
      },
      width: {
        type: String,
        default: "48"
      },
      height: {
        type: String,
        default: "48"
      },
      mode: {
        type: String,
        default: "scaleToFill"
      },
      fadeShow: {
        type: Boolean,
        default: false
      },
      lazyLoad: {
        type: Boolean,
        default: false
      },
      showMenuByLongpress: {
        type: Boolean,
        default: false
      },
      draggable: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      fillColor() : string[] {
        let res: string[] =  this.fill
        return res
      },
      outStrokeColor() : string {
        return typeof this.fillColor[0] === 'string'
          ? this.fillColor[0]
          : 'currentColor'
      },
      outFillColor() : string {
        return typeof this.fillColor[1] === 'string'
          ? this.fillColor[1]
          : 'currentColor'
      },
      innerStrokeColor() : string {
        return typeof this.fillColor[2] === 'string'
          ? this.fillColor[2]
          : '#FFF'
      },
      innerFillColor() : string {
        return typeof this.fillColor[3] === 'string'
          ? this.fillColor[3]
          : '#43CCF8'
      },
      sizeWithUnit() : string {
        let size = this.size + ''
        if (/d$/.test(this.size)) {
          size = this.size + 'px'
        } else if (size.endsWith('rpx')) {
          size = uni.rpx2px(parseInt(size)) + 'px'
        }
        return size
      },
      colors() : string[] {
        let res: string[] = []
        if(this.theme === 'outline') {
          res = this.outline()
        };
        if(this.theme === 'filled') {
          res = this.filled()
        };
        if(this.theme === 'two-tone') {
          res = this.twoTone()
        };
        if(this.theme === 'multi-color') {
          res = this.multi()
        };
        
        return res;
      },
      boxStyle() : UTSJSONObject {
        const style = {
          width: this.sizeWithUnit,
          height: this.sizeWithUnit
        }
        return style
      },
    },
    methods: {
      multi(): string[] {
        return [
          this.outStrokeColor,
          this.outFillColor,
          this.innerStrokeColor,
          this.innerFillColor
        ]
      },
      twoTone(): string[] {
        return [
          this.outStrokeColor,
          this.outFillColor,
          this.outStrokeColor,
          this.outFillColor
        ]
      },
      filled(): string[] {
        return [this.outStrokeColor, this.outStrokeColor, '#FFF', '#FFF']
      },
      outline(): string[] {
        return [this.outStrokeColor, 'none', this.outStrokeColor, 'none']
      },
      changeMessage(event : WebViewMessageEvent) {
       const props = this
          const src = 
          
    '<?xml version="1.0" encoding="UTF-8"?>'
    + '<svg width="' + props.sizeWithUnit + '" height="' + props.sizeWithUnit + '" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">'
        + '<path d="M35.9786 15.4998C35.4536 15.0194 34.8809 14.5921 34.2688 14.2248L32.8034 13.3456C31.4442 12.53 29.9497 11.965 28.3909 11.6774L27.8672 11.5808C25.0469 11.0603 22.3297 10.0961 19.8159 8.7262C19.586 7.71021 18.7942 6.86455 17.7106 6.61834C16.2072 6.27672 14.7114 7.21858 14.3698 8.72205C14.0282 10.2255 14.97 11.7212 16.4735 12.0629C16.5995 12.0915 16.7255 12.1111 16.8507 12.1221C18.5056 13.7407 19.9052 15.6035 21 17.6467" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
        + '<path d="M11.3067 25.0192C11.0447 25.3012 8 28.6506 8 32C8 35.5 9.55844 37 11 38C12.4416 39 14.7208 40 16.6104 40C18.5 40 28.5 41 33 40C37.5 39 38 38 40 36C41.0815 34.9185 41.882 33.3983 42.3892 32.2301C42.8802 31.0991 43.2654 29.9245 43.5649 28.7284C43.9246 27.2922 44.2883 25.2299 44 23.5C43.5 20.5 42 18.5 37 16C32.3958 13.6979 28.3532 14.7876 27.7421 14.9745C27.6868 14.9914 27.6352 15.0095 27.5806 15.0287C26.7806 15.3116 20.3163 17.6254 17.5714 19.5C14.7687 21.414 11.6092 24.7019 11.3428 24.981C11.3293 24.9951 11.3201 25.0048 11.3067 25.0192Z" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
        + '<path d="M6.10263 20.0094C5.97741 19.9983 5.85146 19.9787 5.72546 19.9501C4.222 19.6085 3.28014 18.1127 3.62176 16.6093C3.96338 15.1058 5.45912 14.1639 6.96259 14.5055C8.04613 14.7518 8.83798 15.5974 9.0679 16.6134C11.5816 17.9833 14.2989 18.9475 17.1192 19.468L17.6428 19.5646C19.2016 19.8522 20.6961 20.4172 22.0553 21.2328L23.5207 22.112C24.5005 22.6999 25.3793 23.4416 26.1234 24.3087L26.5288 24.7812C27.7526 26.2073 28.3101 28.0877 28.0613 29.9504L28.0331 30.1621C27.8636 31.4312 27.2894 32.6118 26.3957 33.5287L25.9345 34.0018C24.4094 35.5665 22.2148 36.2854 20.0595 35.9261C18.3925 35.6483 16.8493 34.8707 15.6342 33.6962L14.8952 32.9819C14.301 32.4076 13.7859 31.7568 13.3634 31.0466L11.1909 27.3947L10.5574 26.1247C9.42238 23.8489 7.91571 21.7826 6.10263 20.0094Z" fill="' + props.colors[1] + '" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '" stroke-linejoin="' + props.strokeLinejoin + '"/>'
    + '</svg>'

       
        if ((event.detail.data?.get("isInitialize") ?? false) == true) {
          this.webviewContext = uni.createWebviewContext(this._uid, this);
          this.webviewContext?.evalJS("onReceiveSvg('"+src+"')");
        } else {
          this.png = event.detail.data?.get("png");
          this.isShow = false;
        };
      },
      uuid(length : number) : string {
        let uuid : string = "";
        let chars : string[] = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        for (let i = 0; i < length; i++) { uuid += chars[Math.floor(Math.random() * chars.length)]; };
        return uuid;
      },
      changeError(event : ImageErrorEvent) {
        this.$emit("error", event)
      },
      changeLoad(event : ImageLoadEvent) {
        this.$emit("load", event)
      }
    }
  }
</script>

<style scoped>
  @import '../../style/index.css';
</style>
