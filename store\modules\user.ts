import {
  bindPhoneNumber,
  validatePhoneNumber,
  loginGetInfo
} from '@/api/user'; // 假设 user.js 将变为 user.ts，因此不需要 .js 扩展名

// 定义状态接口
interface UserState {
  ledger_name: string;
  ledger_schema_name: string;
  enterprise_name: string;
  access_token: string;
  refresh_token: string;
}

// 持久化存储的key
const STORAGE_KEY = 'user_state';

// 从本地存储恢复state
const getStorageState = (): UserState | null => {
  try {
    const storage = uni.getStorageSync(STORAGE_KEY);
    return storage ? JSON.parse(storage) : null;
  } catch (e) {
    console.error('获取本地存储失败:', e);
    return null;
  }
};

// 初始state
const getDefaultState = (): UserState => ({
  ledger_name: '',
  ledger_schema_name: '',
  enterprise_name: '',
  access_token: '',
  refresh_token: '',
});

// 合并本地存储的state和默认state
const state: UserState = {
  ...getDefaultState(),
  ...getStorageState(),
};

// 保存state到本地存储
const saveStateToStorage = (state: UserState) => {
  try {
    uni.setStorageSync(STORAGE_KEY, JSON.stringify(state));
  } catch (e) {
    console.error('保存到本地存储失败:', e);
  }
};

const mutations = {
  /**
   * 登录后初始化数据
   * @param {UserState} state - Vuex state
   * @param {object} data - 包含 access_token 和 refresh_token 的数据
   */
  tokenInit(state: UserState, data: { access_token: string; refresh_token: string }) {
    if (!data) {
      console.error('传递的数据为空');
      return;
    }
    state.access_token = data.access_token || '';
    state.refresh_token = data.refresh_token || '';
    // 保存到本地存储
    saveStateToStorage(state);
  },
  /**
   * 初始化用户信息
   * @param {UserState} state - Vuex state
   * @param {object} data - 包含用户信息的数据
   */
  infoInit(state: UserState, data: { ledger_name: string; schema_name: string; ledger_schema_name: string }) {
    if (!data) {
      console.error('传递的数据为空');
      return;
    }
    state.ledger_name = data.ledger_name || '';
    state.enterprise_name = data.schema_name || ''; // 假设 schema_name 映射到 enterprise_name
    state.ledger_schema_name = data.ledger_schema_name || '';
    // 保存到本地存储
    saveStateToStorage(state);
  },
  /**
   * 重置state
   * @param {UserState} state - Vuex state
   */
  resetState(state: UserState) {
    Object.assign(state, getDefaultState());
    // 清除本地存储
    uni.clearStorageSync();
  },
};

const actions = {
  /**
   * 校验手机号是否存在
   * @param {object} { commit } - Vuex commit
   * @param {any} data - 手机号数据
   * @returns {Promise<any>}
   */
  async validatePhoneNumber({ commit }: { commit: Function }, data: any): Promise<any> {
    try {
      const res = await validatePhoneNumber(data);
      console.log('validatePhoneNumber resp', res);
      // 将token和refresh_token存储到本地
      uni.setStorageSync('server_access_token', res.access_token);
      uni.setStorageSync('server_refresh_token', res.refresh_token);

      commit('tokenInit', res); // 在接口调用成功后，提交 mutation
      return res;
    } catch (err) {
      console.error('登录失败:', err); // 捕获并处理错误
      throw err;
    }
  },
  /**
   * 绑定手机号
   * @param {object} { commit } - Vuex commit
   * @param {any} data - 手机号绑定数据
   * @returns {Promise<any>}
   */
  async bindPhoneNumber({ commit }: { commit: Function }, data: any): Promise<any> {
    try {
      const res = await bindPhoneNumber(data); // 调用 login 接口，并将 data 传递给它
      // 将token和refresh_token存储到本地
      if (res.access_token) {
        // 有可能刷新 token
        uni.setStorageSync('server_access_token', res.access_token);
        uni.setStorageSync('server_refresh_token', res.refresh_token);
        commit('tokenInit', res); // 在接口调用成功后，提交 mutation
      }
      return res;
    } catch (err) {
      console.error('登录失败:', err); // 捕获并处理错误
      throw err;
    }
  },

  /**
   * 获取企业以及账套信息
   * @param {object} { commit } - Vuex commit
   * @returns {Promise<any>}
   */
  async loginGetInfo({ commit }: { commit: Function }): Promise<any> {
    try {
      console.log('尝试获取用户信息');
      const res = await loginGetInfo();
      console.log('获取用户信息成功，响应数据:', res);

      // 设置全局变量
      uni.setStorageSync('tenant_id', res.ledger_name);
      console.log('设置LEDGER_TAG:', res.ledger_name);

      // 直接传递res，不需要访问data属性
      commit('infoInit', res);
      return res;
    } catch (err) {
      console.error('获取用户信息失败:', err);
      uni.showToast({
        title: '获取用户信息失败',
        icon: 'none',
      });
      throw err;
    }
  },

  /**
   * 退出登录
   * @param {object} { commit } - Vuex commit
   */
  logout({ commit }: { commit: Function }) {
    commit('resetState');
    // 可以添加其他退出登录的逻辑
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
