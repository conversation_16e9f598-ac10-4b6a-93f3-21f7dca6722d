<template>
    <view>
        <u-popup v-model:show="productCategoryShow" mode="bottom" @close="closeProductCategory">
            <view class="container">
                <view class="categoryConfirmationBtn">
                    <view @click="closeProductCategory">取消</view>
                    <view class="popup_title">选择商品类别</view>
                    <view class="blueFont" @click="confirmCategory">确定</view>
                </view>
                <view class="divider"></view>
                <scroll-view 
                    class="content" 
                    scroll-y 
                    @scrolltolower="onReachBottom"
                >
                    <!-- 一级分类循环 -->
                    <view class="category" v-for="item in parentList" :key="item.id">
                        <!-- 单选区域 -->
                        <view class="selectArea">
                            <!-- 一级分类只展现个名字和圆框提供点击 -->
                            <view class="levelOne">
                                <view class="levelOne_left">
                                    <view class="circleTool" @click="selected(item.name, item.id)" :class="{ 'active-circle': categoryId === item.id }">
                                        <view :class="{ 'selected-circle': categoryId === item.id }"></view>
                                    </view>
                                    <view class="levelOneName" :class="{ 'active-text': categoryId === item.id }" @click="selected(item.name, item.id)">{{ item.name }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 加载更多 -->
                    <view class="loading-more" v-if="isLoading">
                        <u-loading-icon></u-loading-icon>
                        <text class="loading-text">正在加载...</text>
                    </view>
                </scroll-view>
            </view>
        </u-popup>
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { getGoodsType } from "@/api/goodsType";

// 定义接口
interface CategoryItem {
  id: string;
  name: string;
  parent: string | null;
}

// 定义 props
const props = defineProps({
  selePdCaShow: {
    type: Boolean,
    default: false,
  },
});

// 定义 emits
const emit = defineEmits(['update:show', 'update:category']);

// 响应式数据
const productCategoryShow = ref(props.selePdCaShow); // 商品类别弹出层是否展示
const categoryName = ref<string | null>(null); // 分类选中值
const categoryId = ref<string | null>(null); // 分类选中值id
const pageParams = reactive({
  page: 1,
  page_size: 10,
});
const isLoading = ref(false); // 是否正在加载
const hasMore = ref(true); // 是否还有更多数据
const categoryList = ref<CategoryItem[]>([]); // 分类列表数据
const parentList = ref<CategoryItem[]>([]); // 一级分类数据

// 监听 selePdCaShow prop 的变化
watch(() => props.selePdCaShow, (newValue) => {
  productCategoryShow.value = newValue;
  if (newValue) {
    // 打开弹窗时加载数据
    pageParams.page = 1;
    categoryList.value = [];
    parentList.value = [];
    loadCategories();
  }
});

// 方法
/**
 * 加载分类数据
 */
const loadCategories = async () => {
  if (!hasMore.value || isLoading.value) return;

  isLoading.value = true;
  try {
    const response = await getGoodsType(pageParams);
    console.log(response);

    // 将新数据添加到列表中
    if (pageParams.page === 1) {
      categoryList.value = response.data.results;
    } else {
      categoryList.value = [...categoryList.value, ...response.data.results];
    }

    // 过滤出一级分类
    const newParentCategories = response.data.results.filter(
      (item: CategoryItem) => item.parent === null
    );
    if (pageParams.page === 1) {
      parentList.value = newParentCategories;
    } else {
      parentList.value = [...parentList.value, ...newParentCategories];
    }

    hasMore.value = response.next !== null;
    if (hasMore.value) {
      pageParams.page++;
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    isLoading.value = false;
  }
};

/**
 * 监听滚动到底部
 */
const onReachBottom = () => {
  if (hasMore.value && !isLoading.value) {
    loadCategories();
  }
};

/**
 * 重置状态
 */
const resetCategory = () => {
  categoryName.value = null;
  categoryId.value = null;
  pageParams.page = 1;
  categoryList.value = [];
  parentList.value = [];
  hasMore.value = true;
};

/**
 * 选中分类
 * @param name - 分类名称
 * @param id - 分类ID
 */
const selected = (name: string, id: string) => {
  categoryName.value = name;
  categoryId.value = id;
};

/**
 * 关闭商品类别弹出层
 */
const closeProductCategory = () => {
  productCategoryShow.value = false;
  emit('update:show', productCategoryShow.value);
  resetCategory();
};

/**
 * 确认选择的分类
 */
const confirmCategory = () => {
  if (categoryId.value !== null && categoryName.value !== null) {
    const category = {
      id: categoryId.value,
      name: categoryName.value,
    };
    emit('update:category', category);
    resetCategory();
  } else {
    uni.showToast({
      title: '请选择商品类别',
      icon: 'none',
    });
    return;
  }
  closeProductCategory();
};
</script>

<style lang="scss" scoped>
.container {
    height: 500rpx;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
    overflow-y: auto;
    height: calc(100% - 100rpx); // 减去头部高度
}

//商品类别弹出层
.categoryConfirmationBtn {
    width: 90%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 20rpx 0;
}

.divider {
    border: 1px solid #ccc;
    width: 100%;
}

.category {
    display: flex;
}

.selectArea {
    width: 90%;
    display: flex;
    flex-direction: column;

    .levelOne,
    .levelTwo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10rpx;
        border-radius: 4rpx;
        transition: all 0.3s;

        &:active {
            background-color: #f5f5f5;
        }
    }

    .levelTwo {
        margin-left: 30rpx;
        font-size: 25rpx;
    }

    .levelOne_left {
        display: flex;
        align-items: center;
        gap: 15rpx;
    }

    .levelOneName {
        font-size: 28rpx;
        transition: all 0.3s;
    }
}

.circleTool {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid #b2b2b2;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    position: relative;
    flex-shrink: 0;
}

.active-circle {
    border-color: #3982cf;
    border-width: 2px;
}

.active-text {
    font-weight: bold;
    color: #333;
}

.selected-circle {
    width: 6px;
    height: 6px;
    background-color: #3982cf;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    
    .loading-text {
        margin-left: 10rpx;
        font-size: 24rpx;
        color: #999;
    }
}
</style>