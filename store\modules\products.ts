// 定义产品接口
interface Product {
  id: string | number;
  name: string;
  // 根据实际API响应添加更多属性
}

// 定义模块状态接口
interface ProductsState {
  allProducts: Product[];
}

const state: ProductsState = {
  allProducts: [],
};

const mutations = {
  /**
   * 设置所有产品
   * @param {ProductsState} state - Vuex state
   * @param {Product[]} products - 产品列表数组
   */
  SET_PRODUCTS(state: ProductsState, products: Product[]) {
    state.allProducts = products;
  },
};

const actions = {
  /**
   * 从API获取产品列表
   * @param {object} { commit } - Vuex commit
   */
  async fetchProducts({ commit }: { commit: Function }) {
    try {
      // 假设 uni.request 返回的数据结构包含 data 属性，且 data 是 Product[] 类型
      const res = await uni.request({
        url: 'https://api.example.com/products',
      });
      // 检查请求是否成功，并确保 res.data 是一个数组
      if (res.statusCode === 200 && Array.isArray(res.data)) {
        commit('SET_PRODUCTS', res.data as Product[]);
      } else {
        console.error('获取产品失败或数据格式不正确:', res);
      }
    } catch (error) {
      console.error('请求产品API失败:', error);
      // 可以在这里添加错误处理，例如显示toast
      uni.showToast({
        title: '获取产品失败',
        icon: 'none',
      });
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
