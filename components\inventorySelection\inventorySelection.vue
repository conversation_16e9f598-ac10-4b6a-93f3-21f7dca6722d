<template>
  <view>
    <u-popup :show="inventorySelectionShow" mode="bottom" @close="closeInventorySelection" @open="onOpen">
      <view class="container" v-if="isInit || isSelect">
        <view class="header">
          <view class="categoryConfirmationBtn">
            <view @click="closeWarehouse">取消</view>
            <view class="popup_title">{{ title }}</view>
            <view class="blueFont" @click="saveWarehouse">{{
              saveBtnTitle
            }}</view>
          </view>
          <view class="divider"></view>
        </view>
        <scroll-view class="content" scroll-y>
          <view class="category" v-for="item in warehouseList" :key="item.id">
            <view class="scalableIcon" @click="expandOrNot(item)">
              <i-down-one v-if="item.scalableIconShow" theme="filled" size="24" fill="#b2b2b2" />
              <i-right-one v-else theme="filled" size="24" fill="#b2b2b2" />
            </view>
            <view class="selectArea">
              <view class="levelOne" @click="showParentActions(item)">
                <view class="levelOne_left">
                  <view class="levelOneName">{{ item.name }}</view>
                  <!-- :class="{ 'active-text': item.showActions }" -->
                </view>
                <view class="actions" v-if="item.showActions">
                  <view class="action-btn edit" @click.stop="EditWarehouse(1, item)">编辑</view>
                  <view class="action-btn delete" @click.stop="deleteWarehouse(item)">删除</view>
                </view>
              </view>
              <template v-if="item.scalableIconShow">
                <view class="levelTwo" v-for="item2 in item.children" :key="item2.id" @click="showActions(item2, item)">
                  <view class="levelTwo_left">
                    <view class="circleTool" :class="{
                      'active-circle':
                        categoryName === item2.name || item2.selected,
                    }">
                      <view :class="{
                        'selected-circle': item2.selected,
                      }"></view>
                    </view>
                    <view class="levelTwoName" :class="{ 'active-text-two': item2.selected }">{{ item2.name }}</view>
                    <view v-if="item2.isInit">({{ item2.initTotal }})</view>
                  </view>
                  <view class="actions" v-if="item2.showActions">
                    <view class="action-btn edit" @click.stop="EditWarehouse(1, item2, item)">编辑</view>
                    <view class="action-btn edit" v-if="isInit" @click.stop="initWarehouse(item2, item)">初始化
                    </view>
                  </view>
                </view>
              </template>
            </view>
          </view>
        </scroll-view>

        <view class="addBtn">
          <view @click="addWarehouse(0)">
            <i-add-one theme="outline" size="24" fill="#5fa3eb" />
          </view>
        </view>
      </view>

      <!-- 展示库存 -->
      <view class="container" v-if="!isInit && !isSelect">
        <view class="inventoryDisplayHeader">
          <view class="inventoryDisplayBtn">
            <view class="back" @click="closeWarehouse">
              <i-left theme="outline" size="24" fill="#333" />
            </view>
            <view class="popup_title">库存</view>
          </view>
        </view>
        <scroll-view class="content" scroll-y>
          <view class="inventory-group" v-for="item in inventoryInfo" :key="item.id">
            <view class="info_title" style="padding:20rpx 0">
              <text class="inventory-warehouse-name">{{ item.warehouse_name }}</text>
            </view>
            <view class="inventory-list">
              <view class="inventory-item">
                <view class="item-label">当前库存</view>
                <view class="item-value">{{ item.quantity }}</view>
              </view>
            </view>
          </view>
        </scroll-view>
        <view class="totalInventoryQuantity">
          <view class="totalInventoryQuantity_text">
            当前库存量：{{ totalQuantity }}
          </view>
        </view>
      </view>
      <view class="popup" style="position: relative;">
				<!-- 弹出层内容 -->
				<refreshPage :loading="loading" />
			</view>
    </u-popup>

    <!-- 保存提醒框 -->
    <u-modal :show="remindShow" :title="remindTitle" :content="remindContent"></u-modal>

    <!-- 编辑仓库 -->
    <editWarehouse :editShow="editShow" :editType="editType" :editWarehouseInfo="editWarehouseInfo"
      :editWarehouseParentInfo="editWarehouseParentInfo" :warehouseList="warehouseList" @close="closeEditWarehouse"
      @addWarehouse="handleAdd" @editWarehouse="handleEdit" @delWarehouse="handleDel" />

    <!-- 初始化库存 -->
    <initializeWarehouse :initShow="initShow" :initWarehouseInfo="initWarehouseInfo"
      :currentWarehouseInitData="currentWarehouseInitData" :units="units" @close="initClose" @confirm="initConfirm" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { onMounted } from 'vue';
import { getWarehouses, delWarehouses } from "@/api/warehouse";
import editWarehouse from "./components/editWarehouse.vue";
import initializeWarehouse from "./components/initializeWarehouse.vue";
import refreshPage from "@/components/refreshPage.vue";

interface ProductUnit {
  unit_type_id: string;
  conversion_rate: number;
}

interface ProductStock {
  warehouse_id: string;
  warehouse_name: string;
  stock_quantity: number;
}

interface WarehouseItem {
  id: number;
  name: string;
  is_parent: boolean;
  parent?: number;
  isInit: boolean;
  initTotal: number;
  scalableIconShow: boolean;
  showActions: boolean;
  selected: boolean;
  editing: boolean;
  children?: WarehouseItem[];
}

interface InitData {
  warehouse: number;
  unit_type_id: string;
  quantity?: number;
  actual_cost?: number;
}

const props = defineProps({
  inventorySelectionShow: {
    type: Boolean,
    default: false,
  },
  units: {
    type: Array as () => ProductUnit[],
    default: () => [],
  },
  isInit: {
    type: Boolean,
    default: false,
  },
  isSelect: {
    type: Boolean,
    default: false,
  },
  inventory: {
    type: Array as () => ProductStock[],
    default: () => [],
  },
});

const emit = defineEmits(['close', 'save', 'add', 'updateWarehouseData']);

const showActionsItem = ref(false);
const loading = ref(false); //页面加载动画
const remindShow = ref(false); //是否打开保存提醒框
const remindTitle = ref("温馨提示"); //保存提醒框标题
const remindContent = ref("保存后后续将不允许修改，是否确认保存"); //保存提醒框内容
const saveBtnTitle = ref("保存");
const title = ref('');
const initShow = ref(false); //是否打开初始化库存
const editShow = ref(false); //编辑弹出层
const editType = ref(0); //编辑弹出层
const editWarehouseInfo = ref<any>({});
const editWarehouseParentInfo = ref<any>({});
const warehouseParam = reactive({
  //仓库请求页数参数
  page: 1,
  page_size: 10,
});
const warehouseList = ref<WarehouseItem[]>([]);
const loadingStatus = ref("loadmore"); // 'loadmore' | 'loading' | 'nomore'
const initData = ref<InitData[]>([]); //初始化数据
const initWarehouseInfo = ref<any>({}); //初始化仓库
const currentWarehouseInitData = ref<InitData[]>([]); //当前仓库初始化后数据
const selectedWarehouse = ref<any>({});
const selectedWarehouseId = ref<number | null>(null);
const showWarehouseList = ref(false); // 控制仓库选择框显示隐藏
const inventoryInfo = ref<ProductStock[]>([]);
const totalQuantity = ref(0);//总计库存量

watch(() => props.isInit, (newVal) => {
  if (newVal) {
    saveBtnTitle.value = "保存";
    title.value = '初始化库存';
  } else {
    saveBtnTitle.value = "确认";
    title.value = '选择仓库';
  }
});

watch(() => props.inventory, (newVal) => {
  inventoryInfo.value = newVal;
  totalQuantity.value = 0; // 重置总计
  inventoryInfo.value.forEach((item) => {
    totalQuantity.value += Number(item.stock_quantity);
  });
});

watch(() => props.units, (newVal) => {
  if (newVal && newVal.length > 0) {
    // 重新计算所有仓库的初始化总量
    updateAllWarehouseInitTotals();

    // 清理不存在的单位对应的初始化数据
    cleanupInvalidUnitData(newVal);
  }
});

// 编辑和新建时候调用局部刷新数据
const handleAdd = (data: WarehouseItem) => {
  if (data.is_parent) {
    // 父仓库新增 - 补全数据并添加到数组第一位
    const newParentWarehouse: WarehouseItem = {
      ...data,
      isInit: false,
      initTotal: 0,
      scalableIconShow: true,
      showActions: false,
      selected: false,
      editing: false,
      children: []
    };
    warehouseList.value.unshift(newParentWarehouse);
    warehouseList.value = [...warehouseList.value];
  } else {
    // 子仓库新增 - 找到对应父类并添加到children第一位
    warehouseList.value.forEach((parentItem) => {
      if (parentItem.id == data.parent) {
        const newChildWarehouse: WarehouseItem = {
          ...data,
          isInit: false,
          initTotal: 0,
          scalableIconShow: true,
          showActions: false,
          selected: false,
          editing: false,
          children: []
        };
        if (!parentItem.children) {
          parentItem.children = [];
        }
        parentItem.children.unshift(newChildWarehouse);
        // 强制更新children数组
        parentItem.children = [...parentItem.children];
      }
    });
  }
};
const handleEdit = (resData: WarehouseItem, oldParentId: number) => {
  console.log(resData);
  console.log(oldParentId);

  //父仓移动到其他父仓的情况
  warehouseList.value.forEach((e, index) => {
    if (e.id == resData.id && !resData.is_parent) {
      // 从第一级数组中移除
      warehouseList.value.splice(index, 1);
    }
  });

  //修改到其他父类仓库里面
  if (resData.parent !== oldParentId) {
    let oldInitTotal = 0; // 保存旧的initTotal
    let oldIsInit = false; // 保存旧的isInit状态

    warehouseList.value.forEach((e) => {
      //先获取旧父仓下的initTotal和isInit
      if (e.id == oldParentId) {
        e.children?.forEach((child) => {
          if (child.id == resData.id) {
            oldInitTotal = child.initTotal || 0;
            oldIsInit = child.isInit || false;
          }
        });
      }
    });

    warehouseList.value.forEach((e) => {
      //添加在新父仓下面
      if (e.id == resData.parent) {
        const newChildWarehouse: WarehouseItem = {
          ...resData,
          isInit: oldIsInit, // 使用保存的isInit状态
          initTotal: oldInitTotal, // 使用保存的initTotal
          scalableIconShow: true,
          showActions: false,
          selected: false,
          editing: false,
          children: []
        };

        e.children?.unshift(newChildWarehouse);
        // 强制更新children数组
        e.children = [...(e.children || [])];
      }
      //删除在旧父仓的数据
      if (e.id == oldParentId) {
        e.children?.forEach((child, childIndex) => {
          if (child.id == resData.id) {
            e.children?.splice(childIndex, 1);
          }
        });
      }
    });

    console.log(warehouseList.value);

  } else {
    //父类相同修改名称等等
    if (resData.is_parent) {//父类修改情况
      warehouseList.value.forEach((e, index) => {
        if (e.id == resData.id) {
          warehouseList.value[index] = { ...e, ...resData };
        }
      });
    } else {
      // 子类修改情况
      warehouseList.value.forEach((e) => {
        if (e.id == resData.parent) {
          e.children?.forEach((child, childIndex) => {
            if (child.id == resData.id) {
              // 直接替换原来的数据，保留原有的initTotal和isInit状态
              const updatedChild: WarehouseItem = {
                ...resData,
                initTotal: child.initTotal || 0,
                isInit: child.isInit || false,
                scalableIconShow: true,
                showActions: false,
                selected: false,
                editing: false,
                children: []
              };
              // 直接替换数据
              e.children![childIndex] = updatedChild;
            }
          });
        }
      });
    }
  }
};
const handleDel = (item: WarehouseItem) => {
  // 从数组中移除删除的仓库
  if (item.is_parent) {
    // 删除父仓库
    const parentIndex = warehouseList.value.findIndex(warehouse => warehouse.id === item.id);
    if (parentIndex !== -1) {
      warehouseList.value.splice(parentIndex, 1);
    }
  } else {
    // 删除子仓库
    warehouseList.value.forEach((parent) => {
      if (parent.children && parent.children.length > 0) {
        const childIndex = parent.children.findIndex(child => child.id === item.id);
        if (childIndex !== -1) {
          parent.children.splice(childIndex, 1);
        }
      }
    });
  }

  //如果初始化库存的仓库被删掉了，就从初始化库存数据中移除
  initData.value = initData.value.filter((e) => e.warehouse !== item.id);
};

// 弹窗打开时调用 
const onOpen = () => {
  console.log(warehouseList.value);

  loading.value = true;
  // 优先检查本地数据
  if (warehouseList.value && warehouseList.value.length > 0) {
    loadingStatus.value = "nomore";
  } else {
    // 如果都没有数据，才向后端请求
    warehouseParam.page = 1;
    warehouseList.value = [];
    loadingStatus.value = "loading";
    getWarehousesData();
    return;
  }

  // 默认选择第一个子仓库（如果存在）
  if (warehouseList.value[0] && warehouseList.value[0].children && warehouseList.value[0].children.length > 0 && !showActionsItem.value) {
    warehouseList.value[0].children[0].showActions = true;
    warehouseList.value[0].children[0].selected = true;
    selectedWarehouse.value = warehouseList.value[0].children[0];
    selectedWarehouseId.value = warehouseList.value[0].children[0].id;
  } else {
    selectedWarehouse.value = {};
    selectedWarehouseId.value = null;
  }
  loading.value = false;
};
// 获取仓库列表（then链式写法）
const getWarehousesData = () => {
  loadingStatus.value = "loading";
  getWarehouses(warehouseParam)
    .then((res: any) => {
      const list = res.data.results || [];
      const formattedList = formatWarehouseData(list);
      if (warehouseParam.page === 1) {
        warehouseList.value = formattedList;

        // 默认选择第一个子仓库（如果存在）
        if (warehouseList.value[0] && warehouseList.value[0].children && warehouseList.value[0].children.length > 0) {
          warehouseList.value[0].children[0].showActions = true;
          warehouseList.value[0].children[0].selected = true;
          selectedWarehouse.value = warehouseList.value[0].children[0];
          selectedWarehouseId.value = warehouseList.value[0].children[0].id;
        } else {
          // 如果没有子仓库，清空选择
          selectedWarehouse.value = {};
          selectedWarehouseId.value = null;
        }
      } else {
        warehouseList.value = warehouseList.value.concat(formattedList);
      }

      // 分页判断
      if (list.length < warehouseParam.page_size) {
        loadingStatus.value = "nomore";
      } else {
        loadingStatus.value = "loadmore";
        warehouseParam.page++;
      }
    })
    .catch(() => {
      loadingStatus.value = "loadmore";
    })
    .finally(() => {
      loading.value = false;
    });
};
// 触底加载
const onReachBottom = () => {
  if (loadingStatus.value === "loadmore") {
    getWarehousesData();
  }
};
const expandOrNot = (item: WarehouseItem) => {
  item.scalableIconShow = !item.scalableIconShow;
};
//关闭弹出层
const closeInventorySelection = () => {
  // 清除所有状态后传递给父组件
  const cleanedData = clearAllStates(warehouseList.value);
  emit("updateWarehouseData", cleanedData);
  showWarehouseList.value = false;
  emit("close");
};
const showActions = (item: WarehouseItem, parent: WarehouseItem) => {
  // 关闭所有项的操作按钮（包括父仓库和其他子仓库）
  warehouseList.value.forEach((category) => {
    category.showActions = false;
    if (Array.isArray(category.children)) {
      category.children.forEach((child) => {
        child.showActions = false;
        child.selected = false; // 清除所有子仓库的选中状态
      });
    }
  });

  // 显示当前子仓库的操作按钮和选中状态
  item.showActions = true;
  item.selected = true;

  // 更新选择的仓库（只有子仓库才能被选择）
  selectedWarehouse.value = item;
  selectedWarehouseId.value = item.id;
  showActionsItem.value = true;
};

// 父仓库点击处理（只能编辑，不能选择）
const showParentActions = (item: WarehouseItem) => {
  // 只关闭父仓库的操作按钮，保持子仓库的选择状态和操作按钮状态
  warehouseList.value.forEach((category) => {
    category.showActions = false;
    // 不清除子仓库的showActions和selected状态
  });

  // 显示当前父仓库的操作按钮
  item.showActions = true;

  // 父仓库不能被选择，不更新selectedWarehouse
  // 保持当前子仓库的选择状态和selectedWarehouse不变
};
const EditWarehouse = (index: number, item: WarehouseItem, parent?: WarehouseItem) => {
  editType.value = index;
  editWarehouseInfo.value = {
    ...item,
    parent: parent?.id || '',
    parentName: parent?.name || ''
  };
  editWarehouseParentInfo.value = parent || {};
  editShow.value = true;
};
// 删除仓库
const deleteWarehouse = (item: WarehouseItem) => {
  uni.showModal({
    title: "确认删除",
    content: `确定要删除"${item.name}"仓库吗？`,
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用删除API
        delWarehouses(item).then((res: any) => {
          if (res.code == 0) {
            uni.showToast({
              title: "删除成功",
              icon: "success",
            });
            handleDel(item);
          } else {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
        });
      }
    },
  });
};
//打开初始化仓库弹出层
const initWarehouse = (item: WarehouseItem, parent: WarehouseItem) => {
  initWarehouseInfo.value = item;

  // 只筛选当前仓库的初始化数据
  const filteredData = initData.value.filter((e) => e.warehouse === item.id);
  currentWarehouseInitData.value = [...filteredData];

  console.log('传递给子组件的数据:', filteredData);
  console.log('当前仓库ID:', item.id);
  console.log('所有初始化数据:', initData.value);

  initShow.value = true;
};
//处理初始化库存弹出层数据
const initConfirm = (data: InitData[], total: number) => {
  const arr = Array.isArray(data) ? data : [data];
  const currentWarehouseId = arr.length > 0 ? arr[0].warehouse : null;

  console.log('接收到的初始化数据:', data);
  console.log('当前仓库ID:', currentWarehouseId);

  if (currentWarehouseId) {
    // 先移除该仓库的所有旧数据
    initData.value = initData.value.filter(item => item.warehouse !== currentWarehouseId);

    // 只添加有效的新数据（有数量或成本的数据）
    arr.forEach((e) => {
      if (e.quantity || e.actual_cost) {
        initData.value.push(e);
      }
    });

    console.log('更新后的所有初始化数据:', initData.value);

    // 更新仓库状态
    setInitStatus(warehouseList.value, currentWarehouseId, total);
  }
};
//递归循环找出节点
const setInitStatus = (list: WarehouseItem[], warehouseId: number, total: number) => {
  list.forEach((item) => {
    if (item.id == warehouseId) {
      item.isInit = true;
      item.initTotal = total;
    }
    if (item.children && item.children.length > 0) {
      setInitStatus(item.children, warehouseId, total);
    }
  });
};
//关闭初始化库存弹出层
const initClose = () => {
  initShow.value = false;
};
// 新增仓库
const addWarehouse = (type: number) => {
  editType.value = type;
  editShow.value = true;
  emit("add");
};
//关闭编辑弹出层
const closeEditWarehouse = () => {
  editShow.value = false;
};
//关闭编辑弹出层
const closeWarehouse = () => {
  // 在缓存数据之前，将所有selected状态重置为false
  warehouseList.value.forEach((category) => {
    category.selected = false;
    if (Array.isArray(category.children)) {
      category.children.forEach((child) => {
        child.selected = false;
      });
    }
  });

  showWarehouseList.value = false;
  emit("close");
};
// 保存逻辑
const saveWarehouse = () => {
  if (props.isInit) {
    // 清除所有状态后传递给父组件
    const cleanedData = clearAllStates(warehouseList.value);
    emit("updateWarehouseData", cleanedData);
    emit("save", initData.value);
  } else {
    // 检查是否选择了有效的仓库
    if (!selectedWarehouse.value || !selectedWarehouse.value.id) {
      uni.showToast({
        title: "请选择具体的仓库",
        icon: "none",
        duration: 2000
      });
      return;
    }

    const isParentWarehouse = warehouseList.value.some(parent => parent.id === selectedWarehouse.value.id);
    if (isParentWarehouse) {
      uni.showToast({
        title: "不能选择父仓库，请选择具体的子仓库",
        icon: "none",
        duration: 2000
      });
      return;
    }
    emit("save", selectedWarehouse.value);
  }

  showWarehouseList.value = false;
  emit("close");
};
const toggleWarehouseList = () => {
  showWarehouseList.value = !showWarehouseList.value;
};
// 清除所有状态的方法
const clearAllStates = (list: WarehouseItem[]) => {
  return list.map(item => ({
    ...item,
    showActions: false,
    selected: false,
    children: item.children ? clearAllStates(item.children) : []
  }));
};
// 新增：更新所有仓库的初始化总量
const updateAllWarehouseInitTotals = () => {
  warehouseList.value.forEach(warehouse => {
    if (warehouse.children) {
      warehouse.children.forEach(child => {
        child.initTotal = calculateWarehouseTotal(child.id);
      });
    }
  });
};

// 新增：计算指定仓库的总量
const calculateWarehouseTotal = (warehouseId: number) => {
  let total = 0;
  initData.value.forEach(data => {
    if (data.warehouse === warehouseId) {
      const matchingUnit = props.units.find(unit => unit.unit_type_id === data.unit_type_id);
      if (matchingUnit) {
        const quantity = Number(data.quantity) || 0;
        const rate = Number(matchingUnit.conversion_rate) || 0;
        total += quantity * rate;
      }
    }
  });
  return total;
};

// 新增：清理无效的单位数据
const cleanupInvalidUnitData = (currentUnits: ProductUnit[]) => {
  const validUnitIds = currentUnits.map(unit => unit.unit_type_id);
  initData.value = initData.value.filter(data =>
    validUnitIds.includes(data.unit_type_id)
  );
};

function formatWarehouseData(list: any[]) {
  return list.map((item) => {
    // 保留原有字段，新增前端字段
    const newItem: WarehouseItem = {
      ...item,
      isInit: false,
      initTotal: 0,
      scalableIconShow: true,
      showActions: false,
      selected: false, // 新增选中状态字段
      editing: false,
      children:
        item.children && item.children.length > 0
          ? formatWarehouseData(item.children)
          : [],
    };
    return newItem;
  });
}

onMounted(() => {
  // onReady is not available in script setup, use onMounted if needed for DOM access
});

</script>

<style lang="scss" scoped>
.container {
  height: 800rpx;
  display: flex;
  flex-direction: column;
}

.header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.content {
  overflow-y: auto;
  height: 480rpx;
}

.category {
  display: flex;
}

.selectArea {
  width: 90%;
  display: flex;
  flex-direction: column;

  .levelOne,
  .levelTwo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx;
    border-radius: 4rpx;
    transition: all 0.3s;

    &:active {
      background-color: #f5f5f5;
    }
  }

  .levelTwo {
    margin-left: 30rpx;
    font-size: 25rpx;
  }

  .levelOne_left,
  .levelTwo_left {
    display: flex;
    align-items: center;
    gap: 15rpx;
  }

  .levelOneName {
    font-size: 28rpx;
  }

  .parent-warehouse-tip {
    font-size: 20rpx;
    color: #999;
    margin-left: 10rpx;
  }

  .levelTwoName {
    font-size: 25rpx;
    transition: all 0.3s;
  }

  .circleTool {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid #b2b2b2;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    position: relative;
    flex-shrink: 0;
  }

  .active-circle {
    width: 8px;
    height: 8px;
    border-color: #3982cf;
    border-width: 2px;
  }

  .selected-circle {
    width: 6px;
    height: 6px;
    background-color: #3982cf;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .active-text {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
  }

  .active-text-two {
    font-size: 25rpx;
    font-weight: bold;
    color: #333;
  }
}

.actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  transition: all 0.3s;

  &.edit {
    color: #2b85e4;

    &:active {
      background-color: rgba(43, 133, 228, 0.1);
    }
  }

  &.delete {
    color: #ff4d4f;

    &:active {
      background-color: rgba(255, 77, 79, 0.1);
    }
  }
}

.scalableIcon {
  margin-right: 10rpx;
  margin-top: 10rpx;
}

.save-btn-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.save-btn {
  background: #2b85e4;
  color: #fff;
  border: none;
  border-radius: 6rpx;
  padding: 0 60rpx;
  font-size: 28rpx;
}

.edit-input {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  font-size: 14px;
  color: #333;
  outline: none;
  box-sizing: border-box;
}

.addBtn {
  width: 90%;
  height: 60rpx;
  display: flex;
  flex-direction: row-reverse;
  margin: 10rpx auto;
}

.inventory-box {
  padding: 24rpx 32rpx;
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
}

.inventory-header {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}

.inventory-group {
  position: relative;
  width: 90%;
  margin: 0 auto;
}

.group-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.inventory-list {
  margin-top: 8rpx;
}

.inventory-item {
  display: flex;
  align-items: center;
  position: relative;
  padding: 16rpx 0 16rpx 0;
  border-bottom: 1px solid #eee;
}

.line-dot {
  width: 8rpx;
  height: 8rpx;
  background: #e0b84c;
  border-radius: 50%;
  margin-right: 16rpx;
  position: relative;
}

.line-dot::before {
  content: "";
  position: absolute;
  left: 50%;
  top: -24rpx;
  width: 2rpx;
  height: 24rpx;
  background: #e0b84c;
  transform: translateX(-50%);
}

.line-dot::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -24rpx;
  width: 2rpx;
  height: 24rpx;
  background: #e0b84c;
  transform: translateX(-50%);
}

.inventory-item:first-child .line-dot::before {
  display: none;
}

.inventory-item:last-child .line-dot::after {
  display: none;
}

.item-label {
  flex: 1;
  font-size: 28rpx;
  font-weight: 700;
  color: #333;
}

.item-value {
  font-size: 26rpx;
  color: #222;
  font-weight: bold;
}

.switch-warehouse {
  color: #2b85e4;
  font-size: 24rpx;
  cursor: pointer;
  text-align: right;
  margin-top: 20rpx;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

.warehouse-list-popup {
  min-width: 170rpx;
  position: absolute;
  right: 0;
  top: 355rpx;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
  padding: 8rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.warehouse-list-item {
  padding: 12rpx 0;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  width: 100%;
  text-align: center;
}

.warehouse-list-item:last-child {
  border-bottom: none;
}

.inventoryDisplayHeader {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
  height: 70rpx;
  border-bottom: 1rpx solid #cacaca;
}

.inventoryDisplayBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  width: 100%;

  .back {
    position: absolute;
    left: 30rpx;
  }
}

.totalInventoryQuantity {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  margin-top: 20rpx;
  margin-right: 20rpx;

  .totalInventoryQuantity_text {
    color: red;
    font-size: 26rpx;
  }
}

</style>
