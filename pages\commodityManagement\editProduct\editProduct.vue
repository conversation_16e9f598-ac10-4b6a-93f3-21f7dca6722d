<template>
  <view class="container" @click="hideDeleteTooltip">
    <view style="flex: 1; overflow-y: auto; height: calc(100vh - 120px)">
      <!-- 基本信息 -->
        <basicInfo ref="basicInfo" :basicInfo="productInfo" type="product" @update:basicInfo="updateProductInfo" />
      <!-- 价格 -->
      <view class="info">
        <view class="info_title">
          <view class="price_title">
            <text>价格</text><text style="font-size: 20rpx; color: #e25245; border: none">*</text>
          </view>
          <view class="addIcon" v-if="canAddPrice" @click="openPrice">
            <i-add-one theme="outline" size="20" fill="#0f40f5" />
          </view>
        </view>

        <view class="pricesShowRegion">
          <view class="prices_item" v-for="(item, index) in productInfo.units" :key="index">
            <view class="hostTag" v-if="index == 0">主</view>
            <view class="hostTag_reserveASeat" v-else></view>
            <view class="content" @tap="editPrice(item)">
              <view class="content_item">
                <view class="content_item_attribute">
                  <view class="attributeName_left">单位</view><text>：{{ item.unit_type_name }}</text>
                </view>
                <view class="content_item_attribute">
                  <view class="attributeName_left">批发价</view><text>：{{ formatNumber(item.wholesale_price) }}</text>
                </view>
              </view>
              <view class="content_item">
                <view class="content_item_attribute">
                  <view class="attributeName_right">零售价</view><text>：{{ formatNumber(item.retail_price) }} </text>
                </view>
                <view class="content_item_attribute">
                  <view class="attributeName_right">最低售价</view>
                  <text>：{{ formatNumber(item.min_price) }}</text>
                </view>
              </view>
            </view>
            <view class="delIcon">
              <view @click.stop="showDeleteTooltip(index)">
                <i-reduce-one theme="outline" size="20" fill="#E25245" />
              </view>
              <view class="delete-btn" v-if="showDeleteIndex === index" @click.stop="handleDelete(index)">
                <i-delete theme="outline" size="24" fill="#fff" />
                <text>删 除</text>
              </view>
              <view class="proportion" v-if="index !== 0">
                ×{{ formatNumber(item.conversion_rate) }}
                {{ productInfo.units[0].unit_type_name }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 库存 -->
      <view class="info">
        <view class="info_title" style="border-bottom: 0" @click="openStock">
          <text>库存</text>
          <view class="inventory">
            <view v-if="isInventory">当前库存总量：{{ Math.floor(productInfo.total_stock) }} </view>
            <i-right theme="outline" size="20" fill="#333" />
          </view>
        </view>
      </view>
      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="inventory">
          <u--textarea v-model="productInfo.remark" autoHeight placeholder="请输入备注" border="none"></u--textarea>
        </view>
      </view>
      <!-- 图片信息 -->
      <view class="image-upload-container">
        <imageUpload :productInfo="productInfo" :isEdit="isEdit" :images_urls="productInfo.images"
          @uploadSuccess="uploadImages" @deletePic="deletePic" />
      </view>
      <!-- 操作按钮 -->
      <view class="operation">
        <u-button type="error" text="删除" v-if="isEdit" @click="delPrompt"></u-button>
        <u-button type="warning" text="禁用" v-if="isEdit && productInfo.is_active"
          @click="submitProductInfo(1)"></u-button>
        <u-button type="success" text="激活" v-if="isEdit && !productInfo.is_active"
          @click="submitProductInfo(1)"></u-button>
        <u-button type="primary" text="保存" @click="submitForm"></u-button>
      </view>
    </view>

    <!-- 语音输入 -->
    <view class="inputArea">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 删除提示框 -->
    <view>
      <u-modal :show="promptShow" :title="promptTitle" :content="promptContent" :showCancelButton="true"
        :closeOnClickOverlay="true" @confirm="delGoods" @cancel="close" @close="close"></u-modal>
    </view>

    <!-- 商品类别弹出层 -->
    <category v-model:CategoryShow="CategoryShow" :queryCategoryName="'item'" @confirm="updateProductCategory" />

    <!-- 价格录入弹出框 -->
    <priceInput v-model:priceShow="priceShow" :editPriceInfo="editPriceInfo"
      :hostPriceUnit="hostPriceUnit" @confirm-price="confirmPrice" />

    <!-- 库存弹出层 -->
    <inventorySelection :inventorySelectionShow="inventorySelectionShow" :isInit="isInit" :inventory="inventory"
      @close="closeInventorySelection" @save="confirmInventory" :units="productInfo.units" />

    <interactive />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import priceInput from "@/components/priceInput/priceInput.vue";
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import inventorySelection from "@/components/inventorySelection/inventorySelection.vue";
import { saveGoods, updateGoods, deleteGoods } from "@/api/goods";
import { formatNumber } from "@/utils/digitalConversion"; // 确保路径正确

// 定义商品单位接口
interface ProductUnit {
  unit_type_name: string;
  unit_type_id: string;
  wholesale_price: number;
  retail_price: number;
  min_price: number;
  conversion_rate: number;
  numberOfOldUnits?: number;
  numberOfNewUnits?: number;
}

// 定义商品库存接口
interface ProductStock {
  warehouse_id: string;
  warehouse_name: string;
  stock_quantity: number;
}

// 定义商品信息接口
interface ProductInfo {
  item_type_name: string | null;
  id: string;
  code: string;
  name: string;
  item_type: string | null;
  total_stock: number;
  total_cost: number;
  expiry_quantity: number;
  expiry_days: number;
  is_active: boolean;
  serial_number: string;
  color: string;
  brand: string;
  model: string;
  base_weight: number;
  location: string;
  manufacturer: string;
  specification: string;
  units: ProductUnit[];
  stocks: ProductStock[];
  remark: string;
  images: any[]; // 假设图片信息是any类型，后续可以细化
  warehouse_stock?: ProductStock[]; // 假设存在这个属性
}

// 定义表单验证规则类型
interface Rule {
  required?: boolean;
  message: string;
  trigger: string[];
}

interface ProductRules {
  name: Rule[];
  item_type_name: Rule[];
}

// 响应式数据
const promptShow = ref(false); // 提示显示状态
const promptTitle = ref("温馨提示"); // 提示标题
const promptContent = ref("您确定要删除此物品吗？"); // 保存提示内容
const isInit = ref(false); // 是否初始化
const itemId = ref(""); // 商品id
const isExpandProductInfo = ref(false); // 是否展开商品信息
const isEdit = ref(false); // 是否编辑状态
const inventory = ref<ProductStock[]>([]); // 库存信息
const productInfo = reactive<ProductInfo>({
  item_type_name: null,
  id: "", // 主键
  code: "", // 物品编号
  name: "", // 物品名称
  item_type: null, // 物品类型
  total_stock: 0, // 库存总量
  total_cost: 0, // 库存总成本
  expiry_quantity: 0, // 临期或过期商品数量
  expiry_days: 0, // 有效期/保质期（天）
  is_active: true, // 是否有效
  serial_number: "", // 序列号
  color: "", // 颜色
  brand: "", // 品牌
  model: "", // 型号
  base_weight: 0, // 基础重量(kg)
  location: "", // 仓位货架
  manufacturer: "", // 制造商
  specification: "", // 规格
  units: [], // 单位和价格信息
  stocks: [], // 初始化库存信息
  remark: "", // 备注
  images: [], // 图片列表
});
const productRules: ProductRules = {
  name: [
    {
      required: true,
      message: "请输入商品名称",
      trigger: ["blur", "change"],
    },
  ],
  item_type_name: [
    {
      required: true,
      message: "请选择商品类型",
      trigger: ["blur", "change"],
    },
  ],
};
const CategoryShow = ref(false); // 商品类别弹出层是否展示
const stockShow = ref(false); // 库存弹出层是否展示
const priceShow = ref(false); // 价格弹出层是否展示
const editPriceInfo = ref<ProductUnit>({} as ProductUnit); // 编辑价格信息
const hostPriceUnit = ref(""); // 主价格单位
const showDeleteIndex = ref(-1); // 显示删除提示的索引
const goodsData = ref<ProductInfo | null>(null); // 用于存储路由传递的商品数据
const isInventory = ref(false); // 是否显示库存
const isSwitchingWarehouses = ref(false); // 是否显示切换仓库
const inventorySelectionShow = ref(false); // 库存弹出层是否展示

// 模板引用
const uForm = ref(null);

// 计算属性
const canAddPrice = computed(() => {
  return Array.isArray(productInfo.units) && productInfo.units.length < 3;
});

// 方法
/**
 * 关闭提示框
 */
const close = (): void => {
  promptShow.value = false;
};

/**
 * 显示删除提示
 */
const delPrompt = (): void => {
  promptShow.value = true;
};

/**
 * 显示删除工具提示
 * @param index - 价格单位的索引
 */
const showDeleteTooltip = (index: number): void => {
  showDeleteIndex.value = showDeleteIndex.value === index ? -1 : index;
};

/**
 * 处理删除价格单位
 * @param index - 价格单位的索引
 */
const handleDelete = (index: number): void => {
  productInfo.units.splice(index, 1);
  if (index === 0) {
    if (productInfo.units.length === 0) {
      return;
    }
    for (let i = 1; i < productInfo.units.length; i++) {
      productInfo.units[i].conversion_rate /= productInfo.units[0].conversion_rate;
    }
    productInfo.units[0].conversion_rate = 1;
  }
};

/**
 * 隐藏删除工具提示
 */
const hideDeleteTooltip = (): void => {
  showDeleteIndex.value = -1;
};

/**
 * 确认价格信息并添加到商品单位列表
 * @param priceInfo - 价格信息
 */
const confirmPrice = (priceInfo: ProductUnit): void => {
  hostPriceUnit.value = "";
  const targetUnit = priceInfo;
  const index = productInfo.units.findIndex(
    (u) => u.unit_type_id === targetUnit.unit_type_id
  );
  if (index !== -1) {
    productInfo.units.splice(index, 1, targetUnit);
    return;
  }
  // 根据 numberOfOldUnits 和 numberOfNewUnits 的比较结果决定插入位置
  if (priceInfo.numberOfOldUnits! > priceInfo.numberOfNewUnits!) {
    // 如果 numberOfOldUnits 大于 numberOfNewUnits，放在最后
    // 如果 units 长度等于 3，需要再判断与数组第二个的 conversion_rate
    if (productInfo.units.length === 2) {
      const secondConversionRate = productInfo.units[1].conversion_rate;
      const currentConversionRate = priceInfo.conversion_rate;
      // 如果第三个的 conversion_rate 大于第二个的，交换位置
      if (secondConversionRate > currentConversionRate) {
        // 交换第二个和第三个元素的位置
        const temp = productInfo.units[1];
        productInfo.units[1] = priceInfo;
        productInfo.units.push(temp);
      } else {
        productInfo.units.push(priceInfo);
      }
    } else {
      productInfo.units.push(priceInfo);
    }
  } else {
    // 如果 numberOfOldUnits 小于等于 numberOfNewUnits，放在第一个位置
    productInfo.units.unshift(priceInfo);
  }
  // 重新计算 conversion_rate
  for (let i = 1; i < productInfo.units.length; i++) {
    productInfo.units[i].conversion_rate *= productInfo.units[0].conversion_rate;
  }
  productInfo.units[0].conversion_rate = 1;
};

/**
 * 编辑价格信息
 * @param item - 要编辑的价格单位
 */
const editPrice = (item: ProductUnit): void => {
  console.log('editPrice clicked:', item);
  if (productInfo.units.length > 0 && Number(item.conversion_rate) !== 1) {
    hostPriceUnit.value = productInfo.units[0].unit_type_name;
  }
  // 先清空 editPriceInfo，然后重新赋值，确保 watch 能触发
  editPriceInfo.value = {} as ProductUnit;
  nextTick(() => {
    editPriceInfo.value = JSON.parse(JSON.stringify(item));
    priceShow.value = true;
  });
};

/**
 * 关闭库存选择弹出层
 */
const closeInventorySelection = (): void => {
  inventorySelectionShow.value = false;
};

/**
 * 确认库存信息
 * @param data - 库存数据
 */
const confirmInventory = (data: ProductStock[]): void => {
  productInfo.stocks = data;
};

/**
 * 选择商品类别
 */
const selectProductCategory = (): void => {
  console.log("selectProductCategory", CategoryShow.value);
  CategoryShow.value = true;
};

/**
 * 更新商品类别显示状态
 * @param newMessage - 新的显示状态
 */
const updateProductCategoryShow = (newMessage: boolean): void => {
  CategoryShow.value = newMessage;
};

/**
 * 更新商品类别信息
 * @param newMessage - 新的商品类别信息
 */
const updateProductCategory = (newMessage: { id: string; name: string }): void => {
  console.log(newMessage);
  productInfo.item_type = newMessage.id;
  productInfo.item_type_name = newMessage.name;
};

/**
 * 打开商品类别弹出层
 */
const openProductCategory = (): void => {
  CategoryShow.value = true;
};

/**
 * 关闭商品类别弹出层
 */
const closeProductCategory = (): void => {
  CategoryShow.value = false;
};

/**
 * 打开库存弹出层
 */
const openStock = (): void => {
  if (productInfo.units.length === 0) {
    uni.showToast({
      title: "请先填写价格",
      icon: "none",
      mask: true,
    });
    return;
  }
  inventory.value = productInfo.warehouse_stock || [];
  inventorySelectionShow.value = true;
};

/**
 * 关闭库存弹出层
 */
const closeStock = (): void => {
  stockShow.value = false;
};

/**
 * 打开价格录入弹出层
 */
const openPrice = (): void => {
  priceShow.value = true;
  console.log(productInfo.units);

  if (productInfo.units.length !== 0) {
    hostPriceUnit.value = productInfo.units[0].unit_type_name;
  } else {
    hostPriceUnit.value = '';
  }
  console.log(hostPriceUnit.value);
};

/**
 * 更新价格显示状态
 * @param newMessage - 新的显示状态
 */
const updatePriceShow = (newMessage: boolean): void => {
  priceShow.value = newMessage;
};

/**
 * 上传图片成功回调
 * @param data - 图片数据
 */
const uploadImages = (data: any): void => {
  productInfo.images.push(data);
};

/**
 * 删除图片回调
 * @param data - 要删除的图片数据
 */
const deletePic = (data: any): void => {
  productInfo.images = productInfo.images.filter(
    (item) => item.md5 !== data.md5
  );
};

/**
 * 根据路由参数更新表单数据
 * @param data - 商品信息数据
 */
const updateFormData = (data: ProductInfo): void => {
  console.log(data);
  if (data) {
    isEdit.value = true;
    productInfo.item_type_name = data.item_type_name;
    productInfo.images = data.images;
    Object.assign(productInfo, data); // 使用Object.assign进行响应式更新
  }
};

/**
 * 表单验证
 * @returns Promise<boolean> - 验证结果
 */
const validateForm = (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (uForm.value) {
      (uForm.value as any).validate().then((valid: boolean) => {
        if (valid) {
          resolve(true);
        } else {
          uni.showToast({
            title: "请填写必填项",
            icon: "none",
          });
          reject(false);
        }
      });
    } else {
      reject(new Error("uForm ref is not available."));
    }
  });
};

/**
 * 提交商品信息
 * @param id - 可选参数，用于禁用/激活商品
 * @returns Promise<any> - 提交结果
 */
const submitProductInfo = async (id?: number): Promise<any> => {
  if (productInfo.units.length === 0) {
    uni.showToast({
      title: "请填写价格",
      icon: "none",
    });
    console.log(productInfo.units);
    return;
  }

  if (productInfo.id !== "") {
    if (id === 1) {
      productInfo.is_active = !productInfo.is_active;
    }
    try {
      const response = await updateGoods(productInfo);
      if (response.code === 0) {
        Object.assign(productInfo, response);
        if (!Array.isArray(productInfo.units)) {
          productInfo.units = [];
        }
        uni.navigateBack({ delta: 1 });
      } else {
        uni.showToast({
          title: response.msg,
          icon: "none",
        });
      }
      return response;
    } catch (error: any) {
      console.error("保存商品信息失败：", error.response?.data || error);
      throw error;
    } finally {
      uni.removeStorageSync('warehouseList');
    }
  } else {
    try {
      const response = await saveGoods(productInfo);
      Object.assign(productInfo, response);
      if (!Array.isArray(productInfo.units)) {
        productInfo.units = [];
      }
      if (response.code === 0) {
        uni.showToast({
          title: "保存成功",
          icon: "success",
        });
      } else {
        uni.showToast({
          title: response.msg,
          icon: "none",
        });
      }
      setTimeout(() => {
        uni.navigateBack({ delta: 1 });
      }, 1000);
      return response;
    } catch (error: any) {
      console.error("保存商品信息失败：", error.response?.data || error);
      throw error;
    } finally {
      uni.removeStorageSync('warehouseList');
    }
  }
};

/**
 * 删除商品
 */
const delGoods = async (): Promise<void> => {
  try {
    const res = await deleteGoods(productInfo);
    console.log('删除商品响应:', res);
    if (res.code === 0) {
      uni.showToast({
        title: "删除成功",
        icon: "success",
      });
      promptShow.value = false;
      setTimeout(() => {
        uni.navigateBack({ delta: 1 });
      }, 3000);
    } else {
      uni.showToast({
        title: res.msg || "删除失败",
        icon: "none",
        duration: 3000
      });
      promptShow.value = false;
    }
  } catch (err: any) {
    console.error('删除商品错误:', err);
    uni.showToast({
      title: err.msg || err.message || "删除失败",
      icon: "none",
      duration: 3000
    });
    promptShow.value = false;
  }
};

/**
 * 提交表单
 */
const submitForm = (): void => {
  validateForm()
    .then(() => {
      return submitProductInfo();
    })
    .catch((err) => {
      console.log(err);
      uni.showToast({
        title: (err.response && err.response.data && err.response.data.msg) || "提交失败",
        icon: "none",
      });
    });
};

/**
 * 更新商品信息
 * @param val - 新的商品信息
 */
const updateProductInfo = (val: any): void => {
  Object.assign(productInfo, val);
};

// 生命周期钩子
onMounted(() => {
  // 如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
  nextTick(() => {
    if (uForm.value) {
      (uForm.value as any).setRules(productRules);
    }
  });
});

onLoad((options: { item?: string }) => {
  console.log(options);
  if (options.item) {
    try {
      goodsData.value = JSON.parse(decodeURIComponent(options.item));
      if (Number(goodsData.value.total_stock) !== 0) {
        isInventory.value = true;
      }
      console.log(goodsData.value);
      if (Number(goodsData.value.total_stock) === 0) {
        isInit.value = true;
      }
      updateFormData(goodsData.value);
      console.log(productInfo);
    } catch (error) {
      console.error("解析商品数据失败：", error);
    }
  } else {
    isInit.value = true;
  }
});
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  background-color: #efefef;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 17px;
  overflow: hidden;
}

.info {
  width: 90%;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

  .info_title {
    font-size: 30rpx;
    font-weight: 700;
    color: #0f40f5;
    padding: 20rpx 0;
    border-bottom: 1px solid #cacaca;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .price_title {
      display: flex;
      align-items: flex-start;
    }

    text {
      border-left: 3px solid #0f40f5;
      padding-left: 10rpx;
    }
  }
}

.expandBtn {
  width: 100%;

  .expand_text {
    width: 100%;
    font-size: 25rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10rpx;
  }
}
  .expand_text {
    width: 100%;
    font-size: 25rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10rpx;
  }

.pricesShowRegion {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.prices_item {
  width: 90%;
  border: 1px solid black;
  margin: 10rpx 0;
  display: flex;

  .hostTag {
    width: 30rpx;
    height: 30rpx;
    background-color: #ed7c2e;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 30rpx;
    padding: 5rpx;
    margin: 5rpx;
  }

  .hostTag_reserveASeat {
    width: 40rpx;
    height: 40rpx;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    padding: 5rpx;
    margin: 5rpx;
  }

  .delIcon {
    margin: 10rpx 6rpx;
    width: 130rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
    position: relative;

    .proportion {
      font-size: 22rpx;
    }

    .delete-btn {
      position: absolute;
      right: -4px;
      top: -4px;
      width: 100%;
      height: 100%;
      background-color: #e25245;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 20rpx;
    }
  }

  .content {
    width: 80%;
    height: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 23rpx;
    font-weight: 700;
    margin: 10rpx 0;

    .content_item {
      width: 100%;
      height: 100%;
      display: flex;

      .content_item_attribute {
        width: 50%;
        display: flex;
        margin: 7rpx;
        align-items: center;
      }

      .attributeName_left {
        width: 45px;
        display: inline-block;
        text-align: justify;
        text-align-last: justify;
      }

      .attributeName_right {
        width: 45px;
        display: inline-block;
        text-align: justify;
        text-align-last: justify;
      }
    }
  }
}

.inventory {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6d6d6d;
  font-weight: 500;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

//库存数量弹出层
.warehouse_info {
  width: 90%;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;

  .warehouse_info_title {
    font-size: 25rpx;
    font-weight: 500;
    color: black;
    padding: 20rpx 0;
    border-bottom: 1px solid #cacaca;
    display: flex;
    align-items: center;
    justify-content: space-between;

    text {
      border-left: 3px solid #0f40f5;
      padding-left: 10rpx;
    }
  }
}

.quantitys {
  display: flex;
  flex-direction: column;

  .quantitys_item {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #cacaca;
    padding: 20rpx 0;

    .quantitys_item_label {
      font-size: 30rpx;
    }
  }
}

.stockPopup {
  height: 800rpx;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
  border-bottom: 1px solid #cacaca;
}

.switchingWarehouses {
  width: 90%;
  margin: 0 auto;
  display: flex;
  flex-direction: row-reverse;
  position: relative;
}

.switchingWarehouses_text {
  display: flex;
  align-items: center;
  font-size: 25rpx;
  color: #3588e5;
  font-weight: 500;
}

.switchingWarehouses_content {
  position: absolute;
  top: 25px;
  right: 0;
  height: 175rpx;
  overflow-y: auto;
  padding: 10rpx 10rpx;
  border-radius: 10rpx;
  border: 1px solid #cacaca;
  background-color: #fff;
  box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
}

.switchingWarehouses_content_item {
  padding: 10rpx 20rpx;
  border-bottom: 1px solid #cacaca;
}

.switchingWarehouses_content_item:last-child {
  border-bottom: none;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

::v-deep .u-form-item__body__left__content__label {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: space-between !important; // 两端对齐
  flex: 1 !important;
  margin-left: 25rpx !important; // 注意：rpx 是小程序的单位
  width: 100%;
  text-align: justify;
  text-align-last: justify;
  font-size: 30rpx;
  font-weight: 700;
}

::v-deep .u-popup {
  flex: none !important;
}

::v-deep .u-form-item__body__left__content__required.data-v-5e7216f1 {
  position: absolute;
  left: 0px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}

.delete-price-btn {
  position: relative;
  cursor: pointer;
}

.delete-tooltip {
  position: absolute;
  left: -42px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;

  .delete-tooltip-content {
    background-color: #e25245;
    padding: 5px 12px;
    display: flex;
    align-items: center;
    flex-direction: column;

    .delete-icon {
      display: flex;
      align-items: center;
    }

    text {
      color: #fff;
      font-size: 14px;
    }
  }
}

.upload-section {
  padding: 20rpx;
  background-color: #fff;
  margin-top: 20rpx;

  .upload-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
  }

  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .image-item {
      position: relative;
      width: 200rpx;
      height: 200rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }

      .delete-btn {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload-btn {
      width: 200rpx;
      height: 200rpx;
      border: 2rpx dashed #ddd;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
    }
  }
}

::v-deep .u-form-item__body__right__message.data-v-5e7216f1 {
  font-size: 12px;
  line-height: 12px;
  color: #f56c6c;
  text-align: right;
}
</style>