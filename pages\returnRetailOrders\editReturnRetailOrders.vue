<template>
    <view class="container">
        <scroll-view scroll-y class="content">
            <!-- 基本信息 -->
            <view class="info">
                <view class="info_title">
                    <text>基本信息</text>
                </view>
                <view class="form">
                    <u--form :model="returnRetailInfo" :rules="rules" ref="uForm" labelPosition="left">
                        <u-form-item :label-width="'180rpx'" :label="$t('customer')" borderBottom required>
                            <u--input v-model="returnRetailInfo.customer_name" border="none" placeholder="请选择客户"
                                inputAlign="right" disabled disabledColor="#fff"></u--input>
                        </u-form-item>
                        <u-form-item :label-width="'180rpx'" label="退货日期" borderBottom>
                            <u--input v-model="returnRetailInfo.return_date" border="none" placeholder="请选择日期"
                                inputAlign="right" disabled disabledColor="#fff"></u--input>
                        </u-form-item>
                        <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
                            <u--input v-model="returnRetailInfo.id" border="none" placeholder="自动生成" inputAlign="right"
                                disabled disabledColor="#fff"></u--input>
                        </u-form-item>
                        <u-form-item :label-width="'180rpx'" label="关联订单" borderBottom>
                            <u-row>
                                <u-col :span="returnRetailInfo.order_no ? 10.5 : 12">
                                    <u--input v-model="returnRetailInfo.order_no" border="none" placeholder="请选择关联订单"
                                        inputAlign="right" disabled disabledColor="#fff" @tap="addGoods(0)"></u--input>
                                </u-col>
                                <u-col span="0.5"></u-col>
                                <u-col span="1">
                                    <view @click="removeRetailInCode">
                                        <i-close-one theme="filled" size="20" fill="#d13b3b"
                                            v-if="returnRetailInfo.order_no" />
                                    </view>
                                </u-col>
                            </u-row>
                        </u-form-item>
                        <u-form-item :label-width="'180rpx'" label="仓库" borderBottom required>
                            <u-row>
                                <u-col :span="returnRetailInfo.warehouse_name ? 10.5 : 12">
                                    <u--input v-model="returnRetailInfo.warehouse_name" border="none"
                                        placeholder="请选择仓库" inputAlign="right" disabled disabledColor="#fff"
                                        @tap="openInventorySelection()"></u--input>
                                </u-col>
                                <u-col span="0.5"></u-col>
                                <u-col span="1">
                                    <view @click="removeWarehouse">
                                        <i-close-one theme="filled" size="20" fill="#d13b3b"
                                            v-if="returnRetailInfo.warehouse_name" />
                                    </view>
                                </u-col>
                            </u-row>

                        </u-form-item>
                    </u--form>
                </view>
            </view>

            <!-- 商品清单 -->
            <merch-bill :items="returnRetailInfo.items" type="returnRetail" :prohibit-modification="isDisabled"
                :retail-in-code="returnRetailInfo.order_no" @add-goods="addGoods"
                @open-product-details="openProductDetails" @amount-change="handleAmountChange" />

            <!-- 结算账户 -->
            <view class="info">
                <view class="info_title">
                    <text>结算账户</text>
                </view>
                <view class="form">
                    <u--form labelPosition="left">
                        <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom
                            :required="Number(returnRetailInfo.pay_amount) > 0">
                            <u-row>
                                <u-col :span="returnRetailInfo.payment_method_name ? 10.5 : 12">
                                    <u--input v-model="returnRetailInfo.payment_method_name" border="none"
                                        placeholder="请选择结算账户" inputAlign="right" disabled disabledColor="#fff"
                                        @tap="openAccountSelector(0)"></u--input>
                                </u-col>
                                <u-col span="0.5"></u-col>
                                <u-col span="1">
                                    <view @click="removePayMethod">
                                        <i-close-one theme="filled" size="20" fill="#d13b3b"
                                            v-if="returnRetailInfo.payment_method_name" />
                                    </view>
                                </u-col>
                            </u-row>
                        </u-form-item>
                        <u-form-item :label-width="'180rpx'" label="本次退款" borderBottom>
                            <u--input v-model="returnRetailInfo.pay_amount" border="none" placeholder="0"
                                inputAlign="right" disabled disabledColor="color: #fff" @input="changePayAmount" />
                        </u-form-item>
                    </u--form>
                </view>
            </view>

            <!-- 备注 -->
            <view class="info">
                <view class="info_title">
                    <text>备注</text>
                </view>
                <view class="remark">
                    <u--textarea v-model="returnRetailInfo.remark" placeholder="请输入备注" autoHeight border="none"
                        :disabled="isDisabled"></u--textarea>
                </view>
            </view>

            <!-- 附件信息 -->
            <view>
                <imageUpload :prohibitModification="isDisabled" />
            </view>

            <!-- 底部操作栏 -->
            <view class="operation" v-if="isDisabled">
                <u-button type="primary" text="提交" @click="savePrompt"></u-button>
            </view>
        </scroll-view>
        <!-- 语音输入组件 -->
        <view class="input-area">
            <Input style="height: 100%" :isShowCamera="true" />
        </view>
        <!-- 交互组件 -->
        <interactive :isShowAddBtn="false" :jumpToId="4" />

        <!-- 审核模态框 -->
        <u-modal :show="isAuditShow" :zoom="isAuditShowZoom" :title="auditTitle" :content="auditContent"
            :closeOnClickOverlay="true" :showCancelButton="true" :showConfirmButton="true" @confirm="save(1)"
            @cancel="cancel" @close="cancel"></u-modal>

        <!-- 选择器、商品详情、其他费用弹窗等可按需添加 -->
        <searchSelector v-model:selectorShow="selectorShow" :selectType="selectType" @confirm="selectCustomer"
            @close="closePopup" />

        <!-- 商品详细信息弹出层 -->
        <productDetails :productDetailsShow="productDetailsShow" :type="productDetailType" :productData="productData"
            :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing" @close="closeProductDetails"
            @confirm="handleProductDetails" />

        <!-- 库存弹出层 -->
        <inventorySelection :inventorySelectionShow="inventorySelectionShow" :isSelect="true"
            @close="closeInventorySelection" @save="confirmInventory" />

        <!-- 结算账户弹出层 -->
        <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector"
            @confirm="handleAccount" />
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import searchSelector from "@/components/searchSelector.vue";
import productDetails from "@/components/productDetails.vue";
import inventorySelection from "@/components/inventorySelection/inventorySelection.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import MerchBill from "@/components/merchbill.vue";
import { addReturnRetail } from "@/api/returnRetail";
import eventBus from "@/utils/eventBus";

const uForm = ref(null);

const accountSelectShow = ref(false); //结算账户弹出层
const inventorySelectionShow = ref(false); //仓库选择弹出层显示状态
const productDetailsShow = ref(false); //商品详细信息弹出层显示状态
const productData = ref({});//物品信息
const productDetailType = ref(0);
// 是否展开商品清单
const isExpandGoodsList = ref(false);
// 审核模态框显示状态
const isAuditShow = ref(false);
const isAuditShowZoom = ref(false);
const auditTitle = ref("温馨提示"); // 审核模态框标题
const auditContent = ref("审核后将不可撤回，是否确认保存并审核？"); // 审核模态框内容
const activeQtyIndex = ref(-1); // 当前操作数量的商品索引
const returnRetailList = ref<any[]>([]); // 退货商品选择列表
// 退货单表单数据
const returnRetailInfo = reactive<any>({
    order_type: 'retail',
    customer_name: "", // 客户名称
    customer: "", // 客户ID
    return_date: "", // 退货日期
    id: "", // 单据编号id
    sales_out: "", // 销售单据编号id
    order_no: "", // 关联订单编号
    warehouse: "", // 仓库
    warehouse_name: "", //仓库名称
    items: [],
    total_amount: 0, // 实际退款金额/优惠后金额
    discountRate: 0, // 优惠率
    discount: 0, // 优惠金额
    actual_amount: 0, // 合计金额
    other_fee: 0, // 其他费用
    pay_amount: 0, // 本次退款
    remark: "", // 备注
    allocated_cost: "", //其他费用
    allocated_pay_amount: "", //其他费用金额
    allocated_payment_method: '',//其他费用支付方式
    payment_method: "", //结算账户
    payment_method_name: "", //结算账户名称
    is_draft: false,//暂存
});
// 表单校验规则
const rules = reactive<any>({
    "customer_name": {
        type: "string",
        required: true,
        message: "请选择客户",
        trigger: ["blur", "change"],
    },
    "warehouse_name": {
        type: "string",
        required: true,
        message: "请选择仓库",
        trigger: ["blur", "change"],
    },
});
// 选择器相关
const selectorShow = ref(false); // 选择器弹窗显示状态
const selectType = ref(0); // 选择器类型
const customerList = ref<any[]>([]); // 客户列表
const isCustomerMore = ref(true); // 是否还有更多客户
const customerPageParams = reactive({
    page: 1,
    page_size: 20,
});
const isQueryWarehouse = ref(false);
const isDisabled = ref(false);//是否为编辑状态
const disabledTitle = ref('现为提交状态，禁止修改');
const isEdit = ref(false);

//禁止修改信息
const disabledInfo = () => {
    if (isDisabled.value) {
        uni.showToast({
            title: disabledTitle.value,
            icon: "none",
            mask: true,
        })
        return true;
    }
    return false;
};

const showQtyArrows = (index: number) => {
    activeQtyIndex.value = index;
};
const hideQtyArrows = () => {
    activeQtyIndex.value = -1;
};
const changeQuantity = (item: any, val: number) => {
    const targetItem = returnRetailInfo.items.find(
        (infoItem: any) => infoItem.id === item.id
    );
    if (!targetItem) return;
    if (val > Number(item.remaining_quantity)) {
        uni.showToast({
            title: "退货数量大于在库数量",
            icon: "none",
            mask: true,
        });
        targetItem.quantity = Number(item.remaining_quantity);
        calcTotalAndDiscount();
        return;
    }
    targetItem.quantity = val;
    calcTotalAndDiscount();
};
//减少数量
const decreaseQty = (index: number, item: any) => {
    if (item && item.quantity > 1) {
        returnRetailInfo.items.forEach((infoItem: any) => {
            if (infoItem.id === item.id) {
                infoItem.quantity = Number(infoItem.quantity) - 1;
            }
        });
        calcTotalAndDiscount();
    } else {
        uni.showToast({
            title: "数量不能小于1",
            icon: "none",
            mask: true,
        });
    }
};
//增加数量
const increaseQty = (index: number, item: any) => {
    if (item && item.quantity < Number(item.remaining_quantity)) {
        returnRetailInfo.items.forEach((infoItem: any) => {
            if (infoItem.id === item.id) {
                infoItem.quantity = Number(infoItem.quantity) + 1;
            }
        });
        calcTotalAndDiscount();
    } else {
        uni.showToast({
            title: "退货数量大于在库数量",
            icon: "none",
            mask: true,
        });
    }
};
const calcTotalAndDiscount = () => {
    // 计算总金额和优惠相关逻辑
    let total = 0;
    returnRetailInfo.items.forEach((item: any) => {
        total += (item.return_price || 0) * (item.quantity || 0);
    });
    returnRetailInfo.actual_amount = total.toFixed(2); // 合计金额
    const discountAmount = (total * (returnRetailInfo.discountRate || 0) / 100).toFixed(2);
    returnRetailInfo.discount = discountAmount;
    returnRetailInfo.total_amount = (total - Number(discountAmount)).toFixed(2); // 实际退款金额/优惠后金额
};
const changeDiscount = () => {
    calcTotalAndDiscount();
};
const changeDiscountRate = () => {
    const total = Number(returnRetailInfo.actual_amount); // 使用合计金额
    const discount = Number(returnRetailInfo.discount);
    if (discount > total) {
        uni.showToast({
            title: "优惠金额不能大于总金额",
            icon: "none",
        });
        returnRetailInfo.discount = total.toFixed(2);
    }
    returnRetailInfo.discountRate = ((discount / total) * 100).toFixed(2);
};
const changePayAmount = (e: any) => {
    const val = e.detail.value;
    returnRetailInfo.pay_amount = val;
};
const changeDebt = () => {
    // 欠款相关逻辑
};
const openSelector = (type: number) => {
    selectType.value = type;
    selectorShow.value = true;
};
const closePopup = () => {
    selectorShow.value = false;
};
const selectCustomer = (item: any) => {
    returnRetailInfo.customer = item.id;
    returnRetailInfo.customer_name = item.name;
    selectorShow.value = false;
};


const removeRetailInCode = () => {
    returnRetailInfo.id = "";
    returnRetailInfo.order_no = "";
    returnRetailInfo.items = [];
};
const removeWarehouse = () => {
    returnRetailInfo.warehouse = "";
    returnRetailInfo.warehouse_name = "";
};
const removePayMethod = () => {
    returnRetailInfo.payment_method = "";
    returnRetailInfo.payment_method_name = "";
};
const removeOtherCosts = () => {
    returnRetailInfo.allocated_cost = { totalAmount: "" };
};
const openInventorySelection = () => {
    inventorySelectionShow.value = true;
};
const closeInventorySelection = () => {
    inventorySelectionShow.value = false;
};
const confirmInventory = (item: any) => {
    returnRetailInfo.warehouse = item.id;
    returnRetailInfo.warehouse_name = item.name;
    closeInventorySelection();
};

const openAccountSelector = (type: number) => {
    accountSelectShow.value = true;
};
const closeAccountSelector = () => {
    accountSelectShow.value = false;
};
const handleAccount = (item: any) => {
    returnRetailInfo.payment_method = item.id;
    returnRetailInfo.payment_method_name = item.account_name;
    closeAccountSelector();
};
const addGoods = (type: number) => {
    if (disabledInfo()) {
        return;
    }
    if (type === 0) {
        // 选择关联订单
        uni.navigateTo({
            url: "/pages/retailOrders/retailOrders?isSelect=true&customer=" + returnRetailInfo.customer
        });
    } else {
        if (!returnRetailInfo.order_no) {
            // 选择关联订单
            uni.navigateTo({
                url: "/pages/retailOrders/retailOrders?isSelect=true&customer=" + returnRetailInfo.customer
            });
            return;
        }
        // 添加商品
        uni.navigateTo({
            url: '/components/productSelection?data=' + JSON.stringify(returnRetailList.value) + "&type=7"
        });
    }
};
const openProductDetails = (item: any) => {
    productData.value = item;
    productDetailsShow.value = true;
    productDetailType.value = 1;
};
const closeProductDetails = () => {
    productDetailsShow.value = false;
};
const handleProductDetails = (data: any) => {
    // 处理商品详情确认
    const index = returnRetailInfo.items.findIndex((item: any) => item.id === data.id);
    if (index !== -1) {
        returnRetailInfo.items[index] = data;
    } else {
        returnRetailInfo.items.push(data);
    }
    closeProductDetails();
};
const handleDelBtnPricing = () => {
    // 删除商品相关逻辑
    returnRetailInfo.items = returnRetailInfo.items.filter((item: any) => item.id !== productData.value.id);
    closeProductDetails();
};
const savePrompt = () => {
    isAuditShow.value = true;
    isAuditShowZoom.value = true;
};
const save = (isCommit = 0) => {
    // 保存或提交逻辑
    returnRetailInfo.is_commit = isCommit;

    // 验证必填字段
    if (!returnRetailInfo.customer) {
        uni.showToast({ title: '请选择客户', icon: 'none' });
        return;
    }

    if (!returnRetailInfo.warehouse) {
        uni.showToast({ title: '请选择仓库', icon: 'none' });
        return;
    }

    if (!returnRetailInfo.items || returnRetailInfo.items.length === 0) {
        uni.showToast({ title: '请添加退货商品', icon: 'none' });
        return;
    }

    console.log('保存前的数据:', returnRetailInfo);

    uForm.value.validate().then((valid: boolean) => {
        if (valid) {
            addReturnRetail(returnRetailInfo).then(res => {
                console.log('新增响应:', res);
                if (res.code === 0) {
                    uni.showToast({ title: "新增成功", icon: "success" });
                    uni.navigateBack();
                }
            }).catch(err => {
                console.error('新增失败:', err);
                uni.showToast({ title: err.message || '新增失败', icon: 'none' });
            });
        }
    });
};

const cancel = () => {
    isAuditShow.value = false;
};

// 处理金额变化
const handleAmountChange = (amountData: any) => {
    returnRetailInfo.actual_amount = amountData.totalAmount; // 合计金额
    returnRetailInfo.discountRate = amountData.discountRate;
    returnRetailInfo.discount = amountData.discount;
    returnRetailInfo.total_amount = amountData.actualAmount; // 实际退款金额/优惠后金额
    returnRetailInfo.pay_amount = amountData.actualAmount;
};

watch(() => returnRetailInfo.discountRate, (newVal) => {
    if (newVal > 100) {
        uni.showToast({ title: "优惠率不能大于100", icon: "none" });
        returnRetailInfo.discountRate = 100;
        return;
    }
    // 只在输入框@blur时联动，watch不自动算
});

watch(() => returnRetailInfo.items, () => {
    calcTotalAndDiscount();
}, { deep: true });

onMounted(() => {
    if (uForm.value) {
        uForm.value.setRules(rules);
    }
});

onLoad((options: any) => {
    console.log(options);

    if (options.isEdit) {
        isEdit.value = options.isEdit
    }

    if (options.data) {
        const data = JSON.parse(options.data);

        // 映射返回的数据到 returnRetailInfo 结构
        Object.assign(returnRetailInfo, {
            order_type: data.order_type || 'retail',
            customer_name: data.customer_info?.name || "", // 从 customer_info.name 获取
            customer: data.customer || "", // 客户ID
            return_date: data.created_at ? data.created_at.split(' ')[0] : "", // 从 created_at 提取日期
            id: data.id || "", // 单据编号id
            sales_out: data.sales_out || "", // 销售单据编号id
            order_no: data.sales_out_info?.order_no || "", // 从 sales_out_info.order_no 获取
            warehouse: data.warehouse || "", // 仓库
            warehouse_name: data.warehouse_info?.name || "", // 从 warehouse_info.name 获取
            items: data.items,
            total_amount: Number(data.total_amount) || 0, // 实际退款金额/优惠后金额
            discountRate: 0, // 优惠率 - 需要根据 discount 和 actual_amount 计算
            discount: Number(data.discount) || 0, // 优惠金额
            actual_amount: 0, // 合计金额 - 需要计算 total_amount + discount
            other_fee: 0, // 其他费用
            pay_amount: Number(data.total_amount) || 0, // 本次退款，默认等于实际退款金额
            remark: data.remark || "", // 备注
            allocated_cost: "", //其他费用
            allocated_pay_amount: "", //其他费用金额
            allocated_payment_method: '',//其他费用支付方式
            payment_method: data.payment_method || "", //结算账户
            payment_method_name: data.payment_method_info?.name || "", //结算账户名称
            is_draft: data.is_draft || false,//暂存
        });

        // 计算合计金额 = 实际退款金额 + 优惠金额
        returnRetailInfo.actual_amount = (Number(returnRetailInfo.total_amount) + Number(returnRetailInfo.discount)).toFixed(2);

        // 计算优惠率
        if (Number(returnRetailInfo.actual_amount) > 0) {
            returnRetailInfo.discountRate = ((Number(returnRetailInfo.discount) / Number(returnRetailInfo.actual_amount)) * 100).toFixed(2);
        }

        console.log('映射后的数据:', returnRetailInfo);
        return;
    }
});

onShow(() => {
    eventBus.$on("selectRetailOrder", (data: any) => {
        console.log("接收到选择的订单数据:", data);
        returnRetailInfo.sales_out = data.id;
        returnRetailInfo.order_no = data.order_no;
        returnRetailInfo.customer = data.customer;
        returnRetailInfo.customer_name = data.customer_name;
        returnRetailList.value = data.items;
    });
    eventBus.$on("selectGoodsList", (data: any) => {
        console.log("接收到选择的商品数据:", data);
        if (returnRetailInfo.items.length == 0) {
            returnRetailInfo.items = data;
            return;
        }
        data.forEach((newItem: any) => {
            const existingIndex = returnRetailInfo.items.findIndex(
                (item: any) => item.id === newItem.id && item.unit === newItem.unit
            );
            if (existingIndex !== -1) {
                returnRetailInfo.items[existingIndex] = newItem;
            } else {
                returnRetailInfo.items.push(newItem);
            }
        });

        console.log(returnRetailInfo.items);

        calcTotalAndDiscount();
    });

    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    returnRetailInfo.return_date = `${year}-${month}-${day}`;
});

// 确保在组件卸载时取消事件监听，防止内存泄漏
// onUnmounted(() => {
//     eventBus.$off("selectRetailOrder");
//     eventBus.$off("selectGoodsList");
// });

</script>

<style scoped lang="scss">
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.content {
    overflow: auto;
}

.info {
    background-color: #fff;
    margin-bottom: 20rpx;
}

.info_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    border-bottom: 1px solid #eee;
    font-size: 28rpx;
}

.form {
    padding: 0 20rpx;
    width: 95%;
    margin: 0 auto;
}

.otherExpenses {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10rpx;
    padding: 10rpx 0;
    border-bottom: 1px solid #eee;

}

.total-row-title {
    width: 80px;
    font-size: 28rpx;
    margin-left: 30rpx;
    color: #303133;
}

.goods-list {}

.goods_item {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
    margin-bottom: 20rpx;
    position: relative;
}

.goods_item_click {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 10px 0;
}

.goods_img {
    width: 48px;
    height: 48px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.goods_img image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.goods_main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
}

.goods_name {
    font-size: 16px;
    font-weight: bold;
    color: #222;
}

.goods_id {
    font-size: 12px;
    color: #888;
    margin: 2px 0;
}

.goods_extra {
    font-size: 12px;
    color: #888;
    margin-bottom: 2px;
}

.goods_price {
    display: flex;
    align-items: center;
    margin-top: 2px;
}

.price {
    color: #e22;
    font-size: 14px;
    font-weight: bold;
    margin-right: 2px;
}

.unit {
    color: #e22;
    font-size: 12px;
}



.qty_x {
    font-size: 15px;
    font-weight: bold;
    margin-right: 2px;
}

.qty_control {
    display: flex;
    align-items: center;
    position: relative;
}

.qty_input {
    width: 40px;
    height: 22px;
    border: 1px solid #222;
    border-radius: 3px;
    text-align: center;
    font-size: 14px;
    background: #fff;
    color: #222;
    margin-left: 2px;
}

.arrow {
    font-size: 18px;
    color: #888;
    margin-left: 10px;
}

.total-info {
    font-size: 28rpx;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;
    padding: 10rpx 0;
    border-bottom: 1px solid #eee;
    margin-left: 30rpx;

    .total-row-right {
        display: flex;
        align-items: center;
    }

    text:nth-child(1) {
        width: 80px;
        display: inline-block;
        text-align-last: justify;
        text-align: justify;
        text-justify: distribute-all-lines;
    }
}

.telescoping {
    display: flex;
    justify-content: center;
    align-items: center;
}

.remark {
    padding: 20rpx;
    width: 95%;
    margin: 0 auto;
}

.upload-area {
    padding: 20rpx;
    width: 95%;
    margin: 0 auto;
}

.add-icon {
    padding: 10rpx;
    cursor: pointer;
}

.operation {
    width: 90%;
    height: 80rpx;
    margin: 10rpx auto;
    padding: 10rpx 20rpx;
    background-color: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

::v-deep .operation .u-button {
    height: 30px;
    width: 25%;
    position: relative;
    align-items: center;
    justify-content: center;
    display: flex;
    flex-direction: row;
    box-sizing: border-box;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
    border-radius: 4px;
    padding-top: 0 !important;
    padding-left: 0 !important;
    padding-bottom: 0 !important;
    padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
    line-height: 26px;
    text-align: left;
    color: #303133;
    height: 24px;
    font-size: 15px;
    flex: 1;
    width: 125rpx;
}

::v-deep .u-form-item__body__left__content__required.data-v-5e7216f1 {
    position: absolute;
    left: 0px;
    color: #f56c6c;
    line-height: 20px;
    font-size: 20px;
    top: 3px;
}

::v-deep .u-form-item__body__left__content__label.data-v-5e7216f1 {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    margin-left: 30rpx;
}

.other-expenses-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.icon-group {
    display: flex;
    align-items: center;
    gap: 10rpx;
}
</style>
