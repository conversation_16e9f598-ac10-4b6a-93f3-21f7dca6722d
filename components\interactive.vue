<template>
	<view class="interactiveArea" :style="dynamicBottomStyle">
		<view class="aiRecord" :style="{ 'justify-content': isShowAddBtn ? 'space-between' : 'flex-start' }">
			<view class="aiAvatar" @click="expand(0)">
				<image src="/static/img/aiAssistant.png" mode="" v-if="isExpandAi"></image>
				<view class="aiHideAvatar" v-if="!isExpandAi">
					<i-dot theme="outline" size="20" fill="#2b85e4" />
				</view>
			</view>
			<view class="recordContent" v-if="isExpandAi">
				<text>您好，我是你的ai语音助手仓小助</text>
				<view class="openHistoricalDialogue" @click="openHistory">
					<i-history theme="outline" size="11" fill="#ff0000" />
					<text class="history-text">查看对话历史</text>
				</view>
			</view>
			<view class="addBtn" @click="addProduct" v-if="isShowAddBtn">
				<i-plus theme="outline" size="24" fill="#fff" />
			</view>
		</view>
		<view class="userRecord">
			<view class="userAvatar" @click="expand(1)">
				<i-dot theme="outline" size="20" fill="#333" />
			</view>
			<view class="recordContent" v-if="isExpandUser">
				<text>仓小助，我想查看我的商品列表</text>
			</view>
		</view>

		<historyConversationRecord :show="show" @update:show="updateParentShow" :dialogRecords="dialogRecords" />
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import historyConversationRecord from '@/components/historyConversationRecord/historyConversationRecord.vue';

interface Props {
  isShowAddBtn: boolean;
  jumpToId: number;
}

const props = defineProps<Props>();

const safeDistance = ref(0); //底部安全距离
const show = ref(false); //是否展示对话记录框
const isExpandAi = ref(true); //是否展示ai对话框
const isExpandUser = ref(true); //是否展示用户对话框
const dialogRecords = ref([ //本次对话记录
  {
    userRecord: '亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
    aiRecord: '根据“总共”“商品”等关键词为你生成表单'
  },
  {
    userRecord: '亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
    aiRecord: '根据“总共”“商品”等关键词为你生成表单'
  },
  {
    userRecord: '亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
    aiRecord: '根据“总共”“商品”等关键词为你生成表单'
  },
  {
    userRecord: '亲爱的仓小助，我是仓库管理员，我想知道我们总共有多少件商品？帮我列出来',
    aiRecord: '根据“总共”“商品”等关键词为你生成表单'
  },
]);

const dynamicBottomStyle = computed(() => {
  // 动态计算 bottom 的值
  return `bottom: calc(10vh + ${safeDistance.value}rpx)`;
});

const getRectInfo = () => {
  // 获取系统信息
  const sysInfo = uni.getSystemInfoSync();
  console.log(sysInfo);
  safeDistance.value = sysInfo.safeAreaInsets.bottom;
};

//打开本次对话记录框
const openHistory = () => {
  show.value = true;
};

// 更新展示对话框状态
const updateParentShow = (newMessage: boolean) => {
  show.value = newMessage;
};

//点击展开收缩聊天框
const expand = (index: number) => {
  if (index == 0) {
    isExpandAi.value = !isExpandAi.value;
  } else if (index == 1) {
    isExpandUser.value = !isExpandUser.value;
  }
};

const addProduct = () => {
  switch (props.jumpToId) {
    case 1:
      uni.navigateTo({
        url: '/pages/commodityManagement/editProduct/editProduct'
      });
      break;
    case 2:
      uni.navigateTo({
        url: '/pages/purchase/editPurchase/editPurchase'
      });
      break;
    case 3:
      uni.navigateTo({
        url: '/pages/purchaseGoods/editPurchaseGoods/editPurchaseGoods'
      });
      break;
    case 4:
      uni.navigateTo({
        url: '/pages/returnGoodsOrder/editReturnGoodsOrder/editReturnGoodsOrder'
      });
      break;
    case 5:
      uni.navigateTo({
        url: '/pages/salesOrder/addSalesOrder'
      });
      break;
    case 6:
      uni.navigateTo({
        url: '/pages/retailOrders/editRetailOrders/editRetailOrders'
      });
      break;
    case 7:
      uni.navigateTo({
        url: '/pages/salesReturn/addSalesReturnOrder'
      });
      break;
    case 8:
      uni.navigateTo({
        url: '/pages/wholesaleOrders/editWholesaleOrders/editWholesaleOrders'
      });
      break;
    case 9:
      uni.navigateTo({
        url: '/pages/returnRetailOrders/editReturnRetailOrders'
      });
      break;
    default:
      break;
  }
};

onMounted(() => {
  getRectInfo();
  setTimeout(() => {
    isExpandAi.value = false;
    isExpandUser.value = false;
  }, 2000);
});

</script>

<style lang="scss" scoped>
//交互区域
.interactiveArea {
	position: absolute;
	left: 0;
	width: 100%;
	display: flex;
	flex-direction: column;
	pointer-events: none; // 新增：让整个区域不响应点击

	.aiRecord {
		width: 95%;
		min-height: 61px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-left: 20rpx;

		.aiAvatar {
			width: 100rpx;
			height: 100rpx;
			pointer-events: auto; // 新增：允许点击

			.aiHideAvatar {
				width: 40rpx;
				height: 40rpx;
				margin-left: 10rpx;
				border-radius: 50%;
				border: 2px solid #2b85e4;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			image {
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
		}

		.addBtn {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			background-color: #2b85e4;
			margin-left: 20rpx;
			margin-bottom: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 2px 5px rgb(43 133 228);
			pointer-events: auto; // 新增：允许点击
			z-index: 1;
		}
	}

	.recordContent {
		width: 60%;
		margin-left: 20rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		background-color: #ecf5ff;
		font-size: 28rpx;
		border: 1px #bbbbbb solid;

		.openHistoricalDialogue .history-text {
							color: red;
							font-size: 23rpx;
							font-weight: 400;
						}
	}

	.userRecord {
		display: flex;
		align-items: center;
		flex-direction: row-reverse;
		margin: 20rpx;
		min-height: 41px;
		pointer-events: auto; // 新增：允许点击

		.userAvatar {
			width: 40rpx;
			height: 40rpx;
			margin-left: 10rpx;
			border-radius: 50%;
			border: 2px solid #3c4353;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
</style>
