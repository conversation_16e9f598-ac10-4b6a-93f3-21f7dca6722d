import request from '@/utils/request.ts';
import store from '@/store';

// 定义退货商品项接口
interface ReturnGoodsItem {
  id?: string; // 商品项ID，可选
  stock: string; // 库存ID
  item_id: string; // 商品ID
  item_name: string; // 商品名称
  purchase_in_item?: string; // 采购入库项ID，可选
  purchase_in_code?: string; // 采购入库单号，可选
  unit: string; // 单位ID
  unit_name: string; // 单位名称
  quantity: number; // 数量
  return_price: number; // 退货价格
  is_defective: boolean; // 是否有缺陷
  defect_description?: string; // 缺陷描述，可选
  remaining_quantity?: number; // 剩余数量，可选
  remark?: string; // 备注，可选
  track_id?: string; // 追踪ID，可选
  batch_number?: string; // 批次号，可选
  [key: string]: any; // 允许其他属性
}

// 定义退货订单数据接口
interface ReturnGoodsData {
  id?: string; // 订单ID，可选
  is_commit?: number; // 是否提交
  supplier: string; // 供应商ID
  supplier_name?: string; // 供应商名称
  warehouse: string; // 仓库ID
  warehouse_name?: string; // 仓库名称
  return_date: string; // 退货日期
  handler?: string; // 经手人ID，可选
  return_reason?: string; // 退货原因，可选
  return_status?: string; // 退货状态，可选
  total_amount: number; // 总金额
  actual_amount: number; // 实际金额
  refunded_amount?: number; // 已退款金额，可选
  discount?: number; // 折扣金额，可选
  remark?: string; // 备注，可选
  payment_method?: string; // 支付方式，可选
  pay_amount?: number; // 支付金额，可选
  purchase_in?: string; // 采购入库单ID，可选
  purchase_in_code?: string; // 采购入库单号，可选
  items: ReturnGoodsItem[]; // 商品列表
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 构建退货订单请求体
 * @param {ReturnGoodsData} data - 原始退货订单数据
 * @returns {object}
 */
function buildReturnGoodsPayload(data: ReturnGoodsData): object {
  return {
    id: data.id,
    is_commit: data.is_commit || '',
    supplier: data.supplier,
    supplier_name: data.supplier_name,
    warehouse: data.warehouse,
    warehouse_name: data.warehouse_name,
    return_date: data.return_date,
    handler: data.handler,
    return_reason: data.return_reason,
    return_status: data.return_status,
    total_amount: Number(data.total_amount),
    actual_amount: Number(data.actual_amount),
    refunded_amount: data.refunded_amount ? Number(data.refunded_amount) : undefined,
    discount: data.discount ? Number(data.discount) : undefined,
    remark: data.remark,
    payment_method: data.payment_method,
    pay_amount: data.pay_amount ? Number(data.pay_amount) : undefined,
    purchase_in: data.purchase_in,
    purchase_in_code: data.purchase_in_code,
    items: (data.items || []).map(item => ({
      id: item.id,
      stock: item.stock || item.stock_id || "",
      item_id: item.item || item.item_id,
      item_name: item.item_name,
      purchase_in_item: item.purchase_in_item,
      purchase_in_code: item.purchase_in_code,
      unit: item.unit,
      unit_name: item.unit_name,
      quantity: item.quantity ? Number(item.quantity) : undefined,
      return_price: item.return_price ? Number(item.return_price) : undefined,
      is_defective: item.is_defective,
      defect_description: item.defect_description,
      remaining_quantity: item.remaining_quantity ? Number(item.remaining_quantity) : undefined,
      remark: item.remark,
      track_id: item.track_id,
      batch_number: item.batch_number,
    })),
  };
}

/**
 * 获取退货列表
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getReturnGoodsList = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/stockreturns/`, data);
};

/**
 * 搜索退货列表
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchReturnGoodsList = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/stockreturns/search/`, data);
};

/**
 * 获取退货详细信息
 * @param {object} data - 包含退货ID的数据
 * @returns {Promise<any>}
 */
export const getReturnGoodsDetail = (data: { id: string }): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/stockreturns/${data.id}/`);
};

/**
 * 新建退货订单
 * @param {ReturnGoodsData} data - 退货订单数据
 * @returns {Promise<any>}
 */
export const addReturnGoods = (data: ReturnGoodsData): Promise<any> => {
  console.log(data);
  return request.post(`/ztx/${getLedgerName()}/stockreturns/`, buildReturnGoodsPayload(data));
};

/**
 * 编辑退货订单
 * @param {ReturnGoodsData} data - 退货订单数据
 * @returns {Promise<any>}
 */
export const editReturnGoods = (data: ReturnGoodsData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/stockreturns/${data.id}/`, buildReturnGoodsPayload(data));
};

/**
 * 删除退货订单
 * @param {object} data - 包含退货订单ID的数据
 * @returns {Promise<any>}
 */
export const deleteReturnGoods = (data: { id: string }): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/stockreturns/${data.id}/cancel/`);
};
