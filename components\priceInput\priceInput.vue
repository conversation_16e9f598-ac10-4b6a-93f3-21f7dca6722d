<template>
  <view>
    <!-- 价格录入弹出框 -->
    <u-popup :show="priceShow" mode="bottom" @close="closePrice">
      <view>
        <view class="categoryConfirmationBtn">
          <view @click="closePrice">取消</view>
          <view class="popup_title"> 价格录入 </view>
          <view class="blueFont" @click="confirmPrice">确定</view>
        </view>
        <view class="divider"></view>
        <view class="priceAttributes">
          <u--form labelPosition="left" labelAlign="left" :model="priceInfo" :rules="rules" ref="priceInfoForm">
            <u-form-item label="单位" prop="unit_type_name" borderBottom ref="item1" required :label-width="'100px'">
              <searchUnit @selectedUnit="selectUnit" @closeUnitArea="closeSelectUnitArea" :editPriceName="editPriceName" :isResetName="isResetName" :isRefresh="searchUnitRefresh"/>
            </u-form-item>
            <u-form-item label="零售价" prop="retail_price" borderBottom ref="item1" :label-width="'100px'">
              <u--input v-model="priceInfo.retail_price" disabledColor="#ffffff" placeholder="请输入零售价" border="none"
                inputAlign="right" type="digit"></u--input>
            </u-form-item>
            <u-form-item label="批发价" prop="wholesale_price" borderBottom ref="item1" :label-width="'100px'">
              <u--input v-model="priceInfo.wholesale_price" disabledColor="#ffffff" placeholder="请输入批发价" type="digit"
                border="none" inputAlign="right"></u--input>
            </u-form-item>
            <u-form-item label="最低售价" prop="min_price" borderBottom ref="item1" :label-width="'100px'">
              <u--input v-model="priceInfo.min_price" disabledColor="#ffffff" placeholder="请输入最低售价" type="digit"
                border="none" inputAlign="right"></u--input>
            </u-form-item>

            <u-row customStyle="margin-bottom: 10px" v-if="hostPriceUnit !== ''">
              <u-col span="5">
                <u-form-item :label="priceInfo.unit_type_name || '单位'" prop="numberOfNewUnits" borderBottom ref="item1"
                  :label-width="'60px'">
                  <u--input v-model="priceInfo.numberOfNewUnits" :disabled="isNewUnit" type="number"
                    disabledColor="#c3c5c7" placeholder="输入比例" border="none" inputAlign="right"
                    @change="changeNewUnits"></u--input>
                </u-form-item>
              </u-col>
              <u-col span="1">= </u-col>
              <u-col span="5">
                <u-form-item :label="hostPriceUnit" prop="numberOfOldUnits" borderBottom ref="item1" :label-width="'50px'">
                  <u--input v-model="priceInfo.numberOfOldUnits" :disabled="isOldUnit" type="number" disabledColor="#c3c5c7"
                    placeholder="输入比例" border="none" inputAlign="right" @change="changeOldUnits"></u--input>
                </u-form-item>
              </u-col>
            </u-row>
          </u--form>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue';
import searchUnit from "./components/searchUnit.vue";
import { formatNumber } from "@/utils/digitalConversion";

interface ProductUnit {
  unit_type_name: string;
  unit_type_id: string;
  conversion_rate: number;
  conversion_rate_host?: number;
  retail_price: number;
  wholesale_price: number;
  min_price: number;
  numberOfNewUnits?: number;
  numberOfOldUnits?: number;
  oldUnit?: string;
}

interface PriceInfoForm {
  unit_type_name: string;
  unit_type_id: string;
  conversion_rate: number;
  conversion_rate_host: number;
  oldUnit: string;
  retail_price: number;
  wholesale_price: number;
  min_price: number;
  numberOfNewUnits: number;
  numberOfOldUnits: number;
}

const props = defineProps({
  priceShow: {
    type: Boolean,
    default: true,
  },
  hostPriceUnit: {
    type: String,
    default: '',
  },
  editPriceInfo: {
    type: Object as () => ProductUnit,
    default: () => ({}),
  },
});

const emit = defineEmits(['close', 'confirm-price', 'update:priceShow']);

const searchUnitRefresh = ref(false); //搜索单位刷新
const pricesShow = ref(false);
const isNewUnit = ref(false); //新单位比例输入
const isOldUnit = ref(false); //旧单位比例输入
const isResetName = ref(false); //是否需要重置名称
const editPriceName = ref(''); //编辑价格名称
const priceInfo = reactive<PriceInfoForm>({
  unit_type_name: "", // 单位类型名称
  unit_type_id: '', // 单位类型ID
  conversion_rate: 1, // 转换率
  conversion_rate_host: 1, // 与主单位之间的转换率
  oldUnit: props.hostPriceUnit || "", // 原始单位或主价格单位
  retail_price: 0, // 零售价
  wholesale_price: 0, // 批发价
  min_price: 0, // 最低价格
  numberOfNewUnits: 0, // 新单位数量
  numberOfOldUnits: 0, // 原单位数量
});

const rules = reactive({
  unit_type_name: [
    {
      type: "string",
      required: true,
      message: "请选择单位",
      trigger: ["blur", "change"],
    },
  ],
});

const priceInfoForm = ref<any>(null);

onMounted(() => {
  if (typeof document !== "undefined") {
    document.removeEventListener("click", closeUnitArea);
    document.addEventListener("click", closeUnitArea);
  }
  if (priceInfo.unit_type_name === "") {
    isNewUnit.value = true;
    isOldUnit.value = true;
  }
  if (priceInfoForm.value) {
    priceInfoForm.value.setRules(rules);
  }
});

watch(() => props.priceShow, (newVal) => {
  if (newVal) {
    if (Object.keys(props.editPriceInfo).length === 0) {
      resetData();
      return;
    }
    nextTick(() => {
      editPriceName.value = props.editPriceInfo.unit_type_name || '';
      Object.assign(priceInfo, JSON.parse(JSON.stringify(props.editPriceInfo)));

      if (props.editPriceInfo.conversion_rate && props.editPriceInfo.conversion_rate !== 1) {
        priceInfo.numberOfOldUnits = Number(props.editPriceInfo.conversion_rate);
        priceInfo.numberOfNewUnits = 1;
        isOldUnit.value = false;
        isNewUnit.value = false;
      }

      isResetName.value = false;
      searchUnitRefresh.value = false;
    });
  } else {
    searchUnitRefresh.value = true;
  }
});

watch(() => props.editPriceInfo, (newVal, oldVal) => {
  console.log('editPriceInfo changed:', { newVal, oldVal, priceShow: props.priceShow });
  if (newVal && !props.priceShow) {
    editPriceName.value = newVal.unit_type_name || '';
  }
}, { deep: true, immediate: false });

watch(() => props.hostPriceUnit, (newVal) => {
  priceInfo.oldUnit = newVal;
});

watch(() => priceInfo.unit_type_name, () => {
  nextTick(() => {
    // $forceUpdate is not needed in Vue 3
  });
});

const resetData = () => {
  Object.assign(priceInfo, {
    unit_type_name: "", // 单位类型名称
    unit_type_id: '', // 单位类型ID
    conversion_rate: 1, // 转换率
    conversion_rate_host: 1, // 与主单位转换率
    oldUnit: props.hostPriceUnit || "", // 原始单位或主价格单位
    retail_price: 0, // 零售价
    wholesale_price: 0, // 批发价
    min_price: 0, // 最低价格
    numberOfNewUnits: 0, // 新单位数量
    numberOfOldUnits: 0, // 原单位数量
  });
  isResetName.value = true;
};

const confirmPrice = () => {
  priceInfoForm.value.validate().then(() => {
    if (priceInfo.numberOfNewUnits !== 0 && priceInfo.numberOfOldUnits !== 0) {
      if (priceInfo.numberOfNewUnits > priceInfo.numberOfOldUnits) {
        priceInfo.conversion_rate = priceInfo.numberOfNewUnits / priceInfo.numberOfOldUnits;
      } else {
        priceInfo.conversion_rate = priceInfo.numberOfOldUnits / priceInfo.numberOfNewUnits;
      }
      priceInfo.conversion_rate_host = priceInfo.conversion_rate;
    }

    priceInfo.retail_price = Number(priceInfo.retail_price) || 0;
    priceInfo.wholesale_price = Number(priceInfo.wholesale_price) || 0;
    priceInfo.min_price = Number(priceInfo.min_price) || 0;

    emit("confirm-price", priceInfo);
    closePrice();
    resetData();
  }).catch((errors: any) => {
    uni.showToast({
      title: '校验失败,请检查数据是否正确',
      icon: 'none'
    });
  });
};

const closeUnitArea = (e: MouseEvent) => {
  // 确保事件目标不是 searchUnit 组件内部的元素
  // 这里需要一个更可靠的方式来判断，例如给 searchUnit 组件的根元素添加 ref
  // 暂时跳过此处的精确判断，因为 u-form-item 已经处理了点击事件
};

const closePrice = () => {
  pricesShow.value = false;
  searchUnitRefresh.value = true; // 触发 searchUnit 组件的刷新
  isResetName.value = true; // 确保重置名称
  resetData();
  emit("update:priceShow", pricesShow.value);
};

const closeSelectUnitArea = () => {
  console.log(111);
  searchUnitRefresh.value = false;
};

const selectUnit = (data: { unit_type_name: string; unit_type_id: string }) => {
  priceInfo.unit_type_name = data.unit_type_name;
  priceInfo.unit_type_id = data.unit_type_id;

  if (props.hostPriceUnit !== '' && Object.keys(props.editPriceInfo).length === 0) {
    priceInfo.numberOfNewUnits = 1;
    priceInfo.numberOfOldUnits = 1;
  }

  isNewUnit.value = false;
  isOldUnit.value = false;

  nextTick(() => {
    // $forceUpdate is not needed in Vue 3
  });
};

const changeNewUnits = (value: number) => {
  if (value > 1) {
    priceInfo.numberOfOldUnits = 1;
    isOldUnit.value = true;
  } else {
    isOldUnit.value = false;
  }
};

const changeOldUnits = (value: number) => {
  if (priceInfo) {
    if (value > 1) {
      priceInfo.numberOfNewUnits = 1;
      isNewUnit.value = true;
    } else {
      isNewUnit.value = false;
    }
  }
};

// onBeforeUnmount(() => {
//   // 确保组件销毁时单位选择器关闭
//   searchUnitRefresh.value = true;
// });
</script>

<style lang="scss" scoped>
.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.priceAttributes {
  width: 95%;
  display: flex;
  flex-direction: column;
  margin: 0 auto;

  .priceAttributes_item {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #cacaca;
    padding: 20rpx 0;
  }
}

.unitArea {
  max-height: 280rpx;
  width: 300rpx;
  background-color: #fff;
  border: 1px solid #cacaca;
  border-radius: 20rpx;
  overflow: auto;
  position: fixed;
  z-index: 999;
  right: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

  .unitArea_search {
    position: sticky;
    top: 0;
    width: 90%;
    border-bottom: 1px solid #cacaca;
    padding: 10rpx;
    background-color: #fff;
    z-index: 999;
  }

  .unitArea_item {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
    border-bottom: 1px solid #cacaca;
    font-size: 25rpx;
    font-weight: 700;
  }

  .noResults {
    width: 100%;
    height: 50rpx;
    line-height: 50rpx;
    display: flex;
    justify-content: center;
  }

  .unitArea_content {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .unitArea_item {
    background-color: #fff;
    border-radius: 4rpx;
    font-size: 28rpx;
    color: #333;

    &:active {
      background-color: #e0e0e0;
    }
  }
}

::v-deep .u-form-item__body__left__content__label {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  margin-left: 20rpx;
  text-align: justify;
  text-align-last: justify;
  letter-spacing: 15rpx;
}

::v-deep .u-input__content__field-wrapper__field {
  line-height: 26px;
  text-align: right;
  color: #303133;
  height: 24px;
  font-size: 13px !important;
  margin-right: 20rpx;
  flex: 1;
}

//搜索框
::v-deep .u-search__content.data-v-1a326067 {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 10px;
  flex: none !important;
  justify-content: space-between;
  border-width: 1px;
  border-color: transparent;
  border-style: solid;
  overflow: hidden;
  width: 235rpx;
  font-size: 11px;
}

::v-deep .u-form-item {
  font-size: 11px;
}

::v-deep .u-search__content__input .u-search__content__input.data-v-1a326067 {
  flex: 1;
  font-size: 11px;
  line-height: 1;
  margin: 0 5px;
  color: #303133;
}

::v-deep .u-col.data-v-26dd4db9 {
  padding: 0;
  box-sizing: border-box;
  display: block;
  margin: 0 auto;
}
</style>