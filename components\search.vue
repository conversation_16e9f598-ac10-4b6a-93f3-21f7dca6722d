<template>
  <view class="searchArea">
    <view class="search">
      <view class="search_input">
        <u-search :placeholder="search_prompt" v-model="search_value" :clearabled="true" shape="square"
          :showAction="false" inputAlign="center" @search="handleSearch" @clickIcon="handleSearch"
          @input="handleInput" @clear="handleClear"></u-search>
      </view>
      <view class="search_icon">
        <i-filter theme="outline" size="24" fill="#2b85e4" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { searchGoods } from "@/api/goods";
import { searchPurchaseorders } from "@/api/purchaseOrder";
import { searchPurchaseGoods } from "@/api/purchaseGoods";
import { searchReturnGoodsList } from "@/api/returnGoods";
import { searchRetailOrders } from "@/api/retailOrders";
import { searchSalesOrder } from "@/api/salesOrder";

interface Props {
  searchType: number;
}

const props = defineProps<Props>();

const search_prompt = ref(""); //搜索提示内容
const search_value = ref(""); //搜索内容
const searchTimer = ref<number | null>(null); // 防抖定时器
const isSearching = ref(false); // 搜索状态

const emit = defineEmits(['search-result']);

// 清空搜索
const handleClear = () => {
  search_value.value = "";
  isSearching.value = false;
  if (searchTimer.value) {
    clearTimeout(searchTimer.value);
  }
  emit("search-result", null);
};

watch(() => props.searchType, (newVal) => {
  // 当 searchType 变化时，清空搜索内容
  search_value.value = "";
  handleClear();
  switch (newVal) {
    case 0:
      search_prompt.value = "商品名称/商品编号/型号";
      break;
    case 1:
      search_prompt.value = "名称/操作员/供应商/单据编号";
      break;
    case 2:
      search_prompt.value = "名称/操作员/供应商";
      break;
    case 3:
      search_prompt.value = "名称/操作员/供应商";
      break;
    case 4:
      search_prompt.value = "订单号/客户名称/经办人";
      break;
    case 5:
      search_prompt.value = "订单号/客户名称/经办人";
      break;
    default:
      search_prompt.value = "请输入您想搜索的内容";
      break;
  }
}, { immediate: true });

const handleSearch = () => {
  // 调用搜索接口
  if (search_value.value == "") {
    emit("search-result", null);
    return;
  }

  // 设置搜索状态
  isSearching.value = true;

  const searchMap: { [key: number]: Function } = {
    0: searchGoods,
    1: searchPurchaseorders,
    2: searchPurchaseGoods,
    3: searchReturnGoodsList,
    4: searchSalesOrder, // 销售订单搜索
    5: searchRetailOrders,
  };
  console.log(props.searchType);

  const searchFn = searchMap[props.searchType];
  if (searchFn) {
    searchFn({ query: search_value.value })
      .then((res: any) => {
        console.log('搜索结果:', res);
        if (res && res.data) {
          // 检查搜索结果是否为空
          if (res.data.results && res.data.results.length === 0) {
            console.log('搜索结果为空');
            // 发送空结果，但保持搜索状态
            emit("search-result", { count: 0, results: [] });
          } else {
            emit("search-result", res.data);
          }
        } else {
          console.log('搜索返回数据格式异常');
          emit("search-result", { count: 0, results: [] });
        }
      })
      .catch((err: any) => {
        console.error("搜索失败:", err);
        uni.showToast({ title: "搜索失败", icon: "none" });
        // 搜索失败时也发送空结果
        emit("search-result", { count: 0, results: [] });
      })
      .finally(() => {
        // 搜索完成，重置搜索状态
        isSearching.value = false;
      });
  } else {
    console.warn("未知的搜索类型:", props.searchType);
    emit("search-result", { count: 0, results: [] });
    isSearching.value = false;
  }
};

// 实时输入处理
const handleInput = () => {
  // 清除之前的定时器
  if (searchTimer.value) {
    clearTimeout(searchTimer.value);
  }

  // 如果输入为空，立即清空搜索结果
  if (!search_value.value || search_value.value.trim() === "") {
    handleClear();
    return;
  }

  // 设置搜索状态
  isSearching.value = true;

  // 3秒防抖，只发送用户最后一次输入的内容
  searchTimer.value = setTimeout(() => {
    isSearching.value = false;
    handleSearch();
  }, 3000);
};

onUnmounted(() => {
  // 清理定时器
  if (searchTimer.value) {
    clearTimeout(searchTimer.value);
  }
});

</script>

<style lang="scss" scoped>
//搜索
.search {
  width: 100%;
  height: 80rpx;
  border-bottom: 1rpx solid #ccc;
  padding: 10rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;

  .search_input {
    width: 80%;
  }

  .search_icon {
    margin-left: 30rpx;
  }
}

::v-deep .u-input {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  background-color: #ebeaea !important;
  color: #fff;
  border-radius: 20rpx;
}
</style>