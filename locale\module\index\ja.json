{"index.title": "Hello i18n", "index.home": "ホーム", "index.component": "コンポーネント", "index.api": "API", "index.schema": "<PERSON><PERSON><PERSON>", "index.demo": "uni-app globalization", "index.demo-description": "ユニフレームワーク、manifest.json、pages.json、タブバー、ページ、コンポーネント、APIを含める、Schema", "index.detail": "詳細", "index.language": "言語", "index.language-info": "設定", "index.system-language": "システム言語", "index.application-language": "アプリケーション言語", "index.language-change-confirm": "この設定を適用すると、アプリが再起動します"}