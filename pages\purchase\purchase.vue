<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="1" @search-result="handleSearchResult" />

    <!-- 采购单列表 -->
    <scroll-view scroll-y class="purchase-list" :style="{ height: scrollViewHeight }" @scrolltolower="onScrollToLower">
      <view class="purchase-item attribute_font" v-for="(item, index) in purchaseList" :key="index"
        @click="navigatorEdit(item)">
        <view class="status-order-tag">
          {{
            item.order_status === "pending"
              ? "预定单"
              : item.order_status === "partial"
                ? "部分交付"
                : item.order_status === "completed"
                  ? "全部交付"
                  : item.order_status
          }}
        </view>
        <view class="status-payment-tag">
          {{
            item.payment_status === "unpaid"
              ? "未付款"
              : item.payment_status === "prepaid"
                ? "已预付款"
                : item.payment_status
          }}
        </view>
        <view class="item_container">
          <view class="item-content">
            <view class="info-row">
              <text class="label">供应商</text>
              <text class="value">：{{ item.supplier_name }}</text>
            </view>
            <view class="info-row">
              <text class="label">商品</text>
              <text class="value">：{{
                item.short_desc
              }}</text>
              <text class="sub-value">{{ item.category }}</text>
              <text class="sub-value">{{ item.type }}</text>
            </view>
            <view class="info-row">
              <view class="info-row-item">
                <text class="label">操作员</text>
                <text class="value">：{{ item.handler }}</text>
              </view>
              <view>
                <text class="sub-label">数量</text>
                <text class="value">：{{ item.item_count }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-row-item">
                <text class="label">金额合计</text>
                <text class="value">：{{ $toFixed(item.total_amount) }}</text>
              </view>
              <view>
                <text class="sub-label">预付款</text>
                <text class="value">：{{ $toFixed(item.deposit) }}</text>
              </view>
            </view>
          </view>

          <!-- 选择订单按钮 -->
          <view class="select-order-btn" @click="selectOrder(item)" v-if="isSelectOrder">
            <i-add-one theme="outline" size="20" fill="#378ce5" />
          </view>
        </view>
        <view class="info-bottom">
          <view class="info-bottom-container">
            <view class="info-row" style="margin-bottom: 10rpx">
              <text class="label">申请日期</text>
              <text class="value">：{{ item.order_date }}</text>
            </view>
            <view class="info-row" style="margin-bottom: 0" v-if="isSelectOrder">
              <text class="label">单据编号</text>
              <text class="value">：{{ item.order_id }}</text>
            </view>
          </view>
          <button class="share-btn" open-type="share" :data-item="item" v-if="!isSelectOrder">
            分享
          </button>
        </view>
      </view>
      <!-- 加载更多 -->
      <u-loadmore :status="loadStatus" :loading-text="'加载中...'" :loadmore-text="'上拉加载更多'" :nomore-text="'没有更多数据了'" />
    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="true" :jumpToId="2" v-if="!isSelectOrder" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { onLoad, onShow, onShareAppMessage } from '@dcloudio/uni-app';
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import searchSelector from "@/components/searchSelector.vue";
import { getPurchaseorders, getPurchaseordersDetail } from "@/api/purchaseOrder";
import eventBus from "@/utils/eventBus";

const isSelectOrder = ref(false); //是否作为选择订单
const purchaseList = ref<any[]>([]);
const purchaseListParams = reactive({
  page: 1,
  page_size: 5,
});
const hasMore = ref(true);
const scrollViewHeight = ref("500px"); // 默认值，后面会动态设置
const loadStatus = ref('loadmore'); // 新增：加载更多状态

// 选择订单
const selectOrder = (data: any) => {
  getPurchaseordersDetail(data.id).then(res => {
    eventBus.$emit('selectOrder', res.data);
    uni.navigateTo({
      url:
        "/components/productSelection?data=" +
        JSON.stringify(res.data.items) + "&type=0",
    });
  }).catch(err => {
    console.log(err);
  })
};
//搜索成功内容
const handleSearchResult = (newVal: any) => {
  console.log('搜索结果:', newVal);

  if (newVal === null || newVal === undefined) {
    // 搜索为空，重新获取所有数据
    resetPageCount();
  } else if (Array.isArray(newVal)) {
    // 搜索组件返回的是数组格式（res.data）
    purchaseList.value = newVal;

    // 搜索模式下禁用加载更多
    hasMore.value = false;
    loadStatus.value = 'nomore';

    // 如果搜索结果为空，显示提示
    if (purchaseList.value.length === 0) {
      console.log('搜索无结果');
      uni.showToast({
        title: '未找到匹配的采购单',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: `搜索到${purchaseList.value.length}条结果`,
        icon: 'none'
      });
    }
  } else if (newVal && typeof newVal === 'object') {
    // 处理其他可能的对象格式
    if (newVal.results && Array.isArray(newVal.results)) {
      // 标准的分页搜索结果格式 {results: [], count: 0}
      purchaseList.value = newVal.results;
    } else {
      // 其他格式，尝试重置
      console.warn('未知的搜索结果格式:', newVal);
      purchaseList.value = [];
    }

    // 搜索模式下禁用加载更多
    hasMore.value = false;
    loadStatus.value = 'nomore';
  } else {
    // 其他情况，重新获取数据
    resetPageCount();
  }
};
//跳转编辑页面
const navigatorEdit = (item: any) => {
  if (isSelectOrder.value) {
    selectOrder(item);
    return;
  }

  getPurchaseordersDetail(item.id).then(res => {
    if (res.code == 0) {
      uni.navigateTo({
        url:
          "/pages/purchase/editPurchase/editPurchase?data=" +
          JSON.stringify(res.data),
      });
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
      })
    }
  }).catch(err => {
    console.error(err);
    uni.showToast({
      title: "请求失败", // Assuming REQUEST_ERROR is a global constant or needs to be defined
      icon: 'none',
    })
      }).finally(() => {
        // 隐藏加载提示
        uni.hideLoading();
  })
};
//分享内容
const handleShare = (item: any) => {
  // 设置分享内容
  const shareData = {
    title: "采购单分享",
    path: "/pages/purchase/purchase",
    imageUrl: "", // 可以设置自定义分享图片
    success(res: any) {
      uni.showToast({
        title: "分享成功",
        icon: "success",
      });
    },
    fail(err: any) {
      uni.showToast({
        title: "分享失败",
        icon: "none",
      });
    },
  };

  // 返回分享数据
  return shareData;
};
//计算可滚动区域高度
const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44; // 导航栏高度
    const statusBarHeight = info.statusBarHeight; // 状态栏高度
    const searchHeight = (screenWidth * 80) / 750; // 搜索框高度 80rpx
    const inputHeight = (screenWidth * 90) / 750; // 底部输入区域高度 90rpx
    // 没有 selectBtnHeight
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};
//滚动到底部
const onScrollToLower = () => {
  if (loadStatus.value === 'loadmore') {
    loadPurchaseOrders();
  }
};
//重置页数
const resetPageCount = () => {
  purchaseListParams.page = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  loadPurchaseOrders();
};
//获取采购单列表
const loadPurchaseOrders = () => {
  if (!hasMore.value || loadStatus.value === 'loading') return;
  loadStatus.value = 'loading';
  const params = {
    page: purchaseListParams.page,
    page_size: purchaseListParams.page_size,
  };
  getPurchaseorders(params).then((res) => {
    if (res.code == 0) {
      if (purchaseListParams.page === 1) {
        purchaseList.value = res.data.results;
      } else {
        purchaseList.value = purchaseList.value.concat(res.data.results);
      }
      // 判断是否还有更多
      if (purchaseList.value.length < (res.data.count || 0)) {
        hasMore.value = true;
        loadStatus.value = 'loadmore';
        purchaseListParams.page++;
      } else {
        hasMore.value = false;
        loadStatus.value = 'nomore';
      }
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
      loadStatus.value = 'loadmore';
    }
  }).catch((err) => {
    uni.showToast({
      title: "请求失败",
      icon: "none",
    });
    loadStatus.value = 'fail';
  });
};

// 定义页面的分享行为
onShareAppMessage((res) => {
  if (res.from === "button") {
    // 来自页面内分享按钮
    const item = res.target.dataset.item;
    return {
      title: `采购单分享`,
      path: "/pages/purchase/purchase",
      imageUrl: "", // 可以设置自定义分享图片
    };
  }
  // 来自右上角分享
  return {
    title: "采购单分享",
    path: "/pages/purchase/purchase",
    imageUrl: "", // 可以设置自定义分享图片
  };
});

onLoad((options: any) => {
  isSelectOrder.value = options.isSelectOrder;
  initScrollViewHeight();
  loadPurchaseOrders();
});

onShow(() => {
  resetPageCount();
});

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.purchase-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.purchase-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.item_container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}

.item-content {
  width: 100%;
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 400rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 80rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.sub-value {
  color: #333;
  margin-right: 20rpx;
}

.info-bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.info-bottom-container {
  display: flex;
  flex-direction: column;
  align-content: center;
}

.share-btn {
  color: #4080e8;
  font-size: 28rpx;
  text-align: right;
  padding: 10rpx 0;
  background: none;
  border: none;
  line-height: 1;
  margin: 0;
}

.share-btn::after {
  border: none;
}

.status-order-tag {
  position: absolute;
  top: 12rpx;
  right: 140rpx;
  /* padding: 0 -1rpx; */
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #e99129;
  color: #e99129;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}

.status-payment-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}
</style>