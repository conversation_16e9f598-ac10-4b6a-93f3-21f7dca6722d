<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="3" @search-result="handleSearchResult" />

    <!-- 退货单列表 -->
    <scroll-view scroll-y class="return-list" :style="{ height: scrollViewHeight }" @scrolltolower="onScrollToLower">
      <view class="return-item attribute_font" v-for="(item, index) in returnGoodsList" :key="index"
        @click="navigatorEdit(item)">
        <!-- 状态标签 -->
        <view class="status-order-tag">
          {{ getReturnStatusText(item.return_status) }}
        </view>
        <view class="item-content">
          <view class="info-row">
            <text class="label">供应商</text>
            <text class="value">：{{ item.supplier_name }}</text>
          </view>
          <view class="info-row">
            <text class="label">商品</text>
            <text class="value">：{{ item.short_desc }}</text>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">操作员</text>
              <text class="value">：{{ item.handler_name }}</text>
            </view>
            <view>
              <text class="sub-label">数量</text>
              <text class="value">：{{ item.items_count }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">退款金额</text>
              <text class="value">：{{ $toFixed(item.actual_amount) }}</text>
            </view>
            <view class="info-row-item">
              <text class="sub-label">已退金额</text>
              <text class="value">：{{ $toFixed(item.refunded_amount) }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">欠款</text>
              <text class="value">：{{ $toFixed(item.actual_amount - item.refunded_amount) }}</text>
            </view>
          </view>
        </view>
        <view class="info-bottom">
          <view class="info-row" style="margin-bottom: 0">
            <text class="label">申请日期</text>
            <text class="value">：{{ item.return_date }}</text>
          </view>
          <button class="share-btn" open-type="share" :data-item="item">
            分享
          </button>
        </view>
      </view>
      <!-- 加载更多 -->
      <u-loadmore :status="loadStatus" :loading-text="'加载中...'" :loadmore-text="'上拉加载更多'" :nomore-text="'没有更多数据了'" />
    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="true" :jumpToId="4" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { onShow, onLoad } from '@dcloudio/uni-app';
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import { getReturnGoodsList, getReturnGoodsDetail } from "@/api/returnGoods";

const returnGoodsList = ref<any[]>([]);
const returnGoodsListParams = reactive({
  page: 1,
  page_size: 10,
});
const hasMore = ref(true);
const loadStatus = ref("loadmore"); // 'loadmore' | 'loading' | 'nomore'
const scrollViewHeight = ref("500px");

const loadReturnGoodsList = (isLoadMore = false) => {
  if (loadStatus.value === "loading" || (!hasMore.value && isLoadMore))
    return;
  loadStatus.value = "loading";

  const params = {
    page: returnGoodsListParams.page,
    page_size: returnGoodsListParams.page_size,
  };

  getReturnGoodsList(params)
    .then((res: any) => {
      if (res.code == 0) {
        const results = res.data.results || [];
        if (isLoadMore) {
          returnGoodsList.value = returnGoodsList.value.concat(results);
        } else {
          returnGoodsList.value = results;
        }
        // 判断是否还有更多
        if (returnGoodsList.value.length < (res.data.count || 0)) {
          hasMore.value = true;
          loadStatus.value = "loadmore";
          returnGoodsListParams.page++;
        } else {
          hasMore.value = false;
          loadStatus.value = "nomore";
        }
      } else {
        uni.showToast({ title: res.msg, icon: "none" });
        loadStatus.value = "loadmore";
      }
    })
    .catch(() => {
      uni.showToast({ title: "请求失败", icon: "none" });
      loadStatus.value = "loadmore";
    });
};
const getReturnStatusText = (status: string) => {
  const map: { [key: string]: string } = {
    draft: "暂存",
    completed: "已退货",
    cancelled: "已取消",
  };
  return map[status] || status;
};
const handleSearchResult = (newVal: any) => {
  console.log('搜索结果:', newVal);

  if (newVal === null || newVal === undefined) {
    // 搜索为空，重新获取所有数据
    resetData();
  } else if (Array.isArray(newVal)) {
    // 搜索组件返回的是数组格式（res.data）
    returnGoodsList.value = newVal;

    // 搜索模式下禁用加载更多
    hasMore.value = false;
    loadStatus.value = 'nomore';

    // 如果搜索结果为空，显示提示
    if (returnGoodsList.value.length === 0) {
      console.log('搜索无结果');
      uni.showToast({
        title: '未找到匹配的退货单',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: `搜索到${returnGoodsList.value.length}条结果`,
        icon: 'none'
      });
    }
  } else if (newVal && typeof newVal === 'object') {
    // 处理其他可能的对象格式
    if (newVal.results && Array.isArray(newVal.results)) {
      // 标准的分页搜索结果格式 {results: [], count: 0}
      returnGoodsList.value = newVal.results;
    } else {
      // 其他格式，尝试重置
      console.warn('未知的搜索结果格式:', newVal);
      returnGoodsList.value = [];
    }

    // 搜索模式下禁用加载更多
    hasMore.value = false;
    loadStatus.value = 'nomore';
  } else {
    // 其他情况，重新获取数据
    resetData();
  }
};

// 重置数据并重新加载
const resetData = () => {
  returnGoodsListParams.page = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  loadReturnGoodsList();
};
const navigatorEdit = (item: any) => {
  getReturnGoodsDetail(item).then((res: any) => {
    console.log(res);
    
    if (res.code == 0) {
      uni.navigateTo({
        url:
          "/pages/returnGoodsOrder/editReturnGoodsOrder/editReturnGoodsOrder?data=" +
          JSON.stringify(res.data) + "&isEdit=true",
      });
    } else {
      uni.showToast({ title: res.msg || '获取详情失败', icon: 'none' });
    }
  }).catch((err: any) => {
    console.error('获取退货单详情失败:', err);
    uni.showToast({ title: '请求失败', icon: 'none' });
  })

};
const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44;
    const searchHeight = (screenWidth * 80) / 750;
    const inputHeight = (screenWidth * 90) / 750;
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};
const onScrollToLower = () => {
  if (loadStatus.value === "loadmore") {
    loadReturnGoodsList(true);
  }
};

onShow(() => {
  resetData();
});

onLoad(() => {
  initScrollViewHeight();
  returnGoodsListParams.page = 1;
  hasMore.value = true;
  loadStatus.value = "loadmore";
  loadReturnGoodsList();
});

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.return-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.return-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.item-content {
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 50%;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 100rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.sub-value {
  color: #333;
  margin-right: 20rpx;
}

.info-bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.share-btn {
  color: #4080e8;
  font-size: 28rpx;
  text-align: right;
  padding: 10rpx 0;
  background: none;
  border: none;
  line-height: 1;
  margin: 0;
}

.share-btn::after {
  border: none;
}

.status-order-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}
</style>
