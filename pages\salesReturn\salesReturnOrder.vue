<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="5" />

    <!-- 退货订单列表 -->
    <scroll-view
      scroll-y
      class="retail-list"
      :style="{ height: scrollViewHeight }"
      @scrolltolower="loadMore"
    >
      <view
        class="retail-item attribute_font"
        v-for="(item, index) in returnRetailOrdersList"
        :key="index"
        @click="jumpToView(item)"
      >
        <view class="red-tag">{{ item.status || '已退货' }}</view>
        <view class="item-content">
          <view class="info-row">
            <text class="label">供应商</text>
            <text class="value">：{{ item.supplier_name || '未知供应商' }}</text>
          </view>
          <view class="info-row">
            <text class="label">商品</text>
            <text class="value">：{{ item.short_desc || '暂无' }}</text>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">操作人</text>
              <text class="value">：{{ item.operator_name || '未知' }}</text>
            </view>
            <view>
              <text class="sub-label">数量</text> 
              <text class="value">：{{ item.quantity || '0' }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">退款金额</text>
              <text class="value">：{{ item.refund_amount || '0' }}</text>
            </view>
            <view>
              <text class="sub-label">已退金额</text>
              <text class="value">：{{ item.refund_amount || '0' }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">欠款金额</text>
              <text class="value">：{{ item.return_amount || '0' }}</text>
            </view>
          </view>
        </view>
        <view class="info-bottom">
          <view class="info-row" style="margin-bottom: 0">
            <text class="label">单据日期</text>
            <text class="value">：{{ item.create_time || '未知' }}</text>
          </view>
          <button class="share-btn" @click.stop="handleShare(item)">
            分享
          </button>
        </view>
      </view>
      <!-- 加载更多/没有更多 -->
      <u-loadmore
        :status="loadStatus"
        loading-text="正在加载..."
        nomore-text="没有更多数据了"
        :icon="true"
      />
    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="true" :jumpToId="7" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
// 导入销售退货API
import {
  getSalesReturnList,
  getSalesReturnDetail,
  searchSalesReturn
} from '@/api/salesReturn';

const returnRetailOrdersList = ref<any[]>([]);
const pageNum = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loading = ref(false);
const loadStatus = ref('loadmore'); // loadmore/loading/nomore
const scrollViewHeight = ref("500px");
const searchKeyword = ref(''); // 搜索关键词
const isSearchMode = ref(false); // 是否为搜索模式

const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44;
    const searchHeight = (screenWidth * 80) / 750;
    const inputHeight = (screenWidth * 90) / 750;
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};
const getList = async () => {
  if (loading.value || !hasMore.value) return;

  loading.value = true;
  loadStatus.value = 'loading';

  try {
    const params = {
      page: pageNum.value,
      page_size: pageSize.value
    };

    const res: any = await getSalesReturnList(params);

    if (res && res.code === 0 && res.data) {
      const list = res.data.results || [];
      const totalCount = res.data.count || 0;

      // 如果是第一页，清空列表
      if (pageNum.value === 1) {
        returnRetailOrdersList.value = [];
      }

      // 判断是否还有更多数据
      // 使用next字段或者比较当前数据总数与总记录数
      if (!res.data.next || returnRetailOrdersList.value.length + list.length >= totalCount) {
        hasMore.value = false;
        loadStatus.value = 'nomore';
      } else {
        pageNum.value += 1;
        loadStatus.value = 'loadmore';
      }

      returnRetailOrdersList.value = returnRetailOrdersList.value.concat(list);
    } else {
      hasMore.value = false;
      loadStatus.value = 'nomore';
      if (res && res.msg) {
        uni.showToast({
          title: res.msg,
          icon: 'none'
        });
      }
    }

    loading.value = false;
  } catch (error) {
    console.error('获取销售退货列表失败：', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
    loadStatus.value = 'loadmore';
    loading.value = false;
  }
};
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    if (isSearchMode.value) {
      performSearch();
    } else {
      getList();
    }
  }
};

const jumpToView = async (item: any) => {
  if (!item || !item.id) {
    uni.showToast({
      title: '数据异常，无法查看详情',
      icon: 'none'
    });
    return;
  }

  try {
    uni.showLoading({ title: '加载中...' });

    const res: any = await getSalesReturnDetail(item.id);

    uni.hideLoading();

    if (res && res.code === 0 && res.data) {
      // 跳转到销售退货详情页面
      uni.navigateTo({
        url: '/pages/salesReturn/salesReturnDetail?data=' + JSON.stringify(res.data)
      });
    } else {
      const errorMsg = res && res.msg ? res.msg : '获取详情失败';
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('获取销售退货详情失败：', error);
    uni.showToast({
      title: '获取详情失败，请重试',
      icon: 'none'
    });
  }
};

// 搜索销售退货
const searchReturnOrders = async (keyword: string) => {
  searchKeyword.value = keyword;
  isSearchMode.value = !!keyword;
  pageNum.value = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';

  if (keyword) {
    await performSearch();
  }
  else {
    await getList();
  }
};

// 执行搜索
const performSearch = async () => {
  if (loading.value || !hasMore.value) return;

  loading.value = true;
  loadStatus.value = 'loading';

  try {
    const params = {
      page: pageNum.value,
      page_size: pageSize.value,
      search: searchKeyword.value
    };

    const res: any = await searchSalesReturn(params);

    if (res && res.code === 0 && res.data) {
      const list = res.data.results || [];
      const totalCount = res.data.count || 0;

      // 如果是第一页，清空列表
      if (pageNum.value === 1) {
        returnRetailOrdersList.value = [];
      }

      // 判断是否还有更多数据
      // 使用next字段或者比较当前数据总数与总记录数
      if (!res.data.next || returnRetailOrdersList.value.length + list.length >= totalCount) {
        hasMore.value = false;
        loadStatus.value = 'nomore';
      } else {
        pageNum.value += 1;
        loadStatus.value = 'loadmore';
      }

      returnRetailOrdersList.value = returnRetailOrdersList.value.concat(list);
    } else {
      hasMore.value = false;
      loadStatus.value = 'nomore';
      if (res && res.msg) {
        uni.showToast({
          title: res.msg,
          icon: 'none'
        });
      }
    }

    loading.value = false;
  } catch (error) {
    console.error('搜索销售退货失败：', error);
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none'
    });
    loadStatus.value = 'loadmore';
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = '';
  isSearchMode.value = false;
  pageNum.value = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  getList();
};

// 格式化显示文本的辅助方法
const formatDisplayText = (value: string, defaultText = '暂无') => {
  return value && value.trim() ? value : defaultText;
};

// 处理分享功能
const handleShare = (item: any) => {
  // 构建分享内容
  const shareContent = `销售退货单详情：\n客户：${formatDisplayText(item.customer_name, '未知客户')}\n商品：${formatDisplayText(item.short_desc, '暂无描述')}\n销售单号：${formatDisplayText(item.sales_out_id, '无')}\n仓库：${formatDisplayText(item.warehouse_name, '未知仓库')}\n制单人：${formatDisplayText(item.controller_name, '未知')}`;

  // 调用分享功能
  uni.share({
    provider: "weixin",
    scene: "WXSceneSession",
    type: 0,
    summary: shareContent,
    success: function (res: any) {
      console.log("分享成功：" + JSON.stringify(res));
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    },
    fail: function (err: any) {
      console.log("分享失败：" + JSON.stringify(err));
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
};

onMounted(() => {
  initScrollViewHeight();
  getList();
});

onShow(() => {
  pageNum.value = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  getList();
});

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.retail-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.retail-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.item-content {
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 400rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 100rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.sub-value {
  color: #333;
  margin-right: 20rpx;
}

.info-bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.share-btn {
  color: #4080e8;
  font-size: 28rpx;
  text-align: right;
  padding: 10rpx 0;
  background: none;
  border: none;
  line-height: 1;
  margin: 0;
}

.share-btn::after {
  border: none;
}

.status-order-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}
</style>
