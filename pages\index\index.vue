<template>
  <view class="container">
    <view class="">
      <!-- 状态栏占位 -->
      <view
        :style="{
          height: height + 'px',
          width: '100%',
          backgroundColor: '#2b85e4',
        }"
      ></view>
      <!-- 导航栏 -->
      <view
        class="custom-navigation"
        :style="{ height: 40 + 'px', 'padding-right': 40 + 'px' }"
      >
        <view class="dropdown">
          <view class="dropdown-button" @click="toggleDropdown">
            <!-- <u-icon name="more-dot-fill" size="30" color="#333" v-if="istab"></u-icon> -->
            <i-more theme="outline" size="24" fill="#fff" v-if="istab" />
            <i-down theme="outline" size="24" fill="#fff" v-if="!istab" />
            <!-- <u-icon name="arrow-down" size="30" color="#333" v-if="!istab"></u-icon> -->
          </view>
          <view v-show="isDropdownOpen" class="dropdown-menu">
            <view
              class="dropdown-item"
              @click="handleMenuItem('userInformation/userInformation')"
            >
              <i-user theme="outline" size="24" fill="#333" />
            </view>
            <view class="dropdown-item" @click="handleMenuItem('Item 2')">
              <i-history theme="outline" size="24" fill="#333" />
            </view>
            <view class="dropdown-item" @click="handleMenuItem('Item 3')">
              <i-setting theme="outline" size="24" fill="#333" />
            </view>
          </view>
        </view>
        <text class="title">首页</text>
      </view>
      <!-- 轮播图 -->
      <view class="wrap">
        <swiper
          class="swiper"
          circular
          :autoplay="true"
          :indicator-dots="true"
          :interval="5000"
          :duration="500"
        >
          <swiper-item>
            <image
              src="/static/img/carouselImage3.png"
              mode="scaleToFill"
            ></image>
          </swiper-item>
          <swiper-item>
            <image
              src="/static/img/carouselImage4.png"
              mode="scaleToFill"
            ></image>
          </swiper-item>
        </swiper>
      </view>
      <!-- 今日订单信息 -->
      <view class="">
        <view class="orderItems" @click="goLogin">
          <view v-for="item in 9" :key="item" class="orderItem">
            <view class="grid-text"
              ><i-finance
                class="icon"
                theme="outline"
                size="18"
                fill="#8d8d8d"
              />0
            </view>
            <text class="orderTitle">今日销售0笔</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 智能提示 -->
    <view class="aiPrompt">
      <view class="intelligentAssistant" @click="isPrompt = !isPrompt">
        <image src="/static/img/aiAssistant.png" alt="" />
      </view>
      <view class="userSendRecord" v-if="isPrompt">
        <text>{{ aiGreetings }}</text>
        <!-- <view class="timeRecord">
          <view class="time"> 今日 14:02 </view>
          <view class="me"> 仓小助 </view>
        </view> -->
      </view>
    </view>
    <view class="bottom">
      <!-- 常用功能（收起时显示） -->
      <view
        class="commonFunctions"
        :style="dynamicBottomStyle"
        v-if="isfunction"
      >
        <view class="functionTitle" style="padding-left: 16rpx">常用功能</view>
        <view class="commonFunctions-row-with-icon">
          <scroll-view
            class="commonFunctions-scroll"
            scroll-x="true"
            show-scrollbar="false"
          >
            <view class="commonFunctions-row">
              <view
                class="commonFunctions-item"
                v-for="(item, index) in commonlyUsedFunctions"
                :key="index"
                @click="jumpToCorrespondingPage(item.path)"
              >
                {{ item.name }}
              </view>
            </view>
          </scroll-view>
          <view class="detailFunction" @click="expandFunction">
            <i-up theme="outline" size="24" fill="#4095e5" v-if="isfunction" />
            <i-down theme="outline" size="24" fill="#4095e5" v-else />
          </view>
        </view>
      </view>

      <!-- 展开时显示历史功能和常用功能 -->
      <view class="allFunctions" :style="dynamicBottomStyle" v-else>
        <view class="functions">
          <!-- 历史功能 -->
          <view class="historyFunction">
            <view class="functionTitle">历史功能</view>
            <view class="historyFunction-content">
              <view
                class="allFunctions-item"
                v-for="(item, index) in historyFunctions"
                :key="index"
                @click="jumpToCorrespondingPage(item.path)"
              >
                {{ item.name }}
              </view>
              <!-- 空状态显示 -->
              <view class="empty-state" v-if="historyFunctions.length === 0">
                <text class="empty-text">暂无历史功能</text>
              </view>
            </view>
          </view>
          <!-- 常用功能（展开时在下方） -->
          <view class="commonFunctions-expand">
            <view class="functionTitle" style="padding-left: 16rpx"
              >常用功能</view
            >
            <view class="commonFunctions-row-with-icon">
              <scroll-view
                class="commonFunctions-scroll"
                scroll-x="true"
                show-scrollbar="false"
              >
                <view class="commonFunctions-row">
                  <view
                    class="commonFunctions-item"
                    v-for="(item, index) in commonlyUsedFunctions"
                    :key="index"
                    @click="jumpToCorrespondingPage(item.path)"
                  >
                    {{ item.name }}
                  </view>
                </view>
              </scroll-view>
              <view class="detailFunction" @click="expandFunction">
                <i-up
                  theme="outline"
                  size="24"
                  fill="#4095e5"
                  v-if="isfunction"
                />
                <i-down theme="outline" size="24" fill="#4095e5" v-else />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 输入框 -->
      <view class="">
        <Input style="height: 100%" :safeDistance="safeDistance" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import Input from "@/components/input/input.vue";

const isDropdownOpen = ref(false);
const isPrompt = ref(true); //是否展示提示语
const aiGreetings = ref("欢迎来到仓小助");
const istab = ref(true); //头部左侧状态变化
const isfunction = ref(true); // 默认显示常用功能
const inputContent = ref("");
const imageSrc = ref(""); // 用于存储拍照后的图片路径

// 胶囊信息
const menuButtonRect = reactive({
  width: 0,
});
const height = ref(0); //状态栏高度
const safeDistance = ref(0); //底部安全距离
const width = ref(0); //导航栏宽度

const historyFunctions = reactive([
  {
    id: 5,
    name: "商品管理",
    path: "commodityManagement/commodityManagement",
  },
  {
    id: 6,
    name: "采购订单",
    path: "purchase/purchase",
  },
  {
    id: 7,
    name: "进货订单",
    path: "purchaseGoods/purchaseGoods",
  },
  {
    id: 8,
    name: "退货订单",
    path: "returnGoodsOrder/returnGoodsOrder",
  },
  {
    id: 9,
    name: "零售订单",
    path: "order/order",
  },
  {
    id: 10,
    name: "销售订单",
    path: "salesOrder/salesOrder",
  },
  {
    id: 9,
    name: "零售订单",
    path: "order/order",
  },
  {
    id: 10,
    name: "销售订单",
    path: "salesOrder/salesOrder",
  },
]); // 空数组，显示空状态

const commonlyUsedFunctions = reactive([
  {
    id: 5,
    name: "商品管理",
    path: "commodityManagement/commodityManagement",
  },
  {
    id: 6,
    name: "采购订单",
    path: "purchase/purchase",
  },
  {
    id: 7,
    name: "进货订单",
    path: "purchaseGoods/purchaseGoods",
  },
  {
    id: 8,
    name: "退货订单",
    path: "returnGoodsOrder/returnGoodsOrder",
  },
  {
    id: 9,
    name: "零售订单",
    path: "retailOrders/retailOrders",
  },
  {
    id: 9,
    name: "零售退货订单",
    path: "returnRetailOrders/returnRetailOrders",
  },
  {
    id: 11,
    name: "销售订单",
    path: "salesOrder/salesOrder",
  },
  {
    id: 12,
    name: "销售退货",
    path: "salesReturn/salesReturnOrder",
  },
  {
    id: 13,
    name: "销货订单",
    path: "wholesaleOrders/wholesaleOrders",
  },
]);

const dynamicBottomStyle = computed(() => {
  // 动态计算 bottom 的值
  return `bottom: calc(7vh + ${safeDistance.value}rpx + 10rpx)`;
});

const init = () => {
  getRectInfo();
};

const getRectInfo = () => {
  // 获取系统信息
  const sysInfo = uni.getSystemInfoSync();
  console.log(sysInfo);
  safeDistance.value = sysInfo.safeAreaInsets.bottom;
  // statusBarHeight is not directly used in template, but can be kept for reference
  // const statusBarHeight = sysInfo.statusBarHeight;
  // 默认导航栏高度（如果没有胶囊按钮）
  const defaultNavBarheight = 40; // 可以根据需要调整默认高度
  height.value = sysInfo.statusBarHeight; // Use actual statusBarHeight for height

  // 判断是否支持获取胶囊按钮布局信息
  if (uni.canIUse("getMenuButtonBoundingClientRect")) {
    // 获取胶囊按钮布局信息
    const menuButton = uni.getMenuButtonBoundingClientRect();
    menuButtonRect.width = menuButton.width; // Update reactive object
    width.value = menuButton.right - menuButton.width;
  }
};

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
  istab.value = !istab.value;
};

const handleMenuItem = (item: string) => {
  console.log(`Selected: ${item}`);
  uni.navigateTo({
    url: `/pages/${item}`,
  });
  isDropdownOpen.value = false;
};

const expandFunction = () => {
  isfunction.value = !isfunction.value;
};

const jumpToCorrespondingPage = (path: string) => {
  console.log(111);
  uni.navigateTo({
    url: `/pages/${path}`,
  });
};

const goLogin = () => {
  uni.navigateTo({
    url: "/pages/login/login",
  });
};

onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.custom-navigation {
  height: var(--status-bar-height);
  /* 使用 status-bar 的高度 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #2b85e4;
  position: relative;
}

.dropdown {
  display: flex;
  position: relative;
  flex-direction: column;
}

.dropdown-button {
  width: 30px;
  height: 30px;
  margin-left: 20rpx;
  background-color: #1f60a5;
  border: #638fbd 0.5rpx solid;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-icon {
  width: 24px;
  height: 24px;
}

.title {
  color: white;
  font-size: 18px;
  flex: 1;
  text-align: center;
}

.wrap {
  border: #2b85e4 5px solid;
}

.dropdown-menu {
  left: 10px;
  position: absolute;
  top: 32px;
  border: 2px solid #2b85e4;
  border-radius: 30rpx;
  z-index: 100;
}

.dropdown-item {
  cursor: pointer;
  background-color: #ecf5ff;
  width: 100rpx;
  height: 70rpx;
  border-bottom: #397bff 1px solid;
  display: flex;
  align-items: center;
  justify-content: center;

  image {
    width: 70%;
    height: 70%;
  }
}

.dropdown-item:first-child {
  border-top-left-radius: 27rpx;
  border-top-right-radius: 27rpx;
}

.dropdown-item:nth-child(3) {
  border-bottom-left-radius: 27rpx;
  border-bottom-right-radius: 27rpx;
  border-bottom: none;
}

.orderTitle {
  font-size: 20rpx;
  color: #91959e;
}

.orderItems {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}

.orderItem {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  margin: 10rpx 20rpx;
  background-color: #f2f6fe;
}

.grid-text {
  display: flex;
  font-size: 30px;
  margin-top: 2px;
  color: black;
  flex-direction: row;
  padding-top: 10rpx;

  .icon {
    padding-right: 10rpx;
  }
}

.intelligentAssistant {
  width: 150rpx;
  height: 150rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 35rpx;
  /*animation: wave 2s infinite;*/
  /* 应用水波效果动画 */
  /*border-radius: 50%;
	margin-left: 20rpx;*/

  image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.aiPrompt {
  display: flex;
  align-items: center;
}

@keyframes wave {
  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.commonFunctions {
  padding: 20rpx;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;

  .functionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
    padding-right: 30rpx; // 右侧安全距离
  }

  .commonFunctions-scroll {
    width: 90%;
    white-space: nowrap;
  }

  .commonFunctions-row {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
  }

  .commonFunctions-item {
    display: inline-block;
    min-width: 160rpx;
    padding: 10rpx 0rpx;
    margin-right: 20rpx;
    background-color: #fff;
    font-size: 26rpx;
    border: #b2d3f6 1px solid;
    border-radius: 40rpx;
    text-align: center;
    white-space: nowrap;
  }

  .detailFunction {
    width: 20px;
    height: 20px;
    border: 1px #9bc5f2 solid;
    border-radius: 50%;
    color: #9bc5f2;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.allFunctions {
  padding: 0 20rpx;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  position: fixed; // 让它悬浮
  left: 0;
  // bottom 由 dynamicBottomStyle 控制
  z-index: 99;
  max-height: 40vh; // 你可以根据实际页面调整
  overflow-y: auto;

  .functions {
    display: flex;
    flex-direction: column;
  }

  .historyFunction {
    display: flex;
    flex-direction: column;

    .functionTitle {
      font-size: 28rpx;
      font-weight: 700;
      padding: 10rpx 0 10rpx 16rpx;
      color: #333;
    }

    .historyFunction-content {
      display: flex;
      flex-wrap: wrap;
          padding: 0 16rpx;
      min-height: 80rpx;
    }

    .allFunctions-item {
      display: inline-block;
      min-width: 145rpx;
      margin-top: 10rpx;
      padding: 10rpx 0rpx;
      margin-right: 20rpx;
      background-color: #fff;
      font-size: 26rpx;
      border: #b2d3f6 1px solid;
      border-radius: 40rpx;
      text-align: center;
      white-space: nowrap;
    }

    .empty-state {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30rpx 0;

      .empty-text {
        color: #999;
        font-size: 24rpx;
      }
    }
  }

  .commonFunctions-expand {
    margin-top: 20rpx;

    .functionTitle {
      font-size: 28rpx;
      font-weight: 700;
      padding: 10rpx 0 10rpx 16rpx;
      color: #333;
    }

    .commonFunctions-row-with-icon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16rpx;
    }

    .commonFunctions-scroll {
      flex: 1;
      width: 90%;
      white-space: nowrap;
    }

    .commonFunctions-row {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
    }

    .commonFunctions-item {
      display: inline-block;
      min-width: 160rpx;
      padding: 10rpx 0rpx;
      margin-right: 20rpx;
      background-color: #fff;
      font-size: 26rpx;
      border: #b2d3f6 1px solid;
      border-radius: 40rpx;
      text-align: center;
      white-space: nowrap;
    }

    .detailFunction {
      margin-left: 16rpx;
      display: flex;
      align-items: center;
      cursor: pointer;
      width: 20px;
      height: 20px;
      border: 1px #9bc5f2 solid;
      border-radius: 50%;
      color: #9bc5f2;
      justify-content: center;
    }
  }
}

.functionTitle {
  font-size: 28rpx;
  font-weight: 700;
  padding: 10rpx;
  margin-left: 5rpx;
  color: #333;
}

.swiper {
  width: 100%;
  height: 150px;
  /* 根据需要调整高度 */

  swiper-item image {
    width: 100%;
    /* 图片宽度自适应容器宽度 */
    height: 100%;
    /* 图片高度自适应容器高度 */
  }
}

.swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.airRecord {
  width: 55%;
  background-color: #f1f4fa;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.userSendRecord {
  width: 55%;
  height: 50%;
  background-color: #f5f1ff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.timeRecord {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;

  .me {
    margin-right: 20rpx;
  }
}

.commonFunctions-scroll {
  width: 100%;
  white-space: nowrap;
}

.commonFunctions-row {
  display: flex;
  flex-direction: row;
}

.commonFunctions-row-with-icon {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
}

.commonFunctions-scroll {
  flex: 1;
}

.detailFunction {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.commonFunctions-expand {
  margin-top: 20rpx;
}
</style>
