<template>
  <view>
    <u-popup v-model:show="localEditShow" mode="bottom" @close="closeEdit">
      <view class="container">
        <view class="categoryConfirmationBtn">
          <view @click="closeEdit">取消</view>
          <view class="popup_title">{{
            productCategoryShowInfo.id ? "修改分类" : "新建分类"
          }}</view>
          <view class="blueFont" @click="handleSubmit">确定</view>
        </view>
        <view class="divider"></view>
        <view class="inputArea">
          <u--form labelPosition="left" :model="productCategoryShowInfo" :rules="rules" ref="uForm">
            <u-form-item :label-width="'100px'" label="名称" prop="name" borderBottom ref="item1" required>
              <u--input v-model="productCategoryShowInfo.name" inputAlign="right" placeholder="请输入名称"
                border="none"></u--input>
            </u-form-item>
            <u-form-item :label-width="'100px'" label="上级目录" prop="parentName" borderBottom ref="item1"
              >
              <u--input v-model="parentName" inputAlign="right" suffixIcon="grid-fill" suffixIconStyle="color: #d3e9f8"
                placeholder="请选择上级目录" border="none" disabled disabledColor="#fff" @tap="selectProductCategory">
              </u--input>
              <scroll-view class="unitArea_content" scroll-y="true" :style="{ ' max-height': '220rpx' }"
                v-if="isSelectParentShow">
                <view class="unitArea_item" v-for="(item, index) in parentCategory" :key="index" :data-item="item"
                  @tap="selectParent(item)">
                  <text class="categoryName">{{ item.name }}</text>
                </view>
              </scroll-view>
            </u-form-item>
          </u--form>
        </view>
      </view>
    </u-popup>

    <selePdCa :selePdCaShow="selePdCaShow" @update:show="updateParentShow" @update:category="updateCategory" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue';
import selePdCa from "./selectProductCategory.vue";
import { addCatalogtypes, editCatalogtypes } from "@/api/category";

// 定义接口
interface CategoryInfo {
  id: string | null;
  name: string;
  parent: string | null;
}

interface CategoryItem {
  id: string;
  name: string;
  parent: string | null;
  children?: CategoryItem[];
}

// 定义 props
const props = defineProps({
  editShow: {
    type: Boolean,
    default: false,
  },
  editInfo: {
    type: Object as () => {
      editName: string;
      editId: string | null;
      editParent: string | null;
      editParentName: string | null;
      for_type: string;
    },
    default: () => ({
      editName: "",
      editId: null,
      editParent: null,
      editParentName: null,
      for_type: "",
    }),
  },
  categoryList: {
    type: Array as () => CategoryItem[],
    default: () => [],
  },
  CategoryName: {
    type: String,
    default: "",
  },
});

// 定义 emits
const emit = defineEmits(['update:show', 'refresh']);

// 响应式数据
const isSelectParentShow = ref(false); // 是否显示上级目录选择
const selePdCaShow = ref(false); // 选择商品类别弹出层
const localEditShow = ref(props.editShow);
const parentCategory = ref<CategoryItem[]>([]); // 上级目录
const parentCategoryList = ref<CategoryItem[]>([]); // 上级目录原始列表
const parentName = ref(""); // 上级目录名称
const productCategoryShowInfo = reactive<CategoryInfo>({
  id: props.editInfo.editId || null,
  name: props.editInfo.editName || "",
  parent: props.editInfo.editParent || null,
});
const rules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
};

// 模板引用
const uForm = ref(null);

// 监听 props.editShow 的变化
watch(() => props.editShow, (newValue) => {
  localEditShow.value = newValue;
  if (!newValue) {
    isSelectParentShow.value = newValue;
  }
  productCategoryShowInfo.id = props.editInfo.editId || null;
  productCategoryShowInfo.name = props.editInfo.editName || "";
  productCategoryShowInfo.parent = props.editInfo.editParent || null;
  parentName.value = props.editInfo.editParentName || "";
});

// 监听 props.editInfo 的变化
watch(() => props.editInfo, (newValue) => {
  productCategoryShowInfo.name = newValue.editName;
  productCategoryShowInfo.id = newValue.editId;
  productCategoryShowInfo.parent = newValue.editParent;
  parentName.value = newValue.editParentName || "";
  // 过滤父级分类选项，排除自己和自己的子分类
  filterParentCategories(newValue.editId);
}, { deep: true });

// 监听 props.categoryList 的变化
watch(() => props.categoryList, (newValue) => {
  console.log(newValue);
  // 过滤出一级分类（parent为null或空的分类）
  parentCategory.value = newValue.filter(item => !item.parent);
  parentCategoryList.value = parentCategory.value; // 保存原始一级分类列表
  // 如果是编辑模式，需要进一步过滤
  if (productCategoryShowInfo.id) {
    filterParentCategories(productCategoryShowInfo.id);
  }
}, { deep: true });

// 方法
/**
 * 过滤父级分类选项，排除自己和自己的所有子分类
 * @param currentId - 当前分类的ID
 */
const filterParentCategories = (currentId: string | null) => {
  if (!currentId || !props.categoryList.length) return;

  // 获取当前分类的所有子分类ID（递归查找）
  const getChildrenIds = (parentId: string, allCategories: CategoryItem[]) => {
    const childrenIds: string[] = [];
    const findChildren = (pid: string) => {
      allCategories.forEach(item => {
        if (item.parent === pid) {
          childrenIds.push(item.id);
          findChildren(item.id); // 递归查找子分类的子分类
        }
      });
    };
    findChildren(parentId);
    return childrenIds;
  };

  const childrenIds = getChildrenIds(currentId, props.categoryList);
  const excludeIds = [currentId, ...childrenIds]; // 排除自己和所有子分类

  // 过滤父级分类选项
  parentCategory.value = parentCategoryList.value.filter(item =>
    !excludeIds.includes(item.id)
  );
};

/**
 * 处理提交表单
 */
const handleSubmit = () => {
  if (uForm.value) {
    (uForm.value as any).validate().then(() => {
      if (productCategoryShowInfo.id) {
        updateGoodsTypeMethod();
      } else {
        addGoodsTypeMethod();
      }
    }).catch((errors: any) => {
      console.log(errors);
      uni.showToast({
        title: "校验失败",
        icon: "none",
      });
    });
  }
};

/**
 * 新建分类
 */
const addGoodsTypeMethod = async () => {
  const params = {
    name: productCategoryShowInfo.name,
    parent: productCategoryShowInfo.parent || null,
    for_type: props.CategoryName,
  };

  try {
    const res = await addCatalogtypes(params);
    emit("refresh");
    uni.showToast({
      title: "新建成功",
      icon: "success",
    });
    closeEdit();
  } catch (err: any) {
    uni.showToast({
      title: err.msg || err.message || "新建失败",
      icon: "none",
    });
  }
};

/**
 * 修改分类
 */
const updateGoodsTypeMethod = async () => {
  const params = {
    id: productCategoryShowInfo.id,
    name: productCategoryShowInfo.name,
    parent: productCategoryShowInfo.parent || null,
    for_type: props.CategoryName,
  };

  try {
    const res = await editCatalogtypes(params);
    emit("refresh");
    if (res.code === 0) {
      uni.showToast({
        title: res.msg,
        icon: "success",
      });
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
    closeEdit();
  } catch (err: any) {
    uni.showToast({
      title: err.msg || err.message || "修改失败",
      icon: "none",
    });
  }
};

/**
 * 选择父级分类
 * @param item - 选中的父级分类项
 */
const selectParent = (item: CategoryItem) => {
  console.log(item);
  productCategoryShowInfo.parent = item.id;
  parentName.value = item.name;
  isSelectParentShow.value = false;
};

/**
 * 关闭编辑弹出层
 */
const closeEdit = () => {
  localEditShow.value = false;
  emit("update:show", localEditShow.value);
};

/**
 * 更新父组件的显示状态
 * @param newMessage - 新的显示状态
 */
const updateParentShow = (newMessage: boolean) => {
  selePdCaShow.value = newMessage;
};

/**
 * 更新分类信息
 * @param newMessage - 新的分类信息
 */
const updateCategory = (newMessage: { id: string; name: string }) => {
  productCategoryShowInfo.parent = newMessage.id;
  parentName.value = newMessage.name;
};

/**
 * 选择商品类别（上级目录）
 */
const selectProductCategory = () => {
  isSelectParentShow.value = !isSelectParentShow.value;

  // 过滤出一级分类（parent为null或空的分类）
  parentCategory.value = parentCategoryList.value.filter(item => !item.parent);

  // 如果是编辑模式，需要进一步过滤
  if (productCategoryShowInfo.id) {
    filterParentCategories(productCategoryShowInfo.id);
  }
};

// onReady 钩子，用于设置表单规则
nextTick(() => {
  if (uForm.value) {
    (uForm.value as any).setRules(rules);
  }
});
</script>

<style lang="scss" scoped>
.container {
  padding: 0 20rpx;
  height: 600rpx;
  overflow: auto;
}

//商品类别弹出层
.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.category {
  display: flex;
}

.blueFont {
  color: #2979ff;
}

.unitArea_content {
  position: absolute;
  width: 200rpx;
  right: 40rpx;
  top: 260rpx;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  max-height: 220rpx;
  overflow-y: auto;
}

.unitArea_item {
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #ccc;
  background-color: #fff;
  font-size: 28rpx;
  color: #333;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;
  }

  &:hover {
    background-color: #f8f9fa;
  }

  text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
}
</style>