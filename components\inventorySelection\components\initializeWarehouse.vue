<template>
  <view>
    <u-popup :show="initShow" mode="bottom" round="8" @close="onClose">
      <view class="init-warehouse-popup">
        <view class="header">
          <view class="cancel" @click="onClose">取消</view>
          <view class="title">初始化库存</view>
          <view class="confirm" @click="onConfirm">确认</view>
        </view>
        <view class="warehouse-name">
          <view class="name-bar"></view>
          <view class="name-text">{{ initWarehouseInfo.name }}</view>
        </view>
        <view class="form-list" v-if="form.length > 0">
          <view v-for="(item, index) in unitsList" :key="index">
            <view class="form-item">
              <view class="label">库存数量</view>
              <u-input v-model="form[index].quantity" class="input" placeholder="请输入库存数量" border="none"
                inputAlign="right" />
              <view class="unit_type_id">{{ item.unit_type_name }}</view>
            </view>
            <view class="form-item">
              <view class="label">单件成本</view>
              <u-input v-model="form[index].actual_cost" class="input" placeholder="请输入单件成本" border="none"
                inputAlign="right" />
            </view>
            <view class="form-item" v-if="index !== unitsList.length - 1"></view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';

interface ProductUnit {
  unit_type_id: string;
  conversion_rate: number;
  unit_type_name: string;
}

interface InitDataFormItem {
  warehouse: number;
  quantity: string | number;
  unit_type_id: string;
  actual_cost: string | number;
}

const props = defineProps({
  initShow: {
    type: Boolean,
    default: false,
  },
  units: {
    type: Array as () => ProductUnit[],
    default: () => [],
  },
  initWarehouseInfo: {
    type: Object,
    default: () => ({}),
  },
  currentWarehouseInitData: {
    type: Array as () => InitDataFormItem[],
    default: () => [],
  },
});

const emit = defineEmits(['close', 'confirm']);

const unitsList = ref<ProductUnit[]>([]); //单位
const form = ref<InitDataFormItem[]>([]);
const total = ref(0);

watch(() => props.units, (newUnits) => {
  if (newUnits && newUnits.length > 0) {
    unitsList.value = newUnits;

    // 初始化表单结构
    initForm();

    // 如果有现有数据，填充匹配的数据
    if (props.currentWarehouseInitData.length > 0) {
      fillExistingData();
    }
  }
}, { immediate: true });

watch(() => props.currentWarehouseInitData, (newVal) => {
  console.log('子组件接收到的数据变化:', newVal);
  if (unitsList.value.length > 0) {
    // 重新初始化表单
    initForm();
    // 如果有数据则填充，没有数据则保持空白
    if (newVal && newVal.length > 0) {
      fillExistingData();
    }
  }
}, { immediate: true });

watch(() => props.initShow, (newVal) => {
  if (newVal && unitsList.value.length > 0) {
    // 弹窗打开时重新初始化表单
    initForm();
    // 如果有现有数据，填充数据
    if (props.currentWarehouseInitData && props.currentWarehouseInitData.length > 0) {
      fillExistingData();
    }
  }
}, { immediate: false });

onMounted(() => {
  initForm();
  fillExistingData();
});

// 初始化表单
const initForm = () => {
  if (!props.initWarehouseInfo.id || !unitsList.value.length) return;

  form.value = unitsList.value.map((unit) => ({
    warehouse: props.initWarehouseInfo.id,
    quantity: '',
    unit_type_id: unit.unit_type_id,
    actual_cost: '',
  }));

  console.log('初始化表单:', form.value);
};

// 填充现有数据
const fillExistingData = () => {
  if (!form.value.length || !props.currentWarehouseInitData.length) return;

  form.value = form.value.map((formItem) => {
    const matchingData = props.currentWarehouseInitData.find(data =>
      data.warehouse === formItem.warehouse &&
      data.unit_type_id === formItem.unit_type_id
    );

    if (matchingData) {
      return {
        ...formItem,
        quantity: matchingData.quantity || '',
        actual_cost: matchingData.actual_cost || ''
      };
    }

    return formItem;
  });

  console.log('填充数据后的表单:', form.value);
};

// 修改：发送更新后的数据给父组件
const emitUpdatedData = () => {
  const validForm = form.value.map((item) => ({
    warehouse: props.initWarehouseInfo.id,
    quantity: item.quantity || '',
    unit_type_id: item.unit_type_id || '',
    actual_cost: item.actual_cost || ''
  }));

  // 重新计算总数
  let totalVal = 0;
  form.value.forEach((item) => {
    const matchingUnit = unitsList.value.find(unit => unit.unit_type_id === item.unit_type_id);
    if (matchingUnit && item.quantity) {
      const quantity = Number(item.quantity) || 0;
      const rate = Number(matchingUnit.conversion_rate) || 0;
      totalVal += quantity * rate;
    }
  });
  total.value = totalVal;

  emit("confirm", validForm, total.value);

  // 移除这里的清空表单调用
  // clearForm();
};

// 清空表单数据
const clearForm = () => {
  form.value = form.value.map((item) => ({
    ...item,
    quantity: '',
    actual_cost: ''
  }));
  console.log('清空表单后:', form.value);
};

// 关闭时不清空表单，让父组件控制数据
const onClose = () => {
  emit("close");
};
// 修改：确认按钮处理
const onConfirm = () => {
  emitUpdatedData();
  onClose();
};
// 计算总数
const getTotal = () => {
  let totalVal = 0;
  form.value.forEach((item) => {
    const quantity = Number(item.quantity) || 0;
    const matchingUnit = unitsList.value.find(unit => unit.unit_type_id === item.unit_type_id);
    const rate = Number(matchingUnit?.conversion_rate) || 0;
    totalVal += quantity * rate;
  });
  return totalVal;
};
</script>
<style lang="scss" scoped>
.init-warehouse-popup {
  background: #fff;
  width: 100%;
  height: 800rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx 10rpx 30rpx;
  border-bottom: 1px solid #eee;

  .cancel {
    color: #333;
    font-size: 30rpx;
  }

  .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #222;
  }

  .confirm {
    color: #2b85e4;
    font-size: 30rpx;
  }
}

.warehouse-name {
  display: flex;
  align-items: center;
  padding: 20rpx 0 10rpx 30rpx;

  .name-bar {
    width: 6rpx;
    height: 32rpx;
    background: #2b85e4;
    border-radius: 4rpx;
    margin-right: 16rpx;
  }

  .name-text {
    font-size: 28rpx;
    color: #222;
    font-weight: bold;
  }
}

.form-list {
  padding: 0 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding: 18rpx 0;
  height: 48rpx;
  font-size: 28rpx;

  .label {
    width: 180rpx;
    color: #333;
  }

  .input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 28rpx;
    color: #666;
    outline: none;
    padding: 0;

    &::placeholder {
      color: #ccc;
    }
  }

  .unit_type_id {
    font-size: 28rpx;
    margin-left: 10rpx;
  }
}
</style>

