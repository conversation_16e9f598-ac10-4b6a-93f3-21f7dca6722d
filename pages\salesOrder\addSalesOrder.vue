<template>
  <view class="container">
    <scroll-view scroll-y class="content">
      <view class="info">
        <view class="info_title">
          <text>基本信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left" :rules="rules" :model="salesOrder" ref="uForm">
            <u-form-item :label-width="'180rpx'" label="客户" prop="customer_name" :additionType="isFormDisabled ? 0 : 1" borderBottom required >
              <u--input v-model="salesOrder.customer_name" border="none" placeholder="请选择客户" inputAlign="right" disabled
                disabledColor="#fff" @tap="!isFormDisabled && openSelector(1)">
              </u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="订单号" borderBottom>
              <u--input v-model="salesOrder.orderid" border="none" placeholder="自动生成" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="预计交货日期" prop="expected_delivery_date" borderBottom required>
              <u--input v-model="salesOrder.expected_delivery_date" border="none" placeholder="请选择预计交货日期"
                inputAlign="right" disabled disabledColor="#fff" @tap="!isFormDisabled && (showDatePicker = true)">
              </u--input>
            </u-form-item>
          </u--form>
        </view>
      </view>


      <!--  商品清单  -->
      <MerchBill
        :items="salesOrder.items"
        :type="'default'"
        :prohibit-modification="isFormDisabled"
        @open-product-select="openProductSelect"
        @open-product-details="openProductDetails"
        @amount-change="handleAmountChange"
      />

      <!-- 结算信息 -->
      <view class="info">
        <view class="info_title">
          <text>结算信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom>
              <view class="account-input-wrapper">
                <u--input v-model="salesOrder.payment_method_name" border="none" placeholder="请选择结算账户" inputAlign="right"
                  disabled disabledColor="#fff" @tap="handleAccountInputTap"
                  :required="salesOrder.pay_amount > 0"></u--input>
                <view class="delete-account-btn" v-if="salesOrder.payment_method_name && !isFormDisabled" @click="clearAccount">
                  <i-close-one theme="filled" size="20" fill="#d13b3b" />
                </view>
              </view>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="预付款" borderBottom>
              <u--input v-model="salesOrder.pay_amount" border="none" placeholder="0" inputAlign="right"
                :disabled="isFormDisabled" :disabledColor="isFormDisabled ? '#f5f5f5' : '#fff'"
                @blur="!isFormDisabled && changePayAmount"></u--input>
            </u-form-item>
          </u--form>
        </view>
      </view>


      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="salesOrder.remark" placeholder="请输入备注" autoHeight border="none"
            :disabled="isFormDisabled" :disabledColor="isFormDisabled ? '#f5f5f5' : '#fff'"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <imageUpload :prohibitModification="isFormDisabled"  />
      <!-- 底部操作栏 -->
      <!-- 如果为部分到货/全部到货，即不可修改 -->
      <view class="operation" v-if="buttonStates.showSave">
        <!-- 删除按钮：只有待处理状态的订单才显示 -->
        <u-button type="error" text="删除" v-if="buttonStates.showDelete" @click="deleteSalesOrder"></u-button>
        <!-- 保存按钮：始终显示（除非订单已完成） -->
        <u-button type="primary" text="保存" @click="saveSalesOrder"></u-button>
      </view>


    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>



    <!-- 引入搜索选择器组件，用于展示选择器弹窗 -->
    <!-- :selectorShow.sync 用于双向绑定选择器的显示状态 -->
    <!-- :list 用于传递选择器的选项列表 -->
    <!-- :selectType 用于指定选择器的类型 -->
    <!-- @confirm 当用户确认选择时触发 selectCustomer 方法 -->
    <!-- @close 当用户关闭选择器时触发 closePopup 方法 -->
    <!-- @customerScrollToLower 当选择器滚动到底部时触发 getCustomerList 方法 -->
    <searchSelector v-model:selectorShow="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectCustomer" @close="closePopup" @customerScrollToLower="getCustomersList" />
    <product-details v-model:productDetailsShow="productDetailsShow" :type="2" :productData="productData"
      :isShowDelBtn="true" @delBtnPricing="handleDelBtnPricing" @close="handleProductDetailsClose"
      @confirm="handleProductDetailsConfirm" />
    <!-- 日期选择器 -->
    <datePickers :showDatePicker="showDatePicker" @close="closeDatePicker" @confirm="handleDateConfirm" />
    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />
  </view>
</template>



<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getCustomerList } from '@/api/customer';
import searchSelector from "@/components/searchSelector.vue";
import datePickers from "@/components/datePickers.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import eventBus from "@/utils/eventBus";
import goodsList from "@/components/goodsList.vue"; // 确保这个组件是需要的，如果MerchBill已经包含了商品列表功能，可能不需要
import { createSalesOrder, updateSalesOrder, deleteSalesOrder, getSalesOrderDetail } from '@/api/salesOrder';
import productDetails from "@/components/productDetails.vue";
import MerchBill from "@/components/merchbill.vue";
import Input from "@/components/input/input.vue"; // 确保引入了Input组件
import imageUpload from "@/components/imageUpload/imageUpload.vue"; // 确保引入了imageUpload组件

// 定义接口
interface OrderItem {
  id: number | string;
  item_name?: string;
  code?: string;
  unit?: number | string;
  unit_name?: string;
  quantity: number;
  price: number;
  purchase_price?: number;
  total: number;
  [key: string]: any;
}

interface SalesOrder {
  id: string;
  sum: number | string;
  customer: number;
  customer_name: string;
  warehouse: number;
  is_draft: boolean;
  total_amount: number | string;
  discount: number | string;
  payment_method: number | null;
  payment_method_name?: string;
  payment_method_discount: number;
  expected_delivery_date: string;
  pay_amount: number | string;
  debt: number | string;
  remark: string;
  items: OrderItem[];
  order_status?: string;
  order_id?: string;
  orderid?: string;
  discountRate?: number | string;
  received_amount?: number;
  unpaid_amount?: number;
  settlement_account?: { id: number, name: string };
}

interface Customer {
  id: number;
  name: string;
}

// 响应式数据
const productData = ref<OrderItem>({} as OrderItem); // 商品详情
const productDetailsShow = ref(false); // 商品详情弹出层
const showDatePicker = ref(false);
const accountSelectShow = ref(false); // 结算账户弹出层
const salesOrder = reactive<SalesOrder>({
  id: '',
  sum: 0,
  customer: 0,
  customer_name: '',
  warehouse: 1,
  is_draft: false,
  total_amount: 0,
  discount: 0,
  payment_method: null,
  payment_method_discount: 0,
  expected_delivery_date: "",
  pay_amount: 0,
  debt: 0,
  remark: "",
  items: []
});
const selectType = ref(1); // 搜索类型 1 客户
const selectorShow = ref(false);
const selectList = ref<any[]>([]);
const customerList = ref<Customer[]>([]);
const settlementAccountList = ref<any[]>([]); // 假设有结算账户列表
const isCustomerMore = ref(true);
const isSettlementAccountMore = ref(true);
const customerPageParams = reactive({
  page: 1,
  page_size: 20,
});
const rules = reactive({
  customer_name: {
    type: 'string',
    required: true,
    message: '请选择客户',
    trigger: ['blur', 'change']
  },
  expected_delivery_date: {
    type: 'string',
    required: true,
    message: '请选择预计交货日期',
    trigger: ['blur', 'change']
  },
});
const activeQtyIndex = ref(-1);

// 模板引用
const uForm = ref(null);

// 计算属性
const buttonStates = computed(() => {
  const isNewOrder = !salesOrder.id;
  const isCompleted = salesOrder.order_status === 'partial' || salesOrder.order_status === 'completed';
  const isPending = !salesOrder.order_status || salesOrder.order_status === 'pending';

  return {
    showDelete: !isNewOrder && isPending,
    showSave: !isCompleted
  };
});

const isFormDisabled = computed(() => {
  const isNewOrder = !salesOrder.id;
  const isPending = !salesOrder.order_status || salesOrder.order_status === 'pending';
  return !(isNewOrder || isPending);
});

// Watchers
watch(() => salesOrder.items, () => {
  calcTotalAndDiscount();
}, { deep: true, immediate: true });

watch(() => salesOrder.discountRate, (newVal) => {
  if (newVal && Number(newVal) > 100) {
    uni.showToast({
      title: '优惠率不能大于100%',
      icon: 'none'
    });
    salesOrder.discountRate = 100;
  }
});

// 方法
/**
 * 处理MerchBill组件的金额变化事件
 * @param amountData - 金额数据
 */
const handleAmountChange = (amountData: { totalAmount: number; discount: number; actualPurchasePrice: number }) => {
  if (isFormDisabled.value) {
    return;
  }
  console.log('接收到金额变化事件:', amountData);
  salesOrder.sum = amountData.totalAmount;
  salesOrder.discount = amountData.discount;
  salesOrder.total_amount = amountData.actualPurchasePrice;
};

/**
 * 加载销售订单详情
 * @param orderId - 订单ID
 */
const loadSalesOrderDetail = async (orderId: string) => {
  console.log('加载销售订单详情，ID:', orderId);
  uni.showLoading({ title: '加载中...' });

  try {
    const res: any = await getSalesOrderDetail(orderId);
    console.log('订单详情API返回:', res);
    if (res.code === 0) {
      const orderData = res.data;
      let itemsTotal = 0;
      if (orderData.items && orderData.items.length > 0) {
        itemsTotal = orderData.items.reduce((sum: number, item: OrderItem) => {
          return sum + (Number(item.price) * Number(item.quantity));
        }, 0);
      }

      Object.assign(salesOrder, {
        id: orderData.id,
        sum: itemsTotal.toFixed(2),
        customer: orderData.customer,
        customer_name: orderData.customer_name,
        warehouse: orderData.warehouse || 1,
        is_draft: orderData.is_draft,
        total_amount: Number(orderData.total_amount).toFixed(2),
        discount: Number(orderData.discount || 0).toFixed(2),
        discountRate: orderData.payment_method_discount || 0,
        payment_method: orderData.settlement_account?.id || null,
        payment_method_name: orderData.settlement_account?.name || '',
        payment_method_discount: orderData.payment_method_discount || 0,
        expected_delivery_date: orderData.expected_delivery_date,
        pay_amount: Number(orderData.received_amount || 0).toFixed(2),
        debt: Number(orderData.unpaid_amount || 0).toFixed(2),
        remark: orderData.remark || '',
        order_status: orderData.order_status,
        orderid: orderData.order_id,
        items: (orderData.items || []).map((item: any) => ({
          ...item,
          purchase_price: Number(item.price),
          quantity: Number(item.quantity),
          total: Number(item.amount),
          unit_name: item.unit_name,
          item_name: item.item_name,
          id: item.id,
          code: item.item
        }))
      });

      if (Number(salesOrder.sum) > 0 && Number(salesOrder.discount) > 0) {
        const discountRate = (Number(salesOrder.discount) / Number(salesOrder.sum)) * 100;
        salesOrder.discountRate = discountRate.toFixed(2);
      }
      console.log('订单数据已加载:', salesOrder);
    } else {
      uni.showToast({ title: res.msg || '加载订单详情失败', icon: 'none' });
    }
  } catch (err: any) {
    console.error('加载订单详情失败:', err);
    uni.showToast({ title: '加载订单详情失败', icon: 'none' });
  } finally {
    uni.hideLoading();
  }
};

/**
 * 打开商品详情弹出层
 * @param item - 商品项
 */
const openProductDetails = (item: OrderItem) => {
  console.log(item);
  productData.value = item;
  productDetailsShow.value = true;
};

/**
 * 关闭商品详情弹出层
 */
const handleProductDetailsClose = () => {
  productDetailsShow.value = false;
};

/**
 * 删除商品
 * @param item - 要删除的商品项
 */
const handleDelBtnPricing = (item: OrderItem) => {
  console.log('删除商品:', item);
  const index = salesOrder.items.findIndex(goods => goods.id === item.id || goods.code === item.code);
  if (index > -1) {
    salesOrder.items.splice(index, 1);
    console.log('商品已删除，剩余商品数量:', salesOrder.items.length);
    calcTotalAndDiscount();
    productDetailsShow.value = false;
    uni.showToast({ title: '商品已删除', icon: 'success' });
  }
};

/**
 * 商品详情弹出层确认
 * @param item - 确认后的商品项
 */
const handleProductDetailsConfirm = (item: OrderItem) => {
  console.log('商品详情确认，接收到的数据:', item);
  productData.value = item;

  for (let i = 0; i < salesOrder.items.length; i++) {
    if (salesOrder.items[i].id === item.id && salesOrder.items[i].unit === item.unit) {
      salesOrder.items[i] = {
        ...salesOrder.items[i],
        ...item,
        quantity: Number(item.quantity) || salesOrder.items[i].quantity,
        price: Number(item.price || item.purchase_price) || salesOrder.items[i].price,
        total: Number(item.total) || (Number(item.price || item.purchase_price) * Number(item.quantity))
      };
      console.log('已更新商品:', salesOrder.items[i]);
      break;
    }
  }
  calcTotalAndDiscount();
  productDetailsShow.value = false;
};

/**
 * 获取客户列表
 */
const getCustomersList = async () => {
  console.log('开始获取客户列表，参数：', customerPageParams);
  if (!isCustomerMore.value) return;
  try {
    const res: any = await getCustomerList(customerPageParams);
    console.log('客户列表API返回结果：', res);
    if (res.code === 0) {
      customerList.value = [...customerList.value, ...res.data.results];
      console.log('客户列表加载成功，共', customerList.value.length, '条数据');
      if (customerPageParams.page_size > res.data.results.length) {
        isCustomerMore.value = false;
      } else {
        customerPageParams.page++;
        isCustomerMore.value = true;
      }
    } else {
      console.error('客户列表加载失败：', res.msg);
      uni.showToast({ title: res.msg, icon: "none" });
    }
  } catch (err: any) {
    console.error('客户列表接口调用失败：', err);
  }
};

/**
 * 选择客户
 * @param item - 选中的客户
 */
const selectCustomer = (item: Customer) => {
  console.log('用户选择了客户：', item);
  if (selectType.value === 1) {
    salesOrder.customer = item.id;
    salesOrder.customer_name = item.name;
    nextTick(() => {
      (uForm.value as any).validateField('customer_name');
    });
  }
};

/**
 * 打开选择器
 * @param index - 选择器类型
 */
const openSelector = (index: number) => {
  if (isFormDisabled.value) {
    uni.showToast({ title: '当前状态不允许修改', icon: 'none' });
    return;
  }
  console.log('用户点击了选择客户，选择类型：', index);
  selectType.value = index;
  if (selectType.value === 1 && customerList.value.length === 0) {
    console.log('客户列表为空，正在拉取数据...');
    getCustomersList();
  }
  selectorShow.value = true;
  console.log('选择器已打开，当前状态：', selectorShow.value);
  if (selectType.value === 1) {
    selectList.value = customerList.value;
    console.log('客户选择列表数据：', selectList.value);
  } else {
    selectList.value = settlementAccountList.value;
  }
};

/**
 * 关闭搜索弹出层
 */
const closePopup = () => {
  console.log('用户关闭了选择器');
  selectorShow.value = false;
};

/**
 * 关闭日期选择器
 */
const closeDatePicker = () => {
  showDatePicker.value = false;
};

/**
 * 日期选择器确认
 * @param date - 选中的日期
 */
const handleDateConfirm = (date: string) => {
  console.log(date);
  salesOrder.expected_delivery_date = date;
  showDatePicker.value = false;
  nextTick(() => {
    (uForm.value as any).validateField('expected_delivery_date');
  });
};

/**
 * 跳转选择商品页面
 */
const openProductSelect = () => {
  if (isFormDisabled.value) {
    uni.showToast({ title: '当前状态不允许修改', icon: 'none' });
    return;
  }
  uni.navigateTo({
    url: "/components/productSelection" + "?type=2",
  });
};

/**
 * 处理结算账户输入框点击
 */
const handleAccountInputTap = () => {
  if (!isFormDisabled.value) {
    openAccountSelector();
  }
};

/**
 * 打开结算账户选择
 */
const openAccountSelector = () => {
  if (isFormDisabled.value) {
    uni.showToast({ title: '当前状态不允许修改', icon: 'none' });
    return;
  }
  console.log('打开结算账户选择器');
  accountSelectShow.value = true;
};

/**
 * 处理结算账户的数据
 * @param data - 结算账户数据
 */
const handleAccount = (data: { id: number; account_name: string }) => {
  salesOrder.payment_method = data.id;
  salesOrder.payment_method_name = data.account_name;
  console.log(salesOrder);
  closeAccountSelector();
};

/**
 * 关闭结算账户选择
 */
const closeAccountSelector = () => {
  accountSelectShow.value = false;
};

/**
 * 本次付款输入框失焦
 */
const changePayAmount = () => {
  if (isFormDisabled.value) {
    return;
  }
  const actualAmount = Number(salesOrder.total_amount) || 0;
  const payAmount = Number(salesOrder.pay_amount) || 0;
  salesOrder.debt = (actualAmount - payAmount).toFixed(2);
};

/**
 * 清除结算账户
 */
const clearAccount = () => {
  if (isFormDisabled.value) {
    uni.showToast({ title: '当前状态不允许修改', icon: 'none' });
    return;
  }
  salesOrder.payment_method = null;
  salesOrder.payment_method_name = '';
  uni.showToast({ title: '已清除结算账户', icon: 'success', duration: 1500 });
};

/**
 * 选择商品
 * @param data - 选中的商品数据
 */
const handleSelectGoodsList = (data: OrderItem[]) => {
  console.log("选择商品数据：", data);
  const formattedItems = data.map(item => ({
    ...item,
    price: Number(item.purchase_price) || 0,
    quantity: Number(item.quantity) || 1,
    total: Number(item.total) || 0
  }));

  formattedItems.forEach(newItem => {
    const existingItemIndex = salesOrder.items.findIndex(existingItem =>
      existingItem.id === newItem.id && existingItem.unit === newItem.unit
    );

    if (existingItemIndex !== -1) {
      salesOrder.items[existingItemIndex] = newItem;
      console.log(`覆盖商品: ${newItem.item_name} (${newItem.unit_name}), 新数量: ${newItem.quantity}`);
    } else {
      salesOrder.items.push(newItem);
      console.log(`添加新商品: ${newItem.item_name} (${newItem.unit_name}), 数量: ${newItem.quantity}`);
    }
  });

  console.log('商品处理完成，当前商品数量：', salesOrder.items.length);
  nextTick(() => {
    calcTotalAndDiscount();
    console.log('选择商品后金额计算完成');
  });
};

/**
 * 保存订单
 */
const saveSalesOrder = () => {
  if (!salesOrder.items || salesOrder.items.length === 0) {
    uni.showToast({ title: '请至少添加一个商品', icon: 'none' });
    return;
  }

  (uForm.value as any).validate().then(() => {
    performSave();
  }).catch((errors: any) => {
    console.log('表单验证失败：', errors);
    uni.showToast({ title: '请完善必填信息', icon: 'none' });
  });
};

/**
 * 删除销售订单
 */
const deleteSalesOrder = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个销售订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const res: any = await deleteSalesOrder(salesOrder);
          console.log(res);
          if (res.code === 0) {
            uni.showToast({
              title: "删除成功",
              icon: "success",
              duration: 1500,
              success: () => {
                eventBus.emit("refreshSalesOrders");
                setTimeout(() => {
                  uni.navigateBack();
                }, 500);
              }
            });
          } else {
            uni.showToast({ title: res.msg, icon: "none" });
          }
        } catch (err: any) {
          console.error('删除订单失败:', err);
          uni.showToast({ title: "删除失败，请稍后重试", icon: "none" });
        }
      }
    }
  });
};

/**
 * 执行保存操作
 */
const performSave = async () => {
  const orderData = { ...salesOrder };

  orderData.total_amount = Number(orderData.total_amount) || 0;
  orderData.discount = Number(orderData.discount) || 0;
  if (orderData.discountRate) {
    orderData.discountRate = Number(orderData.discountRate) || 0;
  }
  orderData.pay_amount = Number(orderData.pay_amount) || 0;
  orderData.debt = Number(orderData.debt) || 0;
  orderData.customer = Number(orderData.customer) || 0;
  orderData.warehouse = Number(orderData.warehouse) || 0;
  orderData.payment_method = Number(orderData.payment_method) || null;

  orderData.items = orderData.items.map(item => ({
    ...item,
    price: Number(item.price) || 0,
    quantity: Number(item.quantity) || 0,
    total: Number(item.total) || 0,
    id: Number(item.id) || 0
  }));

  console.log('转换后的订单数据:', orderData);

  const successTitle = "保存成功";
  const apiCall = orderData.id ? updateSalesOrder(orderData) : createSalesOrder(orderData);

  try {
    const res: any = await apiCall;
    console.log(res);
    if (res.code === 0) {
      uni.showToast({
        title: successTitle,
        icon: "success",
        duration: 1500,
        success: () => {
          eventBus.emit("refreshSalesOrders");
          setTimeout(() => {
            uni.navigateBack();
          }, 500);
        }
      });
    } else {
      uni.showToast({ title: res.msg, icon: "none" });
    }
  } catch (err: any) {
    console.error('保存失败:', err);
    uni.showToast({ title: "保存失败，请稍后重试", icon: "none" });
  }
};

/**
 * 计算总金额
 * @returns number - 总金额
 */
const calcTotalAmount = (): number => {
  let total = 0;
  if (Array.isArray(salesOrder.items) && salesOrder.items.length > 0) {
    salesOrder.items.forEach((item) => {
      const price = Number(item.price || item.purchase_price) || 0;
      const quantity = Number(item.quantity) || 0;
      const itemTotal = price * quantity;
      if (price > 0 && quantity > 0) {
        total += itemTotal;
      }
    });
  }
  salesOrder.sum = total.toFixed(2);
  return total;
};

/**
 * 根据优惠率计算优惠金额
 */
const calcDiscountAmountFromRate = () => {
  const total = Number(salesOrder.sum) || 0;
  if (salesOrder.discountRate) {
    const discountRate = Number(salesOrder.discountRate) || 0;
    salesOrder.discount = ((total * discountRate) / 100).toFixed(2);
  }
};

/**
 * 根据优惠金额计算优惠率
 */
const calcDiscountRateFromAmount = () => {
  const total = Number(salesOrder.sum) || 0;
  const discountAmount = Number(salesOrder.discount) || 0;
  if (total > 0) {
    salesOrder.discountRate = ((discountAmount / total) * 100).toFixed(2);
  } else {
    salesOrder.discountRate = "0";
  }
};

/**
 * 计算优惠后金额
 * @returns number - 优惠后金额
 */
const calcActualAmount = (): number => {
  const total = Number(salesOrder.sum) || 0;
  const discountAmount = Number(salesOrder.discount) || 0;
  const actualAmount = total - discountAmount;
  salesOrder.total_amount = actualAmount.toFixed(2);
  return actualAmount;
};

/**
 * 计算本次付款和欠款
 */
const calcPayAndDebt = () => {
  const actualAmount = Number(salesOrder.total_amount) || 0;
  let payAmount = Number(salesOrder.pay_amount);
  if (isNaN(payAmount) || !salesOrder.pay_amount || salesOrder.pay_amount === "0") {
    payAmount = actualAmount;
    salesOrder.pay_amount = actualAmount.toFixed(2);
  }
  const debt = actualAmount - payAmount;
  salesOrder.debt = debt.toFixed(2);
};

/**
 * 总控方法：计算总金额和优惠
 */
const calcTotalAndDiscount = () => {
  if (!salesOrder.items || salesOrder.items.length === 0) {
    salesOrder.sum = "0.00";
    salesOrder.discount = "0.00";
    salesOrder.discountRate = "0";
    salesOrder.total_amount = "0.00";
    salesOrder.pay_amount = "0.00";
    salesOrder.debt = "0.00";
    return;
  }
  calcTotalAmount();
  calcActualAmount();
  calcPayAndDebt();
};

/**
 * 优惠金额输入框失焦
 */
const changeDiscount = () => {
  calcDiscountRateFromAmount();
  calcTotalAndDiscount();
};

/**
 * 优惠率输入框失焦
 */
const changeDiscountRate = () => {
  calcDiscountAmountFromRate();
  calcTotalAndDiscount();
};

// 生命周期钩子
onMounted(() => {
  getCustomersList();
  eventBus.on("selectGoodsList", handleSelectGoodsList);
  nextTick(() => {
    if (uForm.value) {
      (uForm.value as any).setRules(rules);
    }
  });
});

onBeforeUnmount(() => {
  eventBus.off("selectGoodsList", handleSelectGoodsList);
});

onLoad((options: { id?: string }) => {
  if (options.id) {
    uni.setNavigationBarTitle({ title: '编辑销售订单' });
    loadSalesOrderDetail(options.id);
  } else {
    uni.setNavigationBarTitle({ title: '新增销售订单' });
  }
});
</script>


<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  flex: 1;
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;

  .sub-title {
    font-size: 24rpx;
    color: #999;
  }

  .arrow {
    transition: transform 0.3s;

    &.arrow-up {
      transform: rotate(180deg);
    }
  }
}

.form {
  padding: 0 20rpx;
}

.goods-list {
  padding: 20rpx;
}

.goods_item {
  display: flex;
  align-items: center;
  border-bottom: 2px dashed #ccc;
  background: #fff;
  margin: 10px 0;
  padding: 10px 16px;
  box-sizing: border-box;
  min-height: 80px;
  position: relative;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  background: #fff;
  padding: 10px 0;
  min-height: 60px;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price_qty {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 2px;
}

.goods_price {
  display: flex;
  align-items: center;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.goods_qty {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.qty_x {
  font-size: 14px;
  color: #666;
  margin-right: 4px;
}

.qty_control {
  display: flex;
  align-items: center;
  height: 28px;
  position: relative;
}

.qty_input {
  width: 50px;
  height: 28px;
  border: 1px solid #bbb;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  background: #fff;
  color: #222;
  margin: 0 2px;
}

i-left-one,
i-right-one {
  cursor: pointer;
  margin: 0 4px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.goods_info {
  display: flex;

  .goods_attributes {
    width: 50%;
    font-size: 24rpx;
    display: flex;
    align-content: flex-start;
    align-items: flex-start;
    flex-direction: column;

    .goods_attribute {
      display: flex;
      margin-top: 10rpx;
    }
  }
}

.goods_attribute_name {
  width: 60px;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;

  text:nth-child(1) {
    width: 80px;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
  }
}

.remark {
  padding: 20rpx;
}

.upload-area {
  padding: 20rpx;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.telescoping {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40rpx;
  background-color: #fff;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10rpx;
}

::v-deep .operation .u-button {
  height: 30px;
  flex: 1;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}

/* 结算账户输入框包装器 */
.account-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 20rpx;
}

/* 删除结算账户按钮 */
.delete-account-btn {
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  transition: all 0.2s;
  flex-shrink: 0;
  z-index: 10;
  width: 32rpx;
  height: 32rpx;
}

.delete-account-btn:active {
  background-color: rgba(209, 59, 59, 0.1);
  transform: scale(0.95);
}

.delete-account-btn:hover {
  background-color: rgba(209, 59, 59, 0.05);
}
</style>
