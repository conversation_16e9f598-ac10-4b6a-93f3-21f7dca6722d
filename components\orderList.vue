<template>
  <view class="orderList">
    <scroll-view scroll-y class="scroll-Y" @scrolltolower="loadMore" :style="{ height: scrollViewHeight }">
      <!-- 空状态显示 -->
      <EmptyState
        v-if="orderList.length === 0 && !loading"
        icon="📋"
        text="暂无订单数据"
        tip="请尝试其他搜索条件"
        :show-action="true"
        action-text="刷新"
        @action="handleRefresh"
      />

      <!-- 订单列表 -->
      <view class="order_item attribute_font" v-for="(item, index) in orderList" :key="item.id" @tap="handleItemClick" :data-index="index" :data-item-id="item.id">
        <!-- 状态标签组 -->
        <view class="status-tags-group">
          <view class="status-order-tag" :class="'status-' + getStatusType(item)">
            {{ getStatusText(item) }}
          </view>
          <view class="status-receipt-tag" :class="'receipt-' + getReceiptStatusType(item.receipt_status)" v-if="item.receipt_status">
            {{ getReceiptStatusText(item.receipt_status) }}
          </view>
        </view>

        <view class="item-content">
          <!-- 供应商/客户信息 -->
          <view class="info-row">
            <text class="label">{{ item.customer_name ? '客户' : '供应商' }}</text>
            <text class="value">：{{ item.customer_name || item.supplier_name || '-' }}</text>
          </view>

          <!-- 商品信息 -->
          <view class="info-row">
            <text class="label">商品</text>
            <text class="value">：{{ item.short_desc || getGoodsText(item.items) }}</text>
          </view>

          <!-- 操作员和数量信息 -->
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">操作员</text>
              <text class="value">：{{ item.handler_name || item.handler || '-' }}</text>
            </view>
            <view>
              <text class="sub-label">数量</text>
              <text class="value">：{{ getTotalQuantity(item.items) }}</text>
            </view>
          </view>

          <!-- 金额信息 -->
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">金额合计</text>
              <text class="value">：{{ formatAmount(item.total_amount) }}</text>
            </view>
            <view v-if="item.received_amount > 0">
              <text class="sub-label">预付款</text>
              <text class="value">：{{ formatAmount(item.received_amount) }}</text>
            </view>
          </view>
        </view>

        <!-- 底部信息：单据日期 + 分享按钮 -->
        <view class="info-bottom">
          <view class="info-row" style="margin-bottom: 0">
            <text class="label">单据日期</text>
            <text class="value">：{{ formatDateTime(item.order_date) }}</text>
          </view>
          <button class="share-btn" @click.stop="shareOrder(item)">
            分享
          </button>
        </view>
      </view>
      <!-- 加载更多 -->
      <view class="loading-more" v-if="loading">
        <u-loading-icon></u-loading-icon>
        <text>加载中...</text>
      </view>
      <view class="no-more" v-if="!hasMore && orderList.length > 0">
        <text>没有更多数据了</text>
      </view>
      <view class="empty" v-if="!loading && orderList.length === 0">
        <text>暂无数据</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import EmptyState from './EmptyState.vue';

// 定义 props
const props = defineProps({
  isOrderListRefresh: {
    type: Boolean,
    default: false,
  },
  orderResults: {
    type: [Array, Object],
    default: () => [],
  },
  type: {
    type: Number,
    default: 0,
  },
});

// 定义 emit
const emit = defineEmits(['update:isOrderListRefresh', 'orderDetail', 'refresh']);

// 响应式状态
const orderListParams = reactive({
  page: 1,
  page_size: 10,
});

const orderList = ref([]);
const loading = ref(false);
const hasMore = ref(true);
const scrollViewHeight = ref("500px");

// 监听 props 变化
watch(() => props.orderResults, (newVal) => {
  if (Array.isArray(newVal)) {
    orderList.value = newVal;
  } else if (newVal && Array.isArray(newVal.results)) {
    orderList.value = newVal.results;
    hasMore.value = (orderList.value.length < (newVal.count || 0));
  } else {
    orderList.value = [];
  }
}, { immediate: true });

watch(() => props.isOrderListRefresh, (newVal) => {
  if (newVal) {
    getOrderList();
    emit('update:isOrderListRefresh', false);
  }
}, { immediate: true });

// 生命周期钩子
onMounted(() => {
  initScrollViewHeight();
  getOrderList();
  console.log('orderList组件已挂载，当前数据:', orderList.value);
});

// 方法定义
const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const navBarHeight = 44;
    const statusBarHeight = info.statusBarHeight;
    const searchHeight = (info.screenWidth * 80) / 750;
    const inputHeight = (info.screenWidth * 90) / 750;
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};

const getOrderList = (isLoadMore = false) => {
  if (!isLoadMore) {
    orderList.value = [];
  }
  hasMore.value = false;
};

const loadMore = () => {
  if (!hasMore.value || loading.value) return;
  orderListParams.page += 1;
  getOrderList(true);
};

const handleItemClick = (event: any) => {
  console.log('=== orderList组件 - 点击事件开始 ===');
  console.log('事件对象:', event);

  const dataset = event.currentTarget.dataset;
  const index = parseInt(dataset.index);
  const itemId = dataset.itemId;

  console.log('点击的索引:', index);
  console.log('点击的订单ID:', itemId);
  console.log('当前orderList数组:', orderList.value);
  console.log('orderList长度:', orderList.value.length);

  if (index >= 0 && index < orderList.value.length) {
    const item = orderList.value[index];
    console.log('通过索引获取的item:', item);
    console.log('订单ID:', item.id);
    console.log('订单号:', item.order_id);
    console.log('客户名称:', item.customer_name);

    emit('orderDetail', item);
    console.log('=== orderList组件 - 事件已发送 ===');
  } else {
    console.error('无法获取订单数据，索引:', index, '数组长度:', orderList.value.length);
  }
};

const getStatusText = (item: any) => {
  if (item.order_status === 'pending') return '待处理';
  if (item.order_status === 'completed') return '已完成';
  if (item.order_status === 'cancelled') return '已取消';
  if (item.order_status === 'partial') return '部分完成';
  return item.status_text || '待处理';
};

const getStatusType = (item: any) => {
  if (item.order_status === 'pending') return 'primary';
  if (item.order_status === 'completed') return 'success';
  if (item.order_status === 'cancelled') return 'error';
  if (item.order_status === 'partial') return 'warning';
  return item.status_type || 'primary';
};

const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

const formatDeliveryDate = (dateStr: string) => {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const formatAmount = (amount: number) => {
  if (!amount && amount !== 0) return '0.00';
  return Number(amount).toFixed(2);
};

const getReceiptStatusText = (status: string) => {
  const statusMap = {
    'unpaid': '未付款',
    'partial': '部分付款',
    'paid': '已付款',
    'overpaid': '超额付款'
  };
  return statusMap[status] || '未知';
};

const getReceiptStatusType = (status: string) => {
  const typeMap = {
    'unpaid': 'error',
    'partial': 'warning',
    'paid': 'success',
    'overpaid': 'info'
  };
  return typeMap[status] || 'primary';
};

const getTotalQuantity = (items: any[]) => {
  if (!items || !Array.isArray(items)) return 0;
  return items.reduce((total, item) => {
    return total + (Number(item.quantity) || 0);
  }, 0);
};

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const shareOrder = (item: any) => {
  console.log('分享订单:', item);
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  });
};

const getGoodsText = (items: any[]) => {
  if (!items || !Array.isArray(items) || items.length === 0) {
    return '-';
  }
  return items.map(item => item.short_desc || item.item_name || '-').join('、');
};

const handleRefresh = () => {
  getOrderList();
  emit('refresh');
};
</script>

<style lang="scss" scoped>
// 样式部分保持不变
.orderList {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  .scroll-Y {
    flex: 1;
    padding: 20rpx;
    width: 95%;
    overflow: auto;
  }

  .order_item {
    width: 95%;
    margin: 0 auto 20rpx;
    background-color: #fff;
    border-radius: 8rpx;
    padding: 20rpx;
    position: relative;
  }

  .item-content {
    border-bottom: 1px solid #eee;
    padding-bottom: 10rpx;
    margin-bottom: 10rpx;
  }

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    flex-wrap: wrap;
  }

  .info-row:last-child {
    margin-bottom: 0;
  }

  .info-row-item {
    width: 400rpx;
    display: flex;
    align-items: center;
  }

  .label {
    color: #333;
    width: 110rpx;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
  }

  .value {
    color: #333;
    margin-right: 20rpx;
  }

  .sub-label {
    color: #333;
    width: 100rpx;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
    margin-right: 10rpx;
  }

  .info-bottom {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .share-btn {
    color: #4080e8;
    font-size: 28rpx;
    text-align: right;
    padding: 10rpx 0;
    background: none;
    border: none;
    line-height: 1;
    margin: 0;
  }

  .share-btn::after {
    border: none;
  }

  .status-tags-group {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8rpx;
  }

  .status-order-tag,
  .status-receipt-tag {
    height: 35rpx;
    line-height: 35rpx;
    border: 2rpx solid #ff4d4f;
    color: #ff4d4f;
    border-radius: 8rpx;
    font-size: 20rpx;
    background: #fff;
    font-weight: 500;
    box-sizing: border-box;
    text-align: center;
    width: 100rpx;
    padding: 0 8rpx;
    white-space: nowrap;
  }

  .status-order-tag {
    &.status-primary {
      border-color: #1890ff;
      color: #1890ff;
    }

    &.status-success {
      border-color: #52c41a;
      color: #52c41a;
    }

    &.status-warning {
      border-color: #faad14;
      color: #faad14;
    }

    &.status-error {
      border-color: #ff4d4f;
      color: #ff4d4f;
    }
  }

  .status-receipt-tag {
    &.receipt-unpaid {
      border-color: #ff4d4f;
      color: #ff4d4f;
    }

    &.receipt-partial {
      border-color: #faad14;
      color: #faad14;
    }

    &.receipt-paid {
      border-color: #52c41a;
      color: #52c41a;
    }

    &.receipt-overpaid {
      border-color: #1890ff;
      color: #1890ff;
    }
  }
  
  .loading-more,
  .no-more,
  .empty {
    text-align: center;
    padding: 20rpx;
    color: #999;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    text {
      margin-left: 10rpx;
    }
  }
}
</style>