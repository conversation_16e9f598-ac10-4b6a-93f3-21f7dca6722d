{"index.title": "Hello i18n", "index.home": "Home", "index.component": "Component", "index.api": "API", "index.schema": "<PERSON><PERSON><PERSON>", "index.demo": "uni-app globalization", "index.demo-description": "Include uni-framework, manifest.json, pages.json, ta<PERSON><PERSON>, Page, Component, API, Schema", "index.detail": "Detail", "index.language": "Language", "index.language-info": "Settings", "index.system-language": "System language", "index.application-language": "Application language", "index.language-change-confirm": "Applying this setting will restart the app"}