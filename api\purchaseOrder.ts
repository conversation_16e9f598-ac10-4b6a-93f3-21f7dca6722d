import request from '@/utils/request.ts';
import store from '@/store';

// 定义采购订单商品项接口
interface PurchaseOrderItem {
  id?: string; // 商品项ID，可选
  item: string | null; // 商品ID
  unit: string | null; // 单位ID
  item_name: string; // 商品名称
  code: string; // 商品编码
  unit_name: string; // 单位名称
  quantity: number; // 数量
  purchase_price: number; // 采购价格
  discount_rate: number; // 折扣率
  actual_purchase_price: number; // 实际采购价格
  delivered_quantity: number; // 已交付数量
  images: any[]; // 图片列表
  [key: string]: any; // 允许其他属性
}

// 定义采购订单数据接口
interface PurchaseOrderData {
  id?: string; // 订单ID，可选
  order_id?: string; // 订单编号
  supplier: string | null; // 供应商ID
  supplier_name?: string; // 供应商名称
  order_date?: string; // 订单日期
  expected_arrival_date?: string | null; // 预计到货日期
  item_count?: number; // 商品数量
  items?: PurchaseOrderItem[]; // 商品列表
  total_amount?: number; // 总金额
  total_actual_amount?: number; // 总实际金额
  handler?: string | null; // 经手人ID
  order_status?: string; // 订单状态
  payment_status?: string; // 支付状态
  discount?: number; // 折扣金额
  payment?: string | null; // 支付ID
  latest_payment?: string; // 最新支付
  deposit?: number; // 订金
  undeducted_deposit?: number; // 未抵扣订金
  remark?: string; // 备注
  payment_method?: string; // 支付方式
  pay_amount?: number; // 支付金额
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取采购订单信息
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getPurchaseorders = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/purchaseorders/`, data);
};

/**
 * 获取采购订单详情
 * @param {string} id - 采购订单ID
 * @returns {Promise<any>}
 */
export const getPurchaseordersDetail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/purchaseorders/${id}/`);
};

/**
 * 创建采购订单
 * @param {PurchaseOrderData} data - 采购订单数据
 * @returns {Promise<any>}
 */
export const createPurchaseOrder = (data: PurchaseOrderData): Promise<any> => {
  console.log(data);

  // 构建符合后端要求的请求数据结构
  const requestData: PurchaseOrderData = {
    order_id: data.order_id || '',
    supplier: data.supplier || null,
    supplier_name: data.supplier_name || '',
    order_date: data.order_date || '',
    expected_arrival_date: data.expected_arrival_date || null,
    item_count: data.items ? data.items.length : 0,
    items: data.items ? data.items.map(item => ({
      item: item.item || null,
      unit: item.unit || null,
      item_name: item.item_name || '',
      code: item.code || '',
      unit_name: item.unit_name || '',
      quantity: parseFloat(item.quantity as any) || 0,
      purchase_price: parseFloat(item.purchase_price as any) || 0,
      discount_rate: 0,
      actual_purchase_price: parseFloat(item.actual_purchase_price as any) || 0,
      delivered_quantity: 0,
      images: item.images || []
    })) : [],
    total_amount: parseFloat(data.total_amount as any) || 0,
    total_actual_amount: parseFloat(data.total_actual_amount as any) || 0,
    handler: data.handler || null,
    order_status: 'pending',
    payment_status: 'unpaid',
    discount: parseFloat(data.discount as any) || 0,
    payment: data.payment || null,
    latest_payment: '',
    deposit: parseFloat(data.deposit as any) || 0,
    undeducted_deposit: parseFloat(data.undeducted_deposit as any) || 0,
    remark: data.remark || '',
    payment_method: data.payment_method,
    pay_amount: parseFloat(data.pay_amount as any) || 0
  };

  return request.post(`/ztx/${getLedgerName()}/purchaseorders/`, requestData);
};

/**
 * 编辑采购订单
 * @param {PurchaseOrderData} data - 采购订单数据
 * @returns {Promise<any>}
 */
export const updatePurchaseOrder = (data: PurchaseOrderData): Promise<any> => {

  const requestData: PurchaseOrderData = {
    order_id: data.order_id || '',
    supplier: data.supplier || null,
    supplier_name: data.supplier_name || '',
    order_date: data.order_date || '',
    expected_arrival_date: data.expected_arrival_date || null,
    item_count: data.items ? data.items.length : 0,
    items: data.items ? data.items.map(item => {
      const itemData: PurchaseOrderItem = {
        item: item.item || null,
        unit: item.unit || null,
        item_name: item.item_name || '',
        code: item.code || '',
        unit_name: item.unit_name || '',
        quantity: parseFloat(item.quantity as any) || 0,
        purchase_price: parseFloat(item.purchase_price as any) || 0,
        discount_rate: 0,
        actual_purchase_price: parseFloat(item.actual_purchase_price as any) || 0,
        delivered_quantity: 0,
        images: item.images || []
      };

      // 只有当id不为null时才添加id字段
      if (item.id !== null && item.id !== undefined) {
        itemData.id = item.id;
      }

      return itemData;
    }) : [],
    total_amount: parseFloat(data.total_amount as any) || 0,
    total_actual_amount: parseFloat(data.total_actual_amount as any) || 0,
    handler: data.handler || null,
    order_status: 'pending',
    payment_status: 'unpaid',
    discount: parseFloat(data.discount as any) || 0,
    payment: data.payment || null,
    latest_payment: '',
    deposit: parseFloat(data.deposit as any) || 0,
    undeducted_deposit: parseFloat(data.undeducted_deposit as any) || 0,
    remark: data.remark || '',
    payment_method: data.payment_method,
    pay_amount: parseFloat(data.pay_amount as any) || 0
  };
  return request.put(`/ztx/${getLedgerName()}/purchaseorders/${data.id}/`, requestData);
};

/**
 * 删除采购订单
 * @param {object} data - 包含采购订单ID的数据
 * @returns {Promise<any>}
 */
export const deletePurchaseOrder = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/purchaseorders/${data.id}/`);
};

/**
 * 搜索采购订单
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchPurchaseorders = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/purchaseorders/search/`, data);
};
