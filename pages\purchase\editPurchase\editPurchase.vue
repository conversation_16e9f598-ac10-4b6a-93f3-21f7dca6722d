<template>
  <view class="container">
    <!-- 基本信息 -->
    <scroll-view scroll-y class="content">
      <basic-info
      :basicInfo="purchaseInfo"
      :isDisabled="isDisabled"
      @update:basicInfo="updatePurchaseInfo"
    />

      <!-- 商品清单 - 使用merchbill组件 -->
      <merch-bill :items="purchaseInfo.items" :total_actual_amount="purchaseInfo.total_actual_amount" :prohibitModification="isDisabled" @open-product-select="openProductSelect"
        @open-product-details="openProductDetails" @amount-change="handleAmountChange" />

      <!-- 结算信息 -->
      <view class="info">
        <view class="info_title">
          <text>结算信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom :required="purchaseInfo.pay_amount > 0">
              <u-row>
                <u-col :span="purchaseInfo.payment_method_name ? 10.5 : 12">
                  <u--input v-model="purchaseInfo.payment_method_name" border="none" placeholder="请选择结算账户"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector"
                    :required="purchaseInfo.pay_amount > 0"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePurchaseOrder">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="purchaseInfo.payment_method_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="支付预付款" borderBottom>
              <u--input v-model="purchaseInfo.pay_amount" border="none" placeholder="0" inputAlign="right"
                @blur="changePayAmount" :disabled="isDisabled"></u--input>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="purchaseInfo.remark" placeholder="请输入备注" autoHeight border="none"
            :disabled="isDisabled"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <imageUpload :prohibitModification="isDisabled" />
      <!-- 底部操作栏 -->
      <!-- 如果为部分到货/全部到货，即不可修改 -->
      <view class="operation" v-if="
        purchaseInfo.order_status !== 'partial' &&
        purchaseInfo.order_status !== 'completed'
      ">
        <u-button type="error" text="删除" v-if="purchaseInfo.id" @click="deletePurchaseOrder"></u-button>
        <u-button type="primary" text="保存" @click="savePurchaseOrder"></u-button>
      </view>
    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 选择搜索器-供应商 -->
    <searchSelector v-model:selectorShow="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectSupplier" @close="closePopup" @newSupplier="newSupplier"
      @supplierScrollToLower="getSupplierList" />
    <!-- 商品详细信息 -->
    <product-details v-model:productDetailsShow="productDetailsShow" :type="2" :productData="productData"
      :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing" @close="handleProductDetailsClose"
      @confirm="handleProductDetailsConfirm" />
    <!-- 日期选择器 -->
    <datePickers :showDatePicker="showDatePicker" @close="closeDatePicker" @confirm="handleDateConfirm" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue';
import { onLoad, onReady } from '@dcloudio/uni-app';
import Input from "@/components/input/input.vue";
import goodsList from "@/components/goodsList.vue";
import searchSelector from "@/components/searchSelector.vue";
import productDetails from "@/components/productDetails.vue";
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import MerchBill from "@/components/merchbill.vue";
import { getSupplier } from "@/api/supplier";
import {
  createPurchaseOrder,
  updatePurchaseOrder,
  deletePurchaseOrder,
} from "@/api/purchaseOrder";
import eventBus from "@/utils/eventBus";
import datePickers from "@/components/datePickers.vue";
import BasicInfo from '@/components/modifyComponent/basicInfo/basicInfo.vue';

const isDisabledTitle = ref('信息禁止修改');
const isDisabled = ref(false); //是否禁用
const accountSelectShow = ref(false); //结算账户弹出层
const isExpandGoodsList = ref(true);
const showDatePicker = ref(false); //日期选择器
const purchaseInfo = reactive<any>({
  supplier_name: "", // 供应商名称
  supplier: 0, // 供应商ID
  order_date: "", // 单据日期
  order_id: "", // 单据编号
  items: [], // 商品清单（数组）
  total_amount: "0", // 合计金额
  discountRate: "0", // 优惠率(%)
  discount: "0", // 优惠金额
  actual_purchase_price: "0", // 优惠后金额
  handler: "", // 经办人ID
  handler_name: "", // 经办人名称
  pay_amount: "0", // 预付款
  payment_method: "", // 付款方式
  payment_method_name: "", // 付款方式名称
  remark: "", // 备注
  attachments: [], // 附件列表
  expected_arrival_date: "", // 预计到货日期（格式化后显示用）
  debt: "0", // 本次欠款
});
const rules = reactive<any>({
  "supplier_name": {
    type: "string",
    required: true,
    message: "请选择供应商",
    trigger: ["blur", "change"],
  },
  "expected_arrival_date": {
    type: "string",
    required: true,
    message: "请选择预计到货日期",
    trigger: ["blur", "change"],
  },
});
const selectorShow = ref(false);
const productData = ref({}); //商品详情
const productDetailsShow = ref(false); //商品详情弹出层
const selectList = ref<any[]>([]); //搜索列表
const selectType = ref(0); //搜索类型
const supplierList = ref<any[]>([]); //供应商列表
const isSupplierMore = ref(true); //是否还有更多供应商
const supplierPageParams = reactive({
  page: 1,
  page_size: 20,
});
const settlementAccountList = ref<any[]>([]);
const activeQtyIndex = ref(-1);

//打开结算账户选择
const openAccountSelector = () => {
  if (isDisabled.value) {
    uni.showToast({
      title: isDisabledTitle.value,
      icon: 'none'
    })
    return;
  }
  accountSelectShow.value = true;
};
//删除结算账户
const removePurchaseOrder = () => {
  if (isDisabled.value) {
    uni.showToast({
      title: isDisabledTitle.value,
      icon: 'none'
    })
    return;
  }
  purchaseInfo.payment_method = "";
  purchaseInfo.payment_method_name = "";
};
//处理结算账户的数据
const handleAccount = (data: any) => {
  purchaseInfo.payment_method = data.id;
  purchaseInfo.payment_method_name = data.account_name;
};
//关闭结算账户选择
const closeAccountSelector = () => {
  accountSelectShow.value = false;
};
//商品清单删除商品
const handleDelBtnPricing = (data: any) => {
  purchaseInfo.items = purchaseInfo.items.filter(
    (item: any) => !(item.item === data.item && item.unit === data.unit)
  );
  productDetailsShow.value = false;
};
//删除订单
const deletePurchaseOrder = () => {
  deletePurchaseOrder(purchaseInfo).then((res: any) => {
    if (res.code == 0) {
      uni.showToast({
        title: "删除成功",
        icon: "none",
      });
      eventBus.$emit("refreshPurchaseOrders");
      uni.navigateBack();
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  });
};

//保存订单
const savePurchaseOrder = () => {
  //如果有预付款就要判断是否有结算账户
  if (
    Number(purchaseInfo.pay_amount) > 0 && !purchaseInfo.payment_method
  ) {
    uni.showToast({
      title: "请选择结算账户",
      icon: "none",
    });
    return;
  }

  if (purchaseInfo.id) {
    updatePurchaseOrder(purchaseInfo).then((res: any) => {
      if (res.code == 0) {
        uni.showToast({
          title: "保存成功",
          icon: "none",
        });
        eventBus.$emit("refreshPurchaseOrders");
        uni.navigateBack();
      } else {
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    });
  } else {
    createPurchaseOrder(purchaseInfo).then((res: any) => {
      if (res.code == 0) {
        uni.showToast({
          title: "保存成功",
          icon: "none",
        });
        eventBus.$emit("refreshPurchaseOrders");
        uni.navigateBack();
      } else {
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    });
  }
};
//选择商品
const handleSelectGoodsList = (data: any) => {
  console.log(data);

  // 创建新数组以强制更新
  const updatedItems = [...purchaseInfo.items];

  // 遍历选择的商品数组
  data.forEach((selectedItem: any) => {
    // 查找是否存在相同的商品和单位类型
    const existingIndex = updatedItems.findIndex(
      (existingItem: any) => existingItem.item === selectedItem.item && existingItem.unit === selectedItem.unit
    );

    if (existingIndex !== -1) {
      // 如果找到相同的商品和单位类型，覆盖原来的数据
      updatedItems[existingIndex] = selectedItem;
    } else {
      // 如果没有找到，新增到items的后面
      updatedItems.push(selectedItem);
    }
  });

  // 强制更新整个数组
  purchaseInfo.items = updatedItems;

  // calcTotalAndDiscount();
};

//打开日期选择器
const openDatePicker = () => {
  if (isDisabled.value) {
    uni.showToast({
      title: isDisabledTitle.value,
      icon: 'none'
    })
    return;
  }
  showDatePicker.value = true;
};
// 关闭日期选择器
const closeDatePicker = () => {
  showDatePicker.value = false;
};
// 日期选择器确认
const handleDateConfirm = (date: string) => {
  purchaseInfo.expected_arrival_date = date;
  showDatePicker.value = false;
};

//获取供应商列表
const getSupplierList = () => {
  if (!isSupplierMore.value) return;
  getSupplier(supplierPageParams).then((res: any) => {
    if (res.code == 0) {
      supplierList.value = res.data.results;
      if (supplierPageParams.page_size > res.data.results.length) {
        isSupplierMore.value = false;
      } else {
        supplierPageParams.page++;
        isSupplierMore.value = true;
      }
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  });
};
//打开搜索弹出层
const openSelector = (index: number) => {
  if (isDisabled.value) {
    uni.showToast({
      title: isDisabledTitle.value,
      icon: 'none'
    })
    return;
  }
  selectType.value = index;
  selectorShow.value = true;
  if (selectType.value === 0) {
    selectList.value = supplierList.value;
  } else {
    selectList.value = settlementAccountList.value;
  }
};
//关闭搜索弹出层
const closePopup = () => {
  selectorShow.value = false;
};
//选择供应商
const selectSupplier = (item: any) => {
  if (selectType.value === 0) {
    purchaseInfo.supplier = item.id;
    purchaseInfo.supplier_name = item.name;
  } else if (selectType.value === 1) {
    // purchaseInfo.account = item.label; // account is not defined in purchaseInfo
  }
};
//新建供应商
const newSupplier = (data: any) => {
  // list.value.unshift(data); // list is not defined
};

//打开商品详情弹出层
const openProductDetails = (item: any) => {
  console.log(item);
  if (isDisabled.value) {
    uni.showToast({
      title: isDisabledTitle.value,
      icon: 'none'
    })
    return;
  }
  productData.value = item;
  productDetailsShow.value = true;
};
//关闭商品详情弹出层
const handleProductDetailsClose = () => {
  productDetailsShow.value = false;
};
//商品详情弹出层确认
const handleProductDetailsConfirm = (item: any) => {
  productData.value = item;

  console.log(item);
  console.log(purchaseInfo.items);

  // 使用 findIndex 查找相同商品和单位类型的元素索引
  const index = purchaseInfo.items.findIndex(
    (existingItem: any) => existingItem.item === item.item && existingItem.unit === item.unit
  );

  if (index !== -1) {
    // 使用 splice 方法来替换元素，确保响应式更新
    purchaseInfo.items.splice(index, 1, item);
  }

  // calcTotalAndDiscount();
  productDetailsShow.value = false;
};
//跳转选择商品页面
const openProductSelect = () => {
  uni.navigateTo({
    url: "/components/productSelection" + "?type=2",
  });
};

// 5. 计算本次付款和欠款
const calcPayAndDebt = () => {
  const actualAmount = Number(purchaseInfo.actual_purchase_price) || 0;
  let payAmount = Number(purchaseInfo.pay_amount);
  if (
    isNaN(payAmount) ||
    !purchaseInfo.pay_amount ||
    purchaseInfo.pay_amount === "0"
  ) {
    payAmount = actualAmount;
    purchaseInfo.pay_amount = actualAmount.toFixed(2);
  }
  const debt = actualAmount - payAmount;
  purchaseInfo.debt = debt.toFixed(2);
};

// 处理金额变化
const handleAmountChange = (amountData: any) => {
  purchaseInfo.total_amount = amountData.totalAmount;
  purchaseInfo.discount = amountData.discount;
  purchaseInfo.actual_purchase_price = amountData.actualPurchasePrice;
  // 重新计算付款和欠款
  calcPayAndDebt();
};

watch(() => purchaseInfo.items, () => {
  // calcTotalAndDiscount();
}, { deep: true, immediate: true });

watch(() => purchaseInfo.discountRate, (newVal) => {
  if (newVal > 100) {
    uni.showToast({
      title: "优惠率不能大于100",
      icon: "none",
    });
    purchaseInfo.discountRate = 100;
    return;
  }
});

onMounted(() => {
  getSupplierList();
  eventBus.$on("selectGoodsList", handleSelectGoodsList);
});

onBeforeUnmount(() => {
  eventBus.$off("selectGoodsList", handleSelectGoodsList);
});

onReady(() => {
  // 微信小程序需要用此写法
  // uForm.value.setRules(rules); // uForm is not defined
  // this.$refs.datetimePicker.setFormatter(this.formatter); // datetimePicker is not defined
});

onLoad((options: any) => {
  console.log(options);
  
  if (options.data) {
    try {
      const item = JSON.parse(decodeURIComponent(options.data));
      // 这里可以根据实际结构做深拷贝或字段映射
      Object.assign(purchaseInfo, {
        ...item,
      });
      purchaseInfo.actual_purchase_price = item.total_actual_amount;

        if (item.order_status == 'partial' || item.order_status == 'completed') {
          this.isDisabled = true;
        }
      } catch (e) {
        console.error("解析采购单数据失败", e);
      } finally {
        // 显示加载完成并停留1秒后关闭
        uni.showToast({
          title: '加载完成',
          icon: 'success',
          duration: 1000
        });
        setTimeout(() => {
          uni.hideLoading();
        }, 1000);
      }
    } else {
      // 显示加载完成并停留1秒后关闭
      uni.showToast({
        title: '加载完成',
        icon: 'success',
        duration: 1000
      });
      setTimeout(() => {
        uni.hideLoading();
      }, 1000);
    }

  if (options) {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    purchaseInfo.order_date = `${year}-${month}-${day}`;
  }
});

/**
 * 更新采购订单信息
 * @param val - 新的采购订单信息
 */
const updatePurchaseInfo = (val: any): void => {
  Object.assign(purchaseInfo, val);
};

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  flex: 1;
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;

  .sub-title {
    font-size: 24rpx;
    color: #999;
  }

  .arrow {
    transition: transform 0.3s;

    &.arrow-up {
      transform: rotate(180deg);
    }
  }
}

.form {
  padding: 0 20rpx;
}

.goods-list {
  padding: 20rpx;
}

.goods_item {
  border-bottom: 2px dashed #ccc;
  background: #fff;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  position: relative;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  background: #fff;
  padding: 10px 0;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}



.qty_control {
  display: flex;
  align-items: center;
  height: 24px;
  position: relative;
}

.qty_input {
  width: 60px;
  height: 28px;
  border: 1px solid #bbb;
  border-radius: 6px;
  text-align: center;
  font-size: 18px;
  background: #fff;
  color: #222;
  margin: 0 4px;
}

i-left-one,
i-right-one {
  cursor: pointer;
  margin: 0 4px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.goods_info {
  display: flex;

  .goods_attributes {
    width: 50%;
    font-size: 24rpx;
    display: flex;
    align-content: flex-start;
    align-items: flex-start;
    flex-direction: column;

    .goods_attribute {
      display: flex;
      margin-top: 10rpx;
    }
  }
}

.goods_attribute_name {
  width: 60px;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;

  text:nth-child(1) {
    width: 80px;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
  }
}

.remark {
  padding: 20rpx;
}

.upload-area {
  padding: 20rpx;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.telescoping {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40rpx;
  background-color: #fff;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}
</style>
