<template>
  <view class="info">
    <view class="info_title">
      <text class="info-title-text">商品清单</text>
      <view class="addGoods">
        <view class="addAssociationGoods" v-if="(type === 'purchaseGoods' || type === 'wholesale') && !prohibitModification">
          <u-button type="primary" plain v-if="purchaseOrderName" @click="emit('add-association-goods')">+
            关联物品</u-button>
        </view>
        <view class="add-icon" @click="handleAddGoods" v-if="!prohibitModification">
          <i-add-one theme="outline" size="20" :fill="getIconColor()" />
        </view>
      </view>
    </view>
    <view class="goods-list">
      <view v-if="items && items.length > 0">
        <view class="goods_item"
              :class="{ 'last-item': isExpandGoodsList || index === items.length - 1 }"
              v-for="(item, index) in isExpandGoodsList ? items.slice(0, 1) : items"
              :key="index">
          <view class="goods_item_click">
            <!-- 商品图片 -->
            <view class="goods_img" @click="emit('open-product-details', item)">
              <image class="goods-image" :src="item.image || '/static/img/logo.png'" mode=""></image>
            </view>
            <!-- 商品主信息 -->
            <view class="goods_main" @click="emit('open-product-details', item)">
              <view class="purchasetTitleContent" v-if="type === 'purchaseGoods' || type === 'wholesale'">
                <view class="purchasetTitle" v-if="type === 'wholesale' && item.sales_order_item === true">订</view>
                <view class="goods_name">{{ item.item_name }}</view>
              </view>
              <view class="goods_name" v-else>{{ item.item_name }}</view>
              <!-- 编号/条码 -->
              <view class="goods_id">{{ item.code || item.barcode }}</view>
              <!-- 原数量、已入库  -->
              <view class="goods_extra" v-if="type == 'purchaseGoods' && item.originalQuantity && !prohibitModification">
                原数量: {{ item.originalQuantity }} 已入库:{{ item.delivered_quantity }}
              </view>
              <!-- 零售订单额外信息 -->
              <view class="goods_extra" v-if="type === 'retail'">
                库存余量: {{ item.total_stock || 0 }}
                <view class="stock-tag" v-if="item.remaining_stock && item.remaining_stock > 0">
                {{ item.total_stock }} - {{ item.remaining_stock }}
                </view>
              </view>
              <!-- 批发订单额外信息 -->
              <view class="goods_extra" v-if="type === 'wholesale'">
                库存余量: {{ item.total_stock || 0 }}
                <view class="stock-tag" v-if="item.remaining_stock && item.remaining_stock > 0">
                {{ item.total_stock }} - {{ item.remaining_stock }}
                </view>
              </view>
              <!-- 退货订单额外信息 -->
              <view class="goods_extra" v-if="type == 'returnGoodsOrder' && !prohibitModification">
                原数量: {{ item.stock_quantity || item.originalQuantity }}　 库存余量:
                {{ item.stock_remaining || item.remaining_quantity || item.total_stock }}
              </view>
              <!-- 零售退货订单额外信息 -->
              <view class="goods_extra" v-if="type == 'returnRetail' && !prohibitModification">
                原数量: {{ item.stock_quantity || item.originalQuantity }}　 已退货:{{ item.return_quantity }}
              </view>
              <view class="goods_price">
                <text class="price">￥{{ getItemPrice(item) }}</text>
                <text class="unit">/{{ item.unit_name }}</text>
              </view>
            </view>
            <!-- 数量加减 -->
           <view class="goods_qty">
              <text class="qty_x" v-if="activeQtyIndex !== index">x </text>
              <view class="qty_control">
                <view v-if="activeQtyIndex === index && !prohibitModification" @click.stop="decreaseQty(index, item)">
                  <i-left-one theme="filled" size="20" fill="#333" />
                </view>
                <u--input class="qty_input" :value="formatNumber(item.quantity)" inputAlign="center"
                  @change="(data) => changeQty(item, data)" @tap="showQtyArrows(index, item)"
                  :disabled="prohibitModification" />
                <view v-if="activeQtyIndex === index && !prohibitModification" @click.stop="increaseQty(index, item)">
                  <i-right-one theme="filled" size="20" fill="#333" />
                </view>
              </view>
            </view>

            <view class="arrow" @click="emit('open-product-details', item)">
              <i-right theme="outline" size="24" fill="#333" />
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 合计信息 -->
    <view class="total-info" v-if="!isExpandGoodsList">
      <view class="total-row">
        <text class="total-row-text">{{ getTotalLabel() }}</text>
        <u--input v-model="totalAmount" placeholder="请输入合计" inputAlign="right" border="none"
          :disabled="type === 'purchaseGoods' || type === 'returnRetail'" disabledColor="color: #fff"></u--input>
      </view>
      <view class="total-row" v-if="showDiscountFields()">
        <text>优惠率(%)</text>
        <u--input v-model="discountRate" placeholder="请输入优惠率" inputAlign="right" border="none"
          @blur="changeDiscountRateInput" @input="changeDiscountRateInput"
          :disabled="(type === 'returnGoods' || type === 'wholesale') ? false : prohibitModification"
          disabledColor="color: #fff"></u--input>
      </view>
      <view class="total-row" v-if="showDiscountFields()">
        <text>{{ getDiscountLabel() }}</text>
        <u--input v-model="discount" placeholder="请输入优惠金额" inputAlign="right" border="none"
          @blur="changeDiscountInput" @input="changeDiscountInput"
          :disabled="(type === 'returnGoods' || type === 'wholesale') ? false : prohibitModification"
          disabledColor="color: #fff"></u--input>
      </view>
      <view class="total-row" v-if="showActualAmountField()">
        <text>{{ getActualAmountLabel() }}</text>
        <u--input v-model="actualAmount" placeholder="请输入实际金额" inputAlign="right"
          border="none" disabled disabledColor="#fff"></u--input>
      </view>
    </view>
    <!-- 伸缩图标 -->
    <view class="telescoping" @click="toggleExpand">
      <i-down theme="outline" size="24" fill="#333" v-if="isExpandGoodsList" />
      <i-up theme="outline" size="24" fill="#333" v-else />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { formatNumber } from "@/utils/digitalConversion";

/**
 * 商品项接口定义
 */
interface MerchItem {
  item?: string;
  unit?: string;
  image?: string;
  item_name: string;
  code?: string;
  barcode?: string;
  originalQuantity?: number;
  delivered_quantity?: number;
  total_stock?: number;
  remaining_stock?: number;
  stock_quantity?: number;
  stock_remaining?: number;
  remaining_quantity?: number;
  return_quantity?: number;
  purchase_price?: number;
  price?: number;
  unit_name?: string;
  quantity: number;
  conversion_rate?: number;
  purchase_order_item?: boolean;
  sales_order_item?: boolean;
}

/**
 * 金额变更事件数据接口
 */
interface AmountChangeData {
  totalAmount: string;
  discount: number;
  discountRate: string;
  actualAmount: string;
}

/**
 * 定义组件属性
 */
const props = defineProps({
  items: {
    type: Array as () => MerchItem[],
    default: () => []
  },
  total_actual_amount: {
    type: String,
    default: '0'
  },
  type: {
    type: String,
    default: 'default'
  },
  prohibitModification: {
    type: Boolean,
    default: false
  },
  purchaseOrderName: {
    type: String,
    default: ''
  },
  isView: {
    type: Boolean,
    default: false
  },
  retailInCode: {
    type: String,
    default: ''
  }
});

/**
 * 定义组件事件
 */
const emit = defineEmits<{
  'add-association-goods': [];
  'open-product-details': [item: MerchItem];
  'amount-change': [data: AmountChangeData];
  'add-goods': [type?: number];
  'open-product-select': [];
  'update-item': [index: number, item: MerchItem];
  'update-items': [items: MerchItem[]];
}>();

/**
 * 响应式数据
 */
const isExpandGoodsList = ref<boolean>(false);
const activeQtyIndex = ref<number>(-1);
const totalAmount = ref<string>('0');
const discount = ref<number>(0);
const discountRate = ref<string>('0');
const actualAmount = ref<string>('0');

// ==================== 核心计算逻辑 ====================

/**
 * 计算总金额和优惠相关数据
 */
const calcTotalAndDiscount = (): void => {
  calculateItemsTotal();
  calculateDiscountFromRate();
  calculateActualAmount();
  emitCalculationResult();
};

/**
 * 计算商品总金额
 */
const calculateItemsTotal = (): void => {
  let total: number = 0;

  if (Array.isArray(props.items) && props.items.length > 0) {
    props.items.forEach((item: MerchItem) => {
      const price: number = getItemPriceNumber(item);
      const quantity: number = Number(item.quantity || 0);
      total += price * quantity;
    });
  }

  totalAmount.value = total.toFixed(2);
};

/**
 * 根据优惠率计算优惠金额
 */
const calculateDiscountFromRate = (): void => {
  const total: number = Number(totalAmount.value) || 0;
  const rate: number = Number(discountRate.value) || 0;

  discount.value = Number(((total * rate) / 100).toFixed(2));
};

/**
 * 计算优惠后的实际金额
 */
const calculateActualAmount = (): void => {
  const total: number = Number(totalAmount.value) || 0;
  const discountVal: number = Number(discount.value) || 0;

  actualAmount.value = (total - discountVal).toFixed(2);
};

/**
 * 向父组件发送计算结果
 */
const emitCalculationResult = (): void => {
  const result: AmountChangeData = {
    totalAmount: totalAmount.value,
    discount: discount.value,
    discountRate: discountRate.value,
    actualAmount: actualAmount.value
  };

  emit('amount-change', result);
};

// ==================== 输入框事件处理 ====================

/**
 * 优惠金额输入框失焦事件
 */
const changeDiscountInput = (): void => {
  const total: number = Number(totalAmount.value) || 0;
  const discountVal: number = Number(discount.value) || 0;

  if (discountVal > total) {
    uni.showToast({ title: '优惠金额不能大于总金额', icon: "none" });
    discount.value = Number(total.toFixed(2));
  }

  calculateDiscountRate();
  calculateActualAmount();
  emitCalculationResult();
};

/**
 * 优惠率输入框失焦事件
 */
const changeDiscountRateInput = (): void => {
  const rate: number = Number(discountRate.value) || 0;

  if (rate > 100) {
    uni.showToast({ title: '优惠率不能大于100', icon: "none" });
    discountRate.value = '100';
  }

  calculateDiscountFromRate();
  calculateActualAmount();
  emitCalculationResult();
};

/**
 * 根据优惠金额计算优惠率
 */
const calculateDiscountRate = (): void => {
  const total: number = Number(totalAmount.value) || 0;
  const discountVal: number = Number(discount.value) || 0;

  if (total > 0) {
    discountRate.value = ((discountVal / total) * 100).toFixed(2);
  } else {
    discountRate.value = "0";
  }
};

// ==================== 工具方法 ====================

/**
 * 获取商品价格（数字格式）
 */
const getItemPriceNumber = (item: MerchItem): number => {
  return Number(item.purchase_price) || Number(item.price) || 0;
};

/**
 * 获取商品价格（格式化字符串）
 */
const getItemPrice = (item: MerchItem): string => {
  const price = getItemPriceNumber(item);
  return price.toFixed(2);
};

/**
 * 获取图标颜色
 */
const getIconColor = (): string => {
  return props.prohibitModification ? '#999999' : '#3894FF';
};

/**
 * 获取总计金额标签文本
 */
const getTotalLabel = (): string => {
  if (props.type === 'purchaseGoods') return '总计金额';
  if (props.type === 'retail') return '单据金额';
  if (props.type === 'returnRetail') return '合计';
  if (props.type === 'returnGoods') return '退货金额';
  return '合计金额';
};

/**
 * 获取优惠标签文本
 */
const getDiscountLabel = (): string => {
  if (props.type === 'returnRetail') return '退款优惠';
  if (props.type === 'returnGoods') return '退货优惠';
  return '优惠金额';
};

/**
 * 获取实际金额标签文本
 */
const getActualAmountLabel = (): string => {
  if (props.type === 'returnRetail') return '优惠后金额';
  if (props.type === 'returnGoods') return '实退金额';
  return '实际金额';
};

/**
 * 是否显示优惠字段
 */
const showDiscountFields = (): boolean => {
  return props.type === 'retail' || props.type === 'returnRetail' || props.type === 'wholesale';
};

/**
 * 是否显示实际金额字段
 */
const showActualAmountField = (): boolean => {
  return props.type === 'retail' || props.type === 'returnRetail' || props.type === 'wholesale';
};

/**
 * 处理添加商品事件
 */
const handleAddGoods = (): void => {
  if (props.type === 'retail') {
    emit('add-goods');
  } else if (props.type === 'returnRetail') {
    emit('add-goods', 1);
  } else {
    emit('open-product-select');
  }
};

/**
 * 切换商品列表展开/收起状态
 */
const toggleExpand = (): void => {
  isExpandGoodsList.value = !isExpandGoodsList.value;
};

// ==================== 数量操作相关方法 ====================

/**
 * 显示数量加减箭头
 */
const showQtyArrows = (index: number, _item: MerchItem): void => {
  if (props.prohibitModification) {
    uni.showToast({
      title: "当前为已提交状态，不允许修改",
      icon: "none"
    });
    return;
  }

  activeQtyIndex.value = index;
};

/**
 * 修改商品数量
 */
const changeQty = (item: MerchItem, data: any): void => {
  const quantity = Number(data);

  if (props.type === 'retail' || props.type === 'wholesale') {
    changeQuantityForRetail(item, quantity);
  } else if (props.type === 'returnGoods') {
    changeQuantityForReturnGoods(item, quantity);
  } else {
    changeQuantityDefault(item, quantity);
  }
};

/**
 * 零售/批发订单数量修改
 */
const changeQuantityForRetail = (item: MerchItem, val: number): void => {
  const currentItemIndex = props.items.findIndex(
    (infoItem) => infoItem.code === item.code && infoItem.unit === item.unit
  );
  if (currentItemIndex === -1) return;

  const currentItem = props.items[currentItemIndex];

  // 验证库存限制
  if (val * Number(currentItem.conversion_rate || 1) > Number(currentItem.total_stock || 0)) {
    uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
    val = Math.floor(Number(currentItem.total_stock || 0) / Number(currentItem.conversion_rate || 1));
  }

  currentItem.quantity = val;

  // 计算库存使用量
  let totalUsedStock = 0;
  props.items.forEach(item => {
    if (item.code === currentItem.code) {
      totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
    }
  });

  props.items.forEach(item => {
    if (item.code === currentItem.code) {
      item.remaining_stock = totalUsedStock;
    }
  });

  calcTotalAndDiscount();
};

/**
 * 退货订单数量修改
 */
const changeQuantityForReturnGoods = (item: MerchItem, val: number): void => {
  const itemIdentifier = item.item || item.code;

  const targetItem = props.items.find(
    (infoItem) => {
      const infoItemIdentifier = infoItem.item || infoItem.code;
      return infoItemIdentifier === itemIdentifier && infoItem.unit === item.unit;
    }
  );

  if (!targetItem) return;

  if (val > Number(item.remaining_quantity || 0)) {
    uni.showToast({
      title: "退货数量大于在库数量",
      icon: "none",
      mask: true,
    });
    targetItem.quantity = Number(item.remaining_quantity || 0);
    calcTotalAndDiscount();
    return;
  }

  targetItem.quantity = val;
  calcTotalAndDiscount();
};

/**
 * 默认数量修改
 */
const changeQuantityDefault = (item: MerchItem, data: number): void => {
  const targetIndex = props.items.findIndex(
    (infoItem) => infoItem.item === item.item && infoItem.unit === item.unit
  );

  if (targetIndex === -1) return;

  props.items[targetIndex].quantity = data;
  calcTotalAndDiscount();
};

/**
 * 减少商品数量
 */
const decreaseQty = (index: number, item: MerchItem): void => {
  if (props.prohibitModification) return;
  if (props.type === 'retail' && props.isView) return;
  if (props.type === 'wholesale' && props.isView) return;

  if (props.type === 'retail' || props.type === 'wholesale') {
    decreaseQtyForRetail(index, item);
  } else if (props.type === 'returnGoods') {
    decreaseQtyForReturnGoods(index, item);
  } else {
    decreaseQtyDefault(index, item);
  }
};

/**
 * 零售/批发订单减少数量
 */
const decreaseQtyForRetail = (index: number, _item: MerchItem): void => {
  if (!props.items || !props.items[index]) return;
  const currentItem = props.items[index];

  if (currentItem.quantity > 1) {
    currentItem.quantity = Number(currentItem.quantity) - 1;

    // 重新计算库存使用量
    let totalUsedStock = 0;
    props.items.forEach(item => {
      if (item.code === currentItem.code) {
        totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
      }
    });

    props.items.forEach(item => {
      if (item.code === currentItem.code) {
        item.remaining_stock = totalUsedStock;
      }
    });

    calcTotalAndDiscount();
  } else {
    uni.showToast({
      title: "数量不能小于1",
      icon: "none",
    });
  }
};

/**
 * 退货订单减少数量
 */
const decreaseQtyForReturnGoods = (_index: number, item: MerchItem): void => {
  if (item && item.quantity > 1) {
    const itemIdentifier = item.item || item.code;

    props.items.forEach((infoItem) => {
      const infoItemIdentifier = infoItem.item || infoItem.code;
      if (infoItemIdentifier === itemIdentifier && infoItem.unit === item.unit) {
        infoItem.quantity = Number(infoItem.quantity) - 1;
      }
    });

    calcTotalAndDiscount();
  } else {
    uni.showToast({
      title: "数量不能小于1",
      icon: "none",
      mask: true,
    });
  }
};

/**
 * 默认减少数量
 */
const decreaseQtyDefault = (index: number, _item: MerchItem): void => {
  const targetItem = props.items[index];
  if (targetItem && targetItem.quantity > 1) {
    targetItem.quantity = Number(targetItem.quantity) - 1;
    calcTotalAndDiscount();
  } else {
    uni.showToast({
      title: "数量不能小于1",
      icon: "none",
    });
  }
};

/**
 * 增加商品数量
 */
const increaseQty = (index: number, item: MerchItem): void => {
  if (props.prohibitModification) return;
  if (props.type === 'retail' && props.isView) return;
  if (props.type === 'wholesale' && props.isView) return;

  if (props.type === 'retail' || props.type === 'wholesale') {
    increaseQtyForRetail(index, item);
  } else if (props.type === 'returnGoods') {
    increaseQtyForReturnGoods(index, item);
  } else {
    increaseQtyDefault(index, item);
  }
};

/**
 * 零售/批发订单增加数量
 */
const increaseQtyForRetail = (index: number, _item: MerchItem): void => {
  if (!props.items || !props.items[index]) return;
  const currentItem = props.items[index];

  // 验证库存限制
  if (Number(currentItem.quantity) >= Number(currentItem.stock_remaining || 0)) {
    uni.showToast({ title: "数量大于库存余量", icon: "none", mask: true });
    return;
  } else if (
    Number(currentItem.quantity * (currentItem.conversion_rate || 1)) >= Number(currentItem.total_stock || 0)
  ) {
    uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
    currentItem.quantity = Number(currentItem.total_stock || 0) / Number(currentItem.conversion_rate || 1);
  } else {
    currentItem.quantity = Number(currentItem.quantity || 0) + 1;
  }

  // 重新计算库存使用量
  let totalUsedStock = 0;
  props.items.forEach(item => {
    if (item.code === currentItem.code) {
      totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
    }
  });

  props.items.forEach(item => {
    if (item.code === currentItem.code) {
      item.remaining_stock = totalUsedStock;
    }
  });

  calcTotalAndDiscount();
};

/**
 * 退货订单增加数量
 */
const increaseQtyForReturnGoods = (_index: number, item: MerchItem): void => {
  if (item) {
    const itemIdentifier = item.item || item.code;

    props.items.forEach((infoItem) => {
      const infoItemIdentifier = infoItem.item || infoItem.code;
      if (infoItemIdentifier === itemIdentifier && infoItem.unit === item.unit) {
        if (Number(infoItem.quantity) >= Number(infoItem.remaining_quantity || 0)) {
          uni.showToast({
            title: "退货数量大于在库数量",
            icon: "none",
            mask: true,
          });
          return;
        } else {
          infoItem.quantity = Number(infoItem.quantity || 0) + 1;
        }
      }
    });
    calcTotalAndDiscount();
  }
};

/**
 * 默认增加数量
 */
const increaseQtyDefault = (index: number, _item: MerchItem): void => {
  const targetItem = props.items[index];
  if (targetItem) {
    targetItem.quantity = Number(targetItem.quantity || 0) + 1;
    calcTotalAndDiscount();
  }
};

// ==================== 监听器 ====================

/**
 * 监听 items 属性变化，重新计算总金额和优惠
 */
watch(() => props.items, (newVal: MerchItem[]) => {
  console.log('merchbill items changed, 数量:', newVal?.length);
  if (newVal && newVal.length > 0) {
    console.log('第一个商品数据:', newVal[0]);
  }
  isExpandGoodsList.value = false; // 确保商品列表展开显示
  calcTotalAndDiscount();
}, { deep: true, immediate: true });

/**
 * 监听 total_actual_amount 属性变化
 */
watch(() => props.total_actual_amount, (newVal: string) => {
  console.log('total_actual_amount changed:', newVal);
  actualAmount.value = newVal;
});
</script>

<style lang="scss" scoped>
.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;

  .sub-title {
    font-size: 24rpx;
    color: #999;
  }

  .arrow {
    transition: transform 0.3s;

    &.arrow-up {
      transform: rotate(180deg);
    }
  }
}

.form {
  padding: 0 20rpx;
}

.goods_item {
  border-bottom: 2px dashed #ccc;
  background: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  position: relative;
}

.goods_item.last-item {
  border-bottom: none;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  background: #fff;
  padding: 10px 0;
  height: 200rpx;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.qty_control {
  display: flex;
  align-items: center;
  height: 24px;
  position: relative;
}

.qty_input {
  width: 60px;
  height: 28px;
  border: 1px solid #bbb;
  border-radius: 6px;
  text-align: center;
  font-size: 18px;
  background: #fff;
  color: #222;
  margin: 0 4px;
}

i-left-one,
i-right-one {
  cursor: pointer;
  margin: 0 4px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.goods_info {
  display: flex;

  .goods_attributes {
    width: 50%;
    font-size: 24rpx;
    display: flex;
    align-content: flex-start;
    align-items: flex-start;
    flex-direction: column;

    .goods_attribute {
      display: flex;
      margin-top: 10rpx;
    }
  }
}

.goods_attribute_name {
  width: 60px;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;

  .total-row-text{
    margin-left: 45rpx;
  }

  .total-row-text:nth-child(1) {
    width: 80px;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
  }
}

.telescoping {
  display: flex;
  justify-content: center;
  align-items: center;
}

.purchasetTitleContent {
  display: flex;
  align-items: center;
}

.purchasetTitle {
  width: 40rpx;
  height: 40rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
  background-color: #72c2e1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}

.goods_extra {
  font-size: 12px;
  color: #888;
  margin-bottom: 2px;
}

.addGoods {
  display: flex;
  align-items: center;

  .addAssociationGoods {
    margin-right: 20rpx;
  }
}

.stock-tag {
  display: inline-block;
  border: 1px solid #ffa940;
  color: #ffa940;
  border-radius: 12px;
  font-size: 20rpx;
  padding: 0 12rpx;
  margin-left: 10rpx;
  line-height: 24rpx;
  height: 24rpx;
  vertical-align: middle;
}
</style>