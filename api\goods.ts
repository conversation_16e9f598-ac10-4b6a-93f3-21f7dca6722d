import request from '@/utils/request';
import http from "@/utils/http";
import store from '@/store';
import pageDataManager, { PageDataManagerCallbacks } from '@/utils/pagedata';

// 定义商品数据接口
interface GoodsData {
  id?: string; // 商品ID，可选，因为新增时可能没有
  name: string; // 商品名称
  item_type_name?: string; // 物品类型名称
  code?: string; // 物品编号
  serial_number?: string; // 序列号
  color?: string; // 颜色
  brand?: string; // 品牌
  model?: string; // 型号
  specification?: string; // 规格
  base_weight?: number; // 基础重量(kg)
  expiry_days?: number; // 有效期/保质期（天）
  location?: string; // 仓位货架
  manufacturer?: string; // 制造商
  units?: any[]; // 单位和价格信息
  stocks?: any[]; // 库存信息
  remark?: string; // 备注
  images?: any[]; // 图片列表
  is_active?: boolean; // 是否有效
  total_stock?: number; // 库存总量
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取商品分页列表
 * @param {object} param - 查询参数
 * @param {PageDataManagerCallbacks} cbs - 回调函数
 * @returns {ReturnType<typeof pageDataManager>}
 */
export const getGoodsPages = (param: object, cbs: PageDataManagerCallbacks = {}) => {
  return pageDataManager(`/ztx/${getLedgerName()}/items/`, 1, 20, param, cbs);
};

/**
 * 获取商品信息
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getGoods = async (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/items/`, data);
};

/**
 * 获取商品详细信息
 * @param {string} id - 商品ID
 * @returns {Promise<any>}
 */
export const getGoodsDetail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/items/${id}/`);
};

/**
 * 新建商品
 * @param {GoodsData} data - 商品数据
 * @returns {Promise<any>}
 */
export const saveGoods = (data: GoodsData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/items/`, data);
};

/**
 * 修改商品
 * @param {GoodsData} data - 商品数据
 * @returns {Promise<any>}
 */
export const updateGoods = (data: GoodsData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/items/${data.id}/`, data);
};

/**
 * 删除商品
 * @param {object} data - 包含商品ID的数据
 * @returns {Promise<any>}
 */
export const deleteGoods = (data: { id: string }): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/items/${data.id}/`);
};

/**
 * 搜索商品
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchGoods = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/items/search/`, data);
};
