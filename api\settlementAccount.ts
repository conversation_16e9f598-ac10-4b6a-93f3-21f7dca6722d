import request from '@/utils/request.ts';
import store from '@/store';

// 定义支付方式数据接口
interface PaymethodData {
  id?: string; // 支付方式ID，可选，因为新增时可能没有
  name: string; // 支付方式名称
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取支付方式信息
 * @returns {Promise<any>}
 */
export const getPaymethods = (): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/paymethods/`);
};

/**
 * 添加支付方式信息
 * @param {PaymethodData} data - 支付方式数据
 * @returns {Promise<any>}
 */
export const addPaymethods = (data: PaymethodData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/paymethods/`, data);
};
