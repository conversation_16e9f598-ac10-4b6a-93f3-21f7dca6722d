<template>
  <view class="a-icon-svg" :style="[boxStyle]">
    <web-view v-if="isShow" class="__wh-0" :id="_uid" @message="changeMessage"
      src="/uni_modules/icon-park/hybrid/html/index.html" />
    <image :src="png"  class="a-icon-pack-image" :style="[boxStyle]" :mode="mode" :fade-show="fadeShow"
      :lazy-load="lazyLoad" :show-menu-by-longpress="showMenuByLongpress" :draggable="draggable" @error="changeError"
      @load="changeLoad" />
  </view>
</template>

<script>
  export default {
    data() {
      return {
        _uid: this.uuid(32) as string,
        png: "" as any | null,
        webviewContext: null as WebviewContext | null,
        isShow: true as boolean,
        rtl: false,
        colors1: {
          outline: {
            fill: '#333',
            background: 'transparent'
          },
          filled: {
            fill: '#333',
            background: '#FFF'
          },
          twoTone: {
            fill: '#333',
            twoTone: '#2F88FF'
          },
          multiColor: {
            outStrokeColor: '#333',
            outFillColor: '#2F88FF',
            innerStrokeColor: '#FFF',
            innerFillColor: '#43CCF8'
          }
        },
        prefix: 'i'

      }
    },
    props: {
      size: {
        type: String,
        default: '24',
        desc: '图标的大小，即宽高的值'
      },
      strokeWidth: {
        type: String,
        default: '4',
        desc: '线条宽度'
      },
      theme: {
        type: String,
        default: 'outline',
        validator(value : String) : boolean {
          return ['outline', 'filled', 'two-tone', 'multi-color'].includes(
            value
          )
        }
      },
      strokeLinecap: {
        type: String,
        default: 'round',
        validator(value : String) : boolean {
          return ['butt', 'round', 'square'].includes(value)
        },
        desc: '描边端点类型'
      },
      strokeLinejoin: {
        type: String,
        default: 'round',
        validator(value : String) : boolean {
          return ['miter', 'round', 'bevel'].includes(value)
        },
        desc: '描边连接类型'
      },
      fill: {
        type:  Array as PropType<string[]>,
        default: ['#000000']
      },
      width: {
        type: String,
        default: "48"
      },
      height: {
        type: String,
        default: "48"
      },
      mode: {
        type: String,
        default: "scaleToFill"
      },
      fadeShow: {
        type: Boolean,
        default: false
      },
      lazyLoad: {
        type: Boolean,
        default: false
      },
      showMenuByLongpress: {
        type: Boolean,
        default: false
      },
      draggable: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      fillColor() : string[] {
        let res: string[] =  this.fill
        return res
      },
      outStrokeColor() : string {
        return typeof this.fillColor[0] === 'string'
          ? this.fillColor[0]
          : 'currentColor'
      },
      outFillColor() : string {
        return typeof this.fillColor[1] === 'string'
          ? this.fillColor[1]
          : 'currentColor'
      },
      innerStrokeColor() : string {
        return typeof this.fillColor[2] === 'string'
          ? this.fillColor[2]
          : '#FFF'
      },
      innerFillColor() : string {
        return typeof this.fillColor[3] === 'string'
          ? this.fillColor[3]
          : '#43CCF8'
      },
      sizeWithUnit() : string {
        let size = this.size + ''
        if (/d$/.test(this.size)) {
          size = this.size + 'px'
        } else if (size.endsWith('rpx')) {
          size = uni.rpx2px(parseInt(size)) + 'px'
        }
        return size
      },
      colors() : string[] {
        let res: string[] = []
        if(this.theme === 'outline') {
          res = this.outline()
        };
        if(this.theme === 'filled') {
          res = this.filled()
        };
        if(this.theme === 'two-tone') {
          res = this.twoTone()
        };
        if(this.theme === 'multi-color') {
          res = this.multi()
        };
        
        return res;
      },
      boxStyle() : UTSJSONObject {
        const style = {
          width: this.sizeWithUnit,
          height: this.sizeWithUnit
        }
        return style
      },
    },
    methods: {
      multi(): string[] {
        return [
          this.outStrokeColor,
          this.outFillColor,
          this.innerStrokeColor,
          this.innerFillColor
        ]
      },
      twoTone(): string[] {
        return [
          this.outStrokeColor,
          this.outFillColor,
          this.outStrokeColor,
          this.outFillColor
        ]
      },
      filled(): string[] {
        return [this.outStrokeColor, this.outStrokeColor, '#FFF', '#FFF']
      },
      outline(): string[] {
        return [this.outStrokeColor, 'none', this.outStrokeColor, 'none']
      },
      changeMessage(event : WebViewMessageEvent) {
       const props = this
          const src = 
          
    '<?xml version="1.0" encoding="UTF-8"?>'
    + '<svg width="' + props.sizeWithUnit + '" height="' + props.sizeWithUnit + '" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">'
        + '<path fill-rule="evenodd" clip-rule="evenodd" d="M24 43.6C32.432 43.6 39.5606 36.9192 41.8935 29.2497C42.4179 27.5255 46 27.5255 46 23.8C46 20.0745 42.3839 19.8601 41.7987 18.048C39.3724 10.5346 32.3209 4 24 4C15.6745 4 8.61973 10.5407 6.19725 18.0606C5.61467 19.8691 2 20.0091 2 23.8C2 27.5909 5.59225 27.5909 6.1349 29.3421C8.4967 36.9639 15.6018 43.6 24 43.6Z" fill="' + props.colors[1] + '" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '"/>'
        + '<path d="M41.7987 18.048C39.3724 10.5346 32.3209 4 24 4" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '"/>'
        + '<path d="M19.1002 21.5998C19.1002 22.4261 18.876 23.1516 18.5398 23.6559C18.2013 24.1637 17.7885 24.3998 17.4002 24.3998C17.0119 24.3998 16.5991 24.1637 16.2606 23.6559C15.9244 23.1516 15.7002 22.4261 15.7002 21.5998C15.7002 20.7735 15.9244 20.048 16.2606 19.5437C16.5991 19.0359 17.0119 18.7998 17.4002 18.7998C17.7885 18.7998 18.2013 19.0359 18.5398 19.5437C18.876 20.048 19.1002 20.7735 19.1002 21.5998Z" fill="' + props.colors[2] + '" stroke="' + props.colors[2] + '"/>'
        + '<path d="M32.2999 21.5998C32.2999 22.4261 32.0757 23.1516 31.7395 23.6559C31.401 24.1637 30.9882 24.3998 30.5999 24.3998C30.2116 24.3998 29.7988 24.1637 29.4603 23.6559C29.1241 23.1516 28.8999 22.4261 28.8999 21.5998C28.8999 20.7735 29.1241 20.048 29.4603 19.5437C29.7988 19.0359 30.2116 18.7998 30.5999 18.7998C30.9882 18.7998 31.401 19.0359 31.7395 19.5437C32.0757 20.048 32.2999 20.7735 32.2999 21.5998Z" fill="' + props.colors[2] + '" stroke="' + props.colors[2] + '"/>'
        + '<path fill-rule="evenodd" clip-rule="evenodd" d="M18.498 31.7505C20.4289 33.0501 22.266 33.6999 24.0094 33.6999C25.7509 33.6999 27.4776 33.0515 29.1894 31.7547" fill="' + props.colors[2] + '"/>'
        + '<path d="M18.498 31.7505C20.4289 33.0501 22.266 33.6999 24.0094 33.6999C25.7509 33.6999 27.4776 33.0515 29.1894 31.7547" stroke="' + props.colors[2] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '"/>'
        + '<path d="M31.7283 6.2002C31.9964 8.13368 31.4067 9.54651 29.9593 10.4387C28.5119 11.3309 26.1602 11.749 22.9043 11.693" stroke="' + props.colors[0] + '" stroke-width="' + props.strokeWidth + '" stroke-linecap="' + props.strokeLinecap + '"/>'
    + '</svg>'

       
        if ((event.detail.data?.get("isInitialize") ?? false) == true) {
          this.webviewContext = uni.createWebviewContext(this._uid, this);
          this.webviewContext?.evalJS("onReceiveSvg('"+src+"')");
        } else {
          this.png = event.detail.data?.get("png");
          this.isShow = false;
        };
      },
      uuid(length : number) : string {
        let uuid : string = "";
        let chars : string[] = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        for (let i = 0; i < length; i++) { uuid += chars[Math.floor(Math.random() * chars.length)]; };
        return uuid;
      },
      changeError(event : ImageErrorEvent) {
        this.$emit("error", event)
      },
      changeLoad(event : ImageLoadEvent) {
        this.$emit("load", event)
      }
    }
  }
</script>

<style scoped>
  @import '../../style/index.css';
</style>
