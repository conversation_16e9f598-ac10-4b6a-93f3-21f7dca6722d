<template>
	<view class="content">
		<view :class="longPress == '1' ? 'record-layer' : 'record-layer1'">
			<view :class="longPress == '1' ? 'record-box' : 'record-box1'">
				<view class="record-btn-layer">
					<button class="record-btn" :class="longPress == '1' ? 'record-btn-1' : 'record-btn-2'"
						:style="VoiceTitle != '松开手指,取消发送' && longPress != '1'?'background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);':'background-color: rgba(0, 0, 0, .5);color:white'"
						@touchend="endRecognize" @touchstart="startRecognize">
						<text v-if="longPress == 1">{{VoiceText}}</text>
						<text v-if="longPress == 2">{{VoiceText_inProgress}}</text>
						<text v-if="longPress == 3">{{VoiceText_complete}}</text>
					</button>
				</view>

				<view :class="VoiceTitle != '松开手指,取消发送'?'prompt-layer prompt-layer-1':'prompt-layer1 prompt-layer-1'"
					v-if="longPress == '2' || longPress == 3">
					<view class="prompt-loader" v-if="longPress == 2">
						<view class="em" v-for="(item,index) in 15" :key="index"></view>
					</view>
					<u--input v-model="result" placeholder="正在识别内容..." autoHeight></u--input>
					
					<textarea style="width: 100%;text-align: left;" 
					 :auto-height="true" v-model="result" />

					<u-button v-if="longPress == 3" shape="circle" style="margin-top: 20rpx;" @click="submit">
						<image class="agree" src='/static/img/同意.png' mode="">
					</u-button>
					</image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, onUnmounted } from 'vue';

// 响应式状态
const title = ref('未开始');
const text = ref('');
const partialResult = ref('...');
const result = ref('正在识别...');
const valueWidth = ref('0px');
const VoiceText = ref('按住说话');
const VoiceText_inProgress = ref('说话中...');
const VoiceText_complete = ref('语音识别完成，请核对！');
const longPress = ref(1); // 语音动画状态
const recordingId = ref(null); // 用于保存录音任务ID
const recorderManager = ref(null); // 录音管理器实例
const socketTask = ref(null); // WebSocket 实例
const audioChunks = reactive([]); // 用于存储音频数据块
const task_id = ref(''); // 任务ID
const isSocketOpen = ref(false); // 标志位，表示 WebSocket 是否已打开

// 生命周期钩子
onUnmounted(() => {
	VoiceText.value = "按住说话";
});

// 方法
const startRecognize = () => {
	longPress.value = 2;
	// 创建 WebSocket 连接
	socketTask.value = uni.connectSocket({
		url: 'ws://192.168.1.55:8001/ws/asr/',
		success: (res) => {
			console.log(res);
			console.log('WebSocket连接成功');
		}
	});

	socketTask.value.onOpen(() => {
		console.log('WebSocket连接已打开');
		result.value = '';
		isSocketOpen.value = true;
		longPress.value = 2;
		sendRunTaskCmd();
	});

	socketTask.value.onMessage((res) => {
		const data = JSON.parse(res.data);
		if (data.type == 'result') {
			result.value += data.text;
		}
		console.log(result.value);
		console.log('收到服务器消息:', res);
	});

	// 初始化录音管理器
	recorderManager.value = uni.getRecorderManager();
	recorderManager.value.onFrameRecorded((res) => {
		socketTask.value.onOpen(() => {
			console.log('WebSocket连接已打开');
			isSocketOpen.value = true;
		});
		sendAudioData(res.frameBuffer);
	});

	recorderManager.value.start({
		format: 'PCM',
		duration: 60000,
		sampleRate: 8000,
		numberOfChannels: 1,
		encodeBitRate: 48000,
		frameSize: 1
	});
	recordingId.value = true;
};

const endRecognize = () => {
	longPress.value = 3;
	if (recordingId.value) {
		recorderManager.value.stop();
		recordingId.value = null;
		sendFinishTaskCmd();
	}
};

const sendRunTaskCmd = () => {
	task_id.value = generateTaskId();
	const runTaskCmd = {
		header: {
			action: 'run-task',
			task_id: task_id.value,
			streaming: 'duplex'
		},
		payload: {
			task_group: 'audio',
			task: 'asr',
			function: 'recognition',
			model: 'paraformer-realtime-v2',
			parameters: {
				format: 'PCM',
				sample_rate: 8000
			},
			input: {}
		}
	};
	socketTask.value.send({
		data: JSON.stringify(runTaskCmd),
		success: () => {
			console.log('开始任务指令发送成功');
		},
		fail: (err) => {
			console.error('开始任务指令发送失败', err);
		}
	});
};

const sendFinishTaskCmd = () => {
	const finishTaskCmd = {
		header: {
			type: 'audio',
			audio: '',
			is_end: true
		},
		payload: {
			input: {}
		}
	};
	if (isSocketOpen.value) {
		socketTask.value.send({
			data: JSON.stringify(finishTaskCmd),
			success: () => {
				console.log('结束任务指令发送成功');
			},
			fail: (err) => {
				console.error('结束任务指令发送失败', err);
			}
		});
	} else {
		console.error('WebSocket连接已关闭，无法发送结束任务指令');
	}
};

const sendAudioData = (audioData) => {
	if (socketTask.value && isSocketOpen.value) {
		const base64Data = uni.arrayBufferToBase64(audioData);
		const message = {
			type: 'audio',
			audio: base64Data,
			is_end: false
		};
		socketTask.value.send({
			data: JSON.stringify(message),
			success: () => {
				console.log('音频数据发送成功');
			},
			fail: (err) => {
				console.error('音频数据发送失败', err);
			}
		});
	} else {
		console.error('WebSocket连接未打开，无法发送音频数据');
	}
};

const generateTaskId = () => {
	return Math.random().toString(36).substr(2, 32);
};

const arrayBufferToBase64 = (buffer) => {
	if (typeof uni !== 'undefined' && uni.arrayBufferToBase64) {
		return uni.arrayBufferToBase64(buffer);
	} else if (typeof wx !== 'undefined' && wx.arrayBufferToBase64) {
		return wx.arrayBufferToBase64(buffer);
	} else if (typeof FileReader !== 'undefined') {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onload = () => resolve(reader.result.split(',')[1]);
			reader.onerror = reject;
			reader.readAsDataURL(new Blob([buffer]));
		});
	} else {
		console.error('当前环境不支持 ArrayBuffer 转 Base64');
		return '';
	}
};

const submit = () => {
	longPress.value = 1;
	uni.navigateTo({
		url: '/pages/order/order'
	});
};
</script>

<style scoped>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.recogniz {
		width: 100%;
		max-width: 400px;
		padding: 12px;
		margin: 20px 0;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 16px;
		text-align: center;
	}

	.partial {
		width: 100%;
		height: 40px;
		margin-top: 16px;
		font-size: 12px;
		color: #ffffff;
	}

	.volume {
		width: 100%;
		height: 6px;
		background-color: #e0e0e0;
		border-radius: 3px;
		overflow: hidden;
		margin-top: 10px;
	}

	.volume::after {
		content: '';
		display: block;
		height: 100%;
		background-color: #00cc00;
		width: 0;
		transition: width 0.3s;
	}

	.result {
		color: #cccccc;
		border: #00cccc 1px solid;
		margin: 25px auto;
		padding: 6px;
		width: 80%;
		height: 100px;
	}

	.prompt-loader {
		width: 96px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 6px;
	}

	.prompt-loader .em {
		display: block;
		background: #333333;
		width: 1px;
		height: 10%;
		margin-right: 2.5px;
		float: left;
	}

	.prompt-loader .em:last-child {
		margin-right: 0px;
	}

	.prompt-loader .em:nth-child(1) {
		animation: load 2.5s 1.4s infinite linear;
	}

	.prompt-loader .em:nth-child(2) {
		animation: load 2.5s 1.2s infinite linear;
	}

	.prompt-loader .em:nth-child(3) {
		animation: load 2.5s 1s infinite linear;
	}

	.prompt-loader .em:nth-child(4) {
		animation: load 2.5s 0.8s infinite linear;
	}

	.prompt-loader .em:nth-child(5) {
		animation: load 2.5s 0.6s infinite linear;
	}

	.prompt-loader .em:nth-child(6) {
		animation: load 2.5s 0.4s infinite linear;
	}

	.prompt-loader .em:nth-child(7) {
		animation: load 2.5s 0.2s infinite linear;
	}

	.prompt-loader .em:nth-child(8) {
		animation: load 2.5s 0s infinite linear;
	}

	.prompt-loader .em:nth-child(9) {
		animation: load 2.5s 0.2s infinite linear;
	}

	.prompt-loader .em:nth-child(10) {
		animation: load 2.5s 0.4s infinite linear;
	}

	.prompt-loader .em:nth-child(11) {
		animation: load 2.5s 0.6s infinite linear;
	}

	.prompt-loader .em:nth-child(12) {
		animation: load 2.5s 0.8s infinite linear;
	}

	.prompt-loader .em:nth-child(13) {
		animation: load 2.5s 1s infinite linear;
	}

	.prompt-loader .em:nth-child(14) {
		animation: load 2.5s 1.2s infinite linear;
	}

	.prompt-loader .em:nth-child(15) {
		animation: load 2.5s 1.4s infinite linear;
	}

	@keyframes load {
		0% {
			height: 10%;
		}

		50% {
			height: 100%;
		}

		100% {
			height: 10%;
		}
	}

	/* 语音音阶-------------------- */
	.prompt-layer-2 {
		top: -40px;
	}

	.prompt-layer-2 .text {
		color: rgba(0, 0, 0, 1);
		font-size: 12px;
	}

	.voice-list {
		padding: 20rpx;
	}

	.voice-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f1f1f1;
	}

	.voice-content {
		display: flex;
		align-items: center;
	}

	.voice-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.voice-time {
		color: #999;
		font-size: 24rpx;
	}

	/* 语音录制开始--------------------------------------------------------------------- */
	.record-layer {
		// width: 91vw;
		// padding: 300px 0;
		box-sizing: border-box;
		height: 100%;
		// position: fixed;
		// margin-left: 4vw;
		z-index: 10;
		// bottom: 3vh;
		display: flex;
		align-items: center;
	}

	.record-layer1 {
		width: 100vw;
		// padding: 300px 0;
		box-sizing: border-box;
		height: 100vh;
		position: fixed;
		background-color: rgba(0, 0, 0, .6);
		// padding: 0 4vw;
		z-index: 10;
		bottom: 0vh;
		left: 0;
	}

	.record-box {
		width: 100%;
		position: relative;
	}

	.record-box1 {
		width: 100%;
		position: relative;
		bottom: -83vh;
		height: 17vh;
	}

	.record-btn-layer {
		width: 100%;
	}

	.record-btn-layer button::after {
		border: none;
		transition: all 0.1s;
	}

	.record-btn-layer button {
		font-size: 14px;
		line-height: 35px;
		width: 100%;
		// height: 50px;
		border-radius: 8px;
		text-align: center;
		background: #FFD300;
		transition: all 0.1s;
	}

	.record-btn-layer button image {
		width: 16px;
		height: 16px;
		margin-right: 4px;
		vertical-align: middle;
		transition: all 0.3s;
	}

	.record-btn-layer .record-btn-1 {
		background-image: linear-gradient(to right, #f1f1f1 0%, #f1f1f1 100%);
		color: #000000 !important;
	}

	.record-btn-layer .record-btn-2 {
		border-radius: 168rpx 168rpx 0 0;
		height: 17vh;
		line-height: 17vh;
		transition: all 0.3s;
	}

	/* 提示小弹窗 */
	.prompt-layer {
		border-radius: 15px;
		background: #95EB6C;
		padding: 8px 16px;
		box-sizing: border-box;
		position: absolute;
		left: 50%;
		/* height: 11vh; */
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	.prompt-layer::after {
		content: '';
		display: block;
		border: 12px solid rgba(0, 0, 0, 0);
		border-radius: 10rpx;
		border-top-color: #95EB6C;
		position: absolute;
		bottom: -46rpx;
		left: 50%;
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	//取消动画
	.prompt-layer1 {
		border-radius: 15px;
		background: #FB5353;
		padding: 8px 16px;
		box-sizing: border-box;
		position: absolute;
		left: 50%;
		height: 11vh;
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	.prompt-layer1::after {
		content: '';
		display: block;
		border: 12px solid rgba(0, 0, 0, 0);
		border-radius: 10rpx;
		border-top-color: #FB5353;
		position: absolute;
		bottom: -46rpx;
		left: 50%;
		transform: translateX(-50%);
		transition: all 0.3s;
	}

	.prompt-layer-1 {
		font-size: 12px;
		width: 150px;
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		top: -400rpx;
	}

	.prompt-layer-1 .p {
		color: #000000;
	}

	.prompt-layer-1 .span {
		color: rgba(0, 0, 0, .6);
	}

	.prompt-loader .em {}

	.agree {
		width: 50rpx;
		height: 50rpx;
		/* background-color: #000000; */
		border-radius: 50%;

	}

	/* .agree image {
			width: 100%;
			height: 100%;
		} */
</style>