<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="5" />

    <!-- 退货订单列表 -->
    <scroll-view scroll-y class="retail-list" :style="{ height: scrollViewHeight }" @scrolltolower="loadMore">
      <view class="retail-item attribute_font" v-for="(item, index) in returnRetailOrdersList" :key="index"
        @click="jumpToView(item)">
        <view class="item-content">
          <view class="info-row">
            <text class="label">客户</text>
            <text class="value">：{{ item.customer_name }}</text>
          </view>
          <view class="info-row">
            <text class="label">商品</text>
            <text class="value">：{{ item.short_desc }}</text>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">操作员</text>
              <text class="value">：{{ item.handler_name }}</text>
            </view>
            <view>
              <text class="sub-label">数量</text>
              <text class="value">：{{ item.item_count }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">金额合计</text>
              <text class="value">：{{ $toFixed(item.total_amount) }}</text>
            </view>
            <view>
              <text class="sub-label">退款优惠</text>
              <text class="value">：{{ item.total_amount - item.payable_amount }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">退款金额</text>
              <text class="value">：{{ item.payable_amount }}</text>
            </view>
          </view>

        </view>
        <view class="info-bottom">
          <view class="info-row" style="margin-bottom: 0">
            <text class="label">单据日期</text>
            <text class="value">：{{ item.update_at }}</text>
          </view>
          <button class="share-btn">
            分享
          </button>
        </view>
        <!-- 加载更多/没有更多 -->
      
        </view>
          <u-loadmore :status="loadStatus" loading-text="正在加载..." nomore-text="没有更多数据了" :icon="true" />
          <u-loadmore :status="loadStatus" loading-text="正在加载..." nomore-text="没有更多数据了" :icon="true" />
      </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="true" :jumpToId="9" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import { getReturnRetailList, getReturnRetailDetail } from '@/api/returnRetail';

const returnRetailOrdersList = ref<any[]>([]);
// 分页参数
const returnRetailListParams = reactive({
  page: 1,
  page_size: 10,
});
const hasMore = ref(true);
const loading = ref(false);
const loadStatus = ref('loadmore');
const scrollViewHeight = ref("500px");

// 首次加载回调
const fakeLoad = (data: any) => {
  console.log("fakeLoad", data);
  returnRetailOrdersList.value = data.results || [];
  hasMore.value = data.count > data.results.length;
  loading.value = false;
  loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';
};

// 第一页数据回调
const pageOne = (data: any) => {
  console.log("pageOne", data);
  returnRetailOrdersList.value = data.results || [];
  hasMore.value = data.count > returnRetailOrdersList.value.length;
  loading.value = false;
  loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';
};

// 加载更多数据回调
const expandPage = (data: any, page: number) => {
  console.log("expandPage", data, page);
  const newResults = data.results || [];
  returnRetailOrdersList.value = returnRetailOrdersList.value.concat(newResults);
  hasMore.value = data.count > returnRetailOrdersList.value.length;
  loading.value = false;
  loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';
};

// 使用 pageDataManager
const pageData = getReturnRetailList({}, {
  fakeLoadCallback: fakeLoad,
  pageOneCallback: pageOne,
  expandCallback: expandPage
});

const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44;
    const searchHeight = (screenWidth * 80) / 750;
    const inputHeight = (screenWidth * 90) / 750;
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};

const getList = (isLoadMore = false) => {
  if (isLoadMore) {
    pageData.nextPage();
  } else {
    pageData.firstPage();
  }
  loading.value = true;
};

const loadMore = () => {
  if (hasMore.value && !loading.value) {
    getList(true);
  }
};

const jumpToView = (item: any) => {
  getReturnRetailDetail(item.id).then((res) => {
      uni.navigateTo({
        url: '/pages/returnRetailOrders/editReturnRetailOrders?data=' + JSON.stringify(res)
      })
  }).catch(err => {
    console.error('获取详情失败:', err);
    uni.showToast({ title: '获取详情失败', icon: 'none' });
  });
};

onMounted(() => {
  initScrollViewHeight();
});

onShow(() => {
  returnRetailListParams.page = 1; // pageNum is not defined, assuming it refers to returnRetailListParams.page
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  getList();
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.retail-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.retail-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.item-content {
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 400rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 100rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.sub-value {
  color: #333;
  margin-right: 20rpx;
}

.info-bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.share-btn {
  color: #4080e8;
  font-size: 28rpx;
  text-align: right;
  padding: 10rpx 0;
  background: none;
  border: none;
  line-height: 1;
  margin: 0;
}

.share-btn::after {
  border: none;
}

.status-order-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}
</style>
