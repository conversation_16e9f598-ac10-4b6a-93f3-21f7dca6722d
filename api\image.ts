import request from '@/utils/request';
import store from '@/store';

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

console.log('当前账套名称：', getLedgerName());

/**
 * 上传图片
 * @param {FormData | object} data - 图片文件数据，可以是 FormData 或包含文件信息的对象
 * @returns {Promise<any>}
 */
export const uploadImageFile = (data: FormData | object): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/images`, data);
};

/**
 * 检查图片是否重复
 * @param {object} data - 包含图片MD5等信息的数据
 * @returns {Promise<any>}
 */
export const checkForDuplication = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/images`, data);
};

/**
 * 获取物品图片
 * @param {string} id - 物品ID
 * @returns {Promise<any>}
 */
export const getGoodsImage = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/items/${id}/images/`);
};

/**
 * 获取指定物品图片缩略图
 * @param {string} id - 物品ID
 * @returns {Promise<any>}
 */
export const getGoodsImageThumbnail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/items/${id}/thumbnails/?order=0`);
};

/**
 * 删除指定物品图片
 * @param {string} id - 物品ID
 * @param {number} order - 图片顺序
 * @returns {Promise<any>}
 */
export const delGoodsImage = (id: string, order: number): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/items/${id}/delete_image/?order=${order}`);
};
