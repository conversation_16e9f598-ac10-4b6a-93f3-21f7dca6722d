// 定义商品项接口
interface GoodsItem {
  code: string;
  quantity: string;
  price: string;
  wholesalePrice: string;
  retailPrice: string;
  lowestSellingPrice: string;
  total: string;
  unit: string;
}

// 定义采购信息接口
interface PurchaseInfo {
  supplier: string;
  date: string;
  goodsId: string;
  goodsList: GoodsItem[];
  totalAmount: string;
  discountRate: string;
  finalAmount: string;
  account: string;
  deposit: string;
  remark: string;
  attachments: any[]; // 假设 attachments 是任意类型数组
}

// 定义模块状态接口
interface GoodsState {
  purchaseInfo: PurchaseInfo;
  isExpandGoodsList: boolean;
}

// 持久化存储的key
const STORAGE_KEY = 'goods_state';

// 从本地存储恢复state
const getStorageState = (): GoodsState | null => {
  try {
    const storage = uni.getStorageSync(STORAGE_KEY);
    return storage ? JSON.parse(storage) : null;
  } catch (e) {
    console.error('获取本地存储失败:', e);
    return null;
  }
};

// 初始state
const getDefaultState = (): GoodsState => ({
  purchaseInfo: {
    supplier: "",
    date: "",
    goodsId: "GD3834273",
    goodsList: [
      {
        code: "1001",
        quantity: "200",
        price: "2",
        wholesalePrice: "2",
        retailPrice: "2",
        lowestSellingPrice: "2",
        total: "200",
        unit: "个",
      },
    ],
    totalAmount: "400",
    discountRate: "0",
    finalAmount: "400",
    account: "",
    deposit: "",
    remark: "",
    attachments: [],
  },
  isExpandGoodsList: true,
});

// 合并本地存储的state和默认state
const state: GoodsState = {
  ...getDefaultState(),
  ...getStorageState(),
};

// 保存state到本地存储
const saveStateToStorage = (state: GoodsState) => {
  try {
    uni.setStorageSync(STORAGE_KEY, JSON.stringify(state));
  } catch (e) {
    console.error('保存到本地存储失败:', e);
  }
};

const mutations = {
  /**
   * 设置采购信息
   * @param {GoodsState} state - Vuex state
   * @param {PurchaseInfo} info - 采购信息对象
   */
  SET_PURCHASE_INFO(state: GoodsState, info: PurchaseInfo) {
    state.purchaseInfo = info;
    saveStateToStorage(state);
  },
  /**
   * 设置商品列表
   * @param {GoodsState} state - Vuex state
   * @param {GoodsItem[]} list - 商品列表数组
   */
  SET_GOODS_LIST(state: GoodsState, list: GoodsItem[]) {
    state.purchaseInfo.goodsList = list;
    saveStateToStorage(state);
  },
  /**
   * 添加商品项
   * @param {GoodsState} state - Vuex state
   * @param {GoodsItem} item - 商品项对象
   */
  ADD_GOODS_ITEM(state: GoodsState, item: GoodsItem) {
    state.purchaseInfo.goodsList.push(item);
    saveStateToStorage(state);
  },
  /**
   * 更新商品项
   * @param {GoodsState} state - Vuex state
   * @param {object} payload - 包含索引和商品项的对象
   * @param {number} payload.index - 商品项的索引
   * @param {GoodsItem} payload.item - 更新后的商品项
   */
  UPDATE_GOODS_ITEM(state: GoodsState, payload: { index: number; item: GoodsItem }) {
    state.purchaseInfo.goodsList[payload.index] = payload.item;
    saveStateToStorage(state);
  },
  /**
   * 移除商品项
   * @param {GoodsState} state - Vuex state
   * @param {number} index - 商品项的索引
   */
  REMOVE_GOODS_ITEM(state: GoodsState, index: number) {
    state.purchaseInfo.goodsList.splice(index, 1);
    saveStateToStorage(state);
  },
  /**
   * 设置展开状态
   * @param {GoodsState} state - Vuex state
   * @param {boolean} status - 展开状态
   */
  SET_EXPAND_STATUS(state: GoodsState, status: boolean) {
    state.isExpandGoodsList = status;
    saveStateToStorage(state);
  },
  /**
   * 更新总金额
   * @param {GoodsState} state - Vuex state
   */
  UPDATE_TOTAL_AMOUNT(state: GoodsState) {
    const total = state.purchaseInfo.goodsList.reduce((sum, item) => {
      return sum + Number(item.total);
    }, 0);
    state.purchaseInfo.totalAmount = total.toString();
    const discountRate = Number(state.purchaseInfo.discountRate) / 100;
    state.purchaseInfo.finalAmount = (total * (1 - discountRate)).toString();
    saveStateToStorage(state);
  },
  /**
   * 重置state
   * @param {GoodsState} state - Vuex state
   */
  RESET_STATE(state: GoodsState) {
    Object.assign(state, getDefaultState());
    uni.removeStorageSync(STORAGE_KEY);
  },
};

const actions = {
  /**
   * 设置整个采购信息
   * @param {object} { commit, rootState } - Vuex commit 和 rootState
   * @param {PurchaseInfo} info - 采购信息对象
   */
  setPurchaseInfo({ commit, rootState }: { commit: Function; rootState: any }, info: PurchaseInfo) {
    // rootState.user.ledger_name 的类型需要根据实际情况定义
    const ledgerName = rootState.user.ledger_name;
    console.log('当前账套名称：', ledgerName);
    commit('SET_PURCHASE_INFO', info);
  },

  /**
   * 添加商品
   * @param {object} { commit, dispatch, rootState } - Vuex commit, dispatch 和 rootState
   * @param {GoodsItem} item - 商品项对象
   */
  addGoodsItem({ commit, dispatch, rootState }: { commit: Function; dispatch: Function; rootState: any }, item: GoodsItem) {
    const ledgerName = rootState.user.ledger_name;
    commit('ADD_GOODS_ITEM', item);
    dispatch('updateTotalAmount');
  },

  /**
   * 更新商品
   * @param {object} { commit, dispatch, rootState } - Vuex commit, dispatch 和 rootState
   * @param {object} payload - 包含索引和商品项的对象
   * @param {number} payload.index - 商品项的索引
   * @param {GoodsItem} payload.item - 更新后的商品项
   */
  updateGoodsItem({ commit, dispatch, rootState }: { commit: Function; dispatch: Function; rootState: any }, payload: { index: number; item: GoodsItem }) {
    const ledgerName = rootState.user.ledger_name;
    commit('UPDATE_GOODS_ITEM', payload);
    dispatch('updateTotalAmount');
  },

  /**
   * 删除商品
   * @param {object} { commit, dispatch, rootState } - Vuex commit, dispatch 和 rootState
   * @param {number} index - 商品项的索引
   */
  removeGoodsItem({ commit, dispatch, rootState }: { commit: Function; dispatch: Function; rootState: any }, index: number) {
    const ledgerName = rootState.user.ledger_name;
    commit('REMOVE_GOODS_ITEM', index);
    dispatch('updateTotalAmount');
  },

  /**
   * 设置展开状态
   * @param {object} { commit } - Vuex commit
   * @param {boolean} status - 展开状态
   */
  setExpandStatus({ commit }: { commit: Function }, status: boolean) {
    commit('SET_EXPAND_STATUS', status);
  },

  /**
   * 更新总金额
   * @param {object} { commit } - Vuex commit
   */
  updateTotalAmount({ commit }: { commit: Function }) {
    commit('UPDATE_TOTAL_AMOUNT');
  },

  /**
   * 重置商品模块状态
   * @param {object} { commit } - Vuex commit
   */
  resetState({ commit }: { commit: Function }) {
    commit('RESET_STATE');
  },
};

const getters = {
  /**
   * 获取商品列表
   * @param {GoodsState} state - Vuex state
   * @returns {GoodsItem[]}
   */
  goodsList: (state: GoodsState): GoodsItem[] => state.purchaseInfo.goodsList,

  /**
   * 获取展开的商品列表（第一个或全部）
   * @param {GoodsState} state - Vuex state
   * @returns {GoodsItem[]}
   */
  displayGoodsList: (state: GoodsState): GoodsItem[] => {
    return state.isExpandGoodsList
      ? state.purchaseInfo.goodsList
      : state.purchaseInfo.goodsList.slice(0, 1);
  },

  /**
   * 获取总金额信息
   * @param {GoodsState} state - Vuex state
   * @returns {object}
   */
  totalInfo: (state: GoodsState) => ({
    totalAmount: state.purchaseInfo.totalAmount,
    discountRate: state.purchaseInfo.discountRate,
    finalAmount: state.purchaseInfo.finalAmount,
  }),

  /**
   * 获取当前账套名称
   * @param {GoodsState} state - Vuex state
   * @param {any} getters - Vuex getters
   * @param {any} rootState - Vuex rootState
   * @returns {string}
   */
  currentLedgerName: (state: GoodsState, getters: any, rootState: any): string => rootState.user.ledger_name,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
