<template>
	<view>
		<u-popup @close="closePopup" @open="handleOpen" mode="bottom" round="10" :show="selectorShow">
			<view class="selector-container">
				<!-- 头部区域 -->
				<view class="header">
					<text class="cancel" @click="closePopup">取消</text>
					<view class="search-box">
						<u-search v-model="keyword" placeholder="请输入要搜索的关键词"
							:clearabled="true" :showAction="false" height="60rpx" @change="handleSearch"></u-search>
					</view>
					<text class="confirm" @click="handleConfirm">确定</text>
				</view>

				<!-- 自定义滑动选择器 -->
				<view class="custom-picker">
					<view class="picker-mask"></view>
					<view class="picker-indicator"></view>
					<scroll-view scroll-y class="picker-content" :scroll-top="scrollTop" @scroll="handleScroll"
						@touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd"
						@scrolltolower="handleScrollToLower">
						<!-- 顶部空白占位 -->
						<view class="picker-item placeholder"></view>
						<view class="picker-item" v-for="(item, index) in filteredList" :key="index"
							:class="{ active: currentIndex === index }" :data-index="index">
							{{ item.name }}
						</view>
						<!-- 底部空白占位 -->
						<view class="picker-item placeholder"></view>
					</scroll-view>
				</view>

				<!-- 新建图标 -->
				<view class="add_icon">
					<view class="new-icon" @click="handleNew">
						<i-add-one theme="outline" size="24" fill="#23a8f2" />
					</view>
				</view>
			</view>
			<!-- 弹出层示例 -->
			<view class="popup" style="position: relative;">
				<!-- 弹出层内容 -->
				<refreshPage :loading="loading" />
			</view>
		</u-popup>

		<generalAddition :generalAdditionShow="generalAdditionShow" :additionType="additionType"
			@close="closeGeneralAddition" @confirm="handleNewSupplier" />

	</view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import generalAddition from "./generalAddition.vue";
import refreshPage from "./refreshPage.vue";
import { getSupplier } from '@/api/supplier';
import { getCustomerList } from '@/api/customer';

// 定义接口
interface ListItem {
  name: string;
  [key: string]: any;
}

interface ScrollEvent {
  detail: {
    scrollTop: number;
  }
}

interface TouchEvent {
  touches: {
    pageY: number;
  }[];
}

interface Props {
  selectorShow: boolean;
  selectType: number;
}

const props = withDefaults(defineProps<Props>(), {
  selectorShow: false,
  selectType: 0, // 0: 供应商, 1: 客户
});

const emit = defineEmits(['close', 'confirm', 'update:selectorShow', 'customerScrollToLower']);

const ITEM_HEIGHT = 100; // 每项高度（rpx）

const keyword = ref(''); // 搜索关键词
const scrollTop = ref(0); // 滚动位置
const currentIndex = ref(0); // 当前选中项索引
const startY = ref(0); // 触摸起始位置
const selectedItem = ref<ListItem | null>(null); // 选中的完整项
const generalAdditionShow = ref(false); // 通用新增弹出层是否展示
const additionType = ref(0); // 通用新增类型
const currentPage = ref(1);
const pageSize = ref(10);
const list = ref<ListItem[]>([]);
const hasMore = ref(true);
const loading = ref(false); // 页面加载动画

// 根据搜索关键词过滤列表
const filteredList = computed(() => {
  if (!keyword.value) return list.value;
  return list.value.filter((item: ListItem) =>
    item.name.toLowerCase().includes(keyword.value.toLowerCase())
  );
});

// 处理打开的事件
const handleOpen = () => {
  loading.value = true;
  currentPage.value = 1;
  if (props.selectType === 0) {
    getSupplierData();
  } else if (props.selectType === 1) {
    getCustomerData();
  }
};

const getSupplierData = async () => {
  try {
    const res = await getSupplier({
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: keyword.value
    });
    const results = res.data.results || [];
    if (currentPage.value === 1) {
      list.value = results;
      selectedItem.value = list.value[0];
    } else {
      list.value = [...list.value, ...results];
    }
    // 判断是否有更多数据
    hasMore.value = results.length >= pageSize.value;
    console.log('getSupplierData:', list.value, 'hasMore:', hasMore.value);
  } catch (error) {
    console.error('加载供应商数据失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

const getCustomerData = async () => {
  try {
    const res = await getCustomerList({
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: keyword.value
    });
    const results = res.data.results || [];
    if (currentPage.value === 1) {
      list.value = results;
      selectedItem.value = list.value[0];
    } else {
      list.value = [...list.value, ...results];
    }
    // 判断是否有更多数据
    hasMore.value = results.length >= pageSize.value;
    console.log('getCustomerData:', list.value, 'hasMore:', hasMore.value);
  } catch (error) {
    console.error('加载客户数据失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (value: string) => {
  keyword.value = value;
  scrollTop.value = 0;
  currentIndex.value = 0;
  currentPage.value = 1;
  if (props.selectType === 0) {
    getSupplierData();
  } else if (props.selectType === 1) {
    getCustomerData();
  }
};

// 处理滚动到底部
const handleScrollToLower = () => {
  // 如果没有更多数据则停止加载
  if (!hasMore.value) return;
  currentPage.value++;
  if (props.selectType === 0) {
    getSupplierData();
  } else if (props.selectType === 1) {
    getCustomerData();
  }
  emit('customerScrollToLower'); // 触发父组件的滚动到底部事件
};

// 处理滚动
const handleScroll = (e: ScrollEvent) => {
  const scrollTopVal = e.detail.scrollTop;
  const index = Math.round(scrollTopVal / uni.upx2px(ITEM_HEIGHT));
  if (index !== currentIndex.value && index >= 0 && index < filteredList.value.length) {
    currentIndex.value = index;
    selectedItem.value = filteredList.value[index];
  }
};

// 处理触摸开始
const handleTouchStart = (e: TouchEvent) => {
  startY.value = e.touches[0].pageY;
};

// 处理触摸移动
const handleTouchMove = (e: TouchEvent) => {
  const moveY = e.touches[0].pageY;
  const diff = moveY - startY.value;
  const newScrollTop = scrollTop.value - diff;
  scrollTop.value = Math.max(0, Math.min(newScrollTop, (filteredList.value.length - 1) * uni.upx2px(ITEM_HEIGHT)));
  startY.value = moveY;
};

// 处理触摸结束
const handleTouchEnd = () => {
  // 滚动到最近的选项
  const targetScrollTop = currentIndex.value * uni.upx2px(ITEM_HEIGHT);
  scrollTop.value = targetScrollTop;
};

// 确认选择
const handleConfirm = () => {
  console.log(selectedItem.value);
  if (selectedItem.value) {
    console.log(selectedItem.value);
    emit('confirm', selectedItem.value);
  }
  closePopup();
};

// 关闭弹窗
const closePopup = () => {
  emit('update:selectorShow', false); // 更新 v-model:selectorShow
  emit('close');
};

// 新建按钮点击事件 - 打开通用新增弹出层
const handleNew = () => {
  console.log(111);
  console.log(props.selectType);
  additionType.value = props.selectType;
  generalAdditionShow.value = true;
};

// 关闭通用新增弹出层
const closeGeneralAddition = () => {
  generalAdditionShow.value = false;
};

// 新增供应商
const handleNewSupplier = (data: ListItem) => {
  list.value.unshift(data);
  closeGeneralAddition();
};

watch(() => props.selectorShow, (val) => {
  if (val) {
    handleOpen();
  }
});
</script>

<style lang="scss" scoped>
.selector-container {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 590rpx;
	display: flex;
	flex-direction: column;
	position: relative;
}

.header {
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #eee;

	.cancel {
		color: #666;
		font-size: 28rpx;
		padding: 0 20rpx;
	}

	.confirm {
		color: #2979ff;
		font-size: 28rpx;
		padding: 0 20rpx;
	}

	.search-box {
		flex: 1;
		padding: 0 20rpx;
	}
}

.custom-picker {
	position: relative;
	height: 500rpx;
	overflow: hidden;

	.picker-mask {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 1;
		background-image: linear-gradient(180deg,
				rgba(255, 255, 255, 0.9),
				rgba(255, 255, 255, 0.4) 45%,
				rgba(255, 255, 255, 0.4) 55%,
				rgba(255, 255, 255, 0.9));
		pointer-events: none;
	}

	.picker-indicator {
		position: absolute;
		left: 0;
		right: 0;
		top: 50%;
		height: 100rpx;
		transform: translateY(-50%);
		border-top: 1px solid #eee;
		border-bottom: 1px solid #eee;
		z-index: 2;
		pointer-events: none;
	}

	.picker-content {
		height: 100%;
		box-sizing: border-box;

		.picker-item {
			height: 100rpx;
			line-height: 100rpx;
			text-align: center;
			font-size: 32rpx;
			color: #333;
			transition: all 0.2s;

			&.placeholder {
				height: 200rpx;
			}

			&.active {
				color: #2979ff;
				font-size: 36rpx;
				font-weight: bold;
			}
		}
	}
}

.add_icon {
	position: absolute;
	bottom: 60rpx;
	right: 70rpx;
	z-index: 111;
}
</style>
