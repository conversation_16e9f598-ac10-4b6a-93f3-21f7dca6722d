<template>
	<view>
		<!-- 个人信息 -->
		<view class="personalInformation">
			<view class="avatar">
				<img src="@/static/img/aiAssistant.png" alt="" />
			</view>
			<view class="content">

			</view>
			<view class="icon">
				<u-icon name="arrow-right" size="40" color="#9a9a9a"></u-icon>
			</view>
		</view>
		<!-- 员工信息 -->
		<view class="employeeInformation">
			员工信息
		</view>
		<!-- 账套信息 -->
		<view class="accountInformation">
			账套信息
		</view>
	</view>
</template>

<script setup lang="ts">

</script>

<style lang="scss" scoped>
	// 个人信息
	.personalInformation {
		width: 100%;
		height: 200rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #FFFFFF;
		border-bottom: #d5d5d5 1px solid;

		.avatar {
			width: 25%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;

			img {
				width: 150rpx;
				height: 150rpx;
				border-radius: 50%;
			}
		}

		.content {
			width: 75%;
			height: 100%;
		}
	}

	.icon {
		width: 50rpx;
		height: 50rpx;
	}

	// 员工信息
	.employeeInformation {
		width: 100%;
		height: 200rpx;
		background-color: #cecece;
		margin: 10rpx 0;
		padding: 10rpx;
	}

	//账套信息
	.accountInformation {
		width: 100%;
		height: 200rpx;
		background-color: #cecece;
		margin: 10rpx 0;
		padding: 10rpx;
	}
</style>