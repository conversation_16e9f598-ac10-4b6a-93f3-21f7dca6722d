<template>
  <view class="a-icon-svg" :style="[boxStyle]">
    <!-- #ifdef APP-NVUE -->
    <image class="a-icon-pack-image" :style="[boxStyle]" :src="png" mode="scaleToFill"></image>
    <web-view v-if="isShow" class="__wh-0" :ref="_uid"  @onPostMessage="changeMessage" 
    src="/uni_modules/icon-park/hybrid/html/index.html" ></web-view>
    <!-- #endif -->
    <!-- #ifndef APP-NVUE -->
    <image class="a-icon-pack-image" :src="url" mode="aspectFit"></image>
    <!-- #endif -->
  </view>
</template>

<script>
import { IconWrapper , genContent } from '../runtime'
export default genContent('i-alipay', IconWrapper('i-alipay',(props)=>(
    '<?xml version="1.0" encoding="UTF-8"?>'
    + '<svg width="' + props.sizeWithUnit + '" height="' + props.sizeWithUnit + '" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">'
        + '<path d="M24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24C0 10.7452 10.7452 0 24 0ZM24 4.36364C13.1551 4.36364 4.36364 13.1551 4.36364 24C4.36364 34.8449 13.1551 43.6364 24 43.6364C30.7379 43.6364 36.6832 40.2427 40.2199 35.0715C38.7865 34.647 36.4967 33.9121 33.537 32.6689C32.3002 32.1494 30.4531 31.3299 27.9958 30.2104C25.0556 33.1735 20.769 36 16.1742 36C12.9855 35.9844 7.63636 34.3728 7.63636 29.3458C7.63636 24.3188 12.5923 23.1225 15.8076 23.1225C17.7535 23.1225 21.1973 24.1964 26.1389 26.3443L26.2149 26.3741C27.5413 24.7514 28.4513 22.867 28.9459 20.7208L29.0455 20.2571L20.3944 20.2571C19.0841 20.257 17.6773 20.2569 16.1742 20.2568V17.9371L22.032 17.9367V15.0371L13.8306 15.0375V13.2977L22.032 13.2971L22.033 9.81818H26.72L26.7196 13.2971L36.0941 13.2977V15.0375L26.7196 15.0371V17.9367L34.2059 17.9371C34.0959 18.661 33.9722 19.3186 33.8347 19.91L33.7506 20.2568C33.4061 21.5023 32.6313 23.3162 31.4262 25.6986C31.1062 26.3312 30.6437 27.0626 30.0599 27.838C31.904 28.5215 33.771 29.1844 35.6605 29.8275C38.1068 30.6601 40.2216 31.3339 42.005 31.8487C43.0543 29.445 43.6364 26.7905 43.6364 24C43.6364 13.1551 34.8449 4.36364 24 4.36364ZM10.3154 28.5436C10.3154 31.7418 17.0047 33.0159 22.4246 29.6497C23.0347 29.2707 23.6048 28.8664 24.1348 28.4369L24.1166 28.4286L23.69 28.1665C20.308 26.1089 17.6806 24.9932 15.8076 24.8194C14.0892 24.66 10.3154 25.3455 10.3154 28.5436ZM29.0455 20.2568L29.0455 20.2571L31.2536 20.2574L29.0455 20.2568Z" fill="' + props.colors[0] + '"/>'
    + '</svg>'
)))
</script>

<style lang="scss" scoped>
@import '../../style/index.css';
</style>
