<template>
    <view>
        <u-popup :show="generalAdditionShow" mode="bottom" @close="closeGeneralAddition">
            <view class="container">
                <!-- 头部 -->
                <view class="header">
                    <view class="header-btn" @click="closeGeneralAddition">取消</view>
                    <view class="header-title">{{ headerTitle }}</view>
                    <view class="header-btn confirm-btn" @click="save">确定</view>
                </view>

                <!-- 分割线 -->
                <view class="divider"></view>

                <!-- 表单内容 -->
                <view class="form-content">
                    <u--form labelPosition="left" :model="formData" :rules="rules" ref="uForm">
                        <!-- 名称 -->
                        <u-form-item :label-width="'120rpx'" label="名称" prop="name" borderBottom required>
                            <u--input v-model="formData.name" border="none" placeholder="请输入名称"
                                inputAlign="right"></u--input>
                        </u-form-item>
                        <!-- 联系人 -->
                        <u-form-item :label-width="'120rpx'" label="联系人" prop="contact_person" borderBottom>
                            <u--input v-model="formData.contact_person" border="none" placeholder="请输入联系人"
                                inputAlign="right"></u--input>
                        </u-form-item>
                        <!-- 分类 -->
                        <u-form-item :label-width="'180rpx'" label="分类" prop="category" borderBottom>
                            <u--input v-model="formData.categoryName" border="none" placeholder="请选择分类"
                                inputAlign="right" suffixIcon="grid" suffixIconSize="20"
                                suffixIconStyle="color: #559de9" disabled disabledColor="#fff"
                                @tap="openCategory"></u--input>
                        </u-form-item>

                        <!-- 电话 -->
                        <u-form-item :label-width="'120rpx'" label="电话" prop="phone" borderBottom>
                            <u--input v-model="formData.phone" border="none" placeholder="请输入电话" inputAlign="right"
                                type="number"></u--input>
                        </u-form-item>

                        <!-- 地址 -->
                        <u-form-item :label-width="'120rpx'" label="地址" prop="address" borderBottom>
                            <u--input v-model="formData.address" border="none" placeholder="请输入地址"
                                inputAlign="right"></u--input>
                        </u-form-item>

                        <!-- 期初应付 -->
                        <u-form-item :label-width="'160rpx'" label="期初应付" prop="initialPayable" borderBottom>
                            <u--input v-model="formData.initialPayable" border="none" placeholder="请输入期初应付"
                                inputAlign="right" type="digit"></u--input>
                        </u-form-item>
                    </u--form>
                </view>
            </view>
        </u-popup>
        <!-- 商品类别弹出层 -->
        <category :CategoryShow="CategoryShow" :queryCategoryName="getQueryCategoryName"
            @update:CategoryShow="updateProductCategoryShow" @update:productCategory="updateProductCategory" />

        <u-modal :show="promptShow" :title="promptTitle" :content="promptContent" :showCancelButton="true"
            :closeOnClickOverlay="true" @confirm="handleConfirm" @cancel="handleCancel" @close="handleCancel"></u-modal>
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import category from "@/components/category/category.vue";
import { addSupplier } from "@/api/supplier";
import { saveCustomer } from "@/api/customer";

// 定义 props
const props = defineProps({
  generalAdditionShow: {
    type: Boolean,
    default: false,
  },
  // 可以传入类型来区分是供应商还是其他类型
  additionType: {
    /*
              0:供应商
              1:客户
          */
    type: Number,
    default: 0,
  },
});

// 定义 emits
const emit = defineEmits(['close', 'confirm']);

// 模板引用
const uForm = ref(null);

// 响应式数据
const formData = reactive({
  name: "",
  categoryName: "",
  contact_person: "",
  categoryId: "",
  phone: "",
  address: "",
  initialPayable: 0,
});
const CategoryShow = ref(false); // 商品类别弹出层是否展示
const promptTitle = ref("温馨提示");
const promptContent = ref("保存后后续起初应付将不允许修改，是否确认保存？");
const promptShow = ref(false);

// 计算属性
const headerTitle = computed(() => {
  const typeMap: { [key: number]: string } = {
    0: "新增供应商",
    1: "新增客户",
  };
  return typeMap[props.additionType] || "新增";
});

const getQueryCategoryName = computed(() => {
  const categoryMap: { [key: number]: string } = {
    0: "supplier",
    1: "customer",
  };
  console.log(categoryMap[props.additionType]);

  return categoryMap[props.additionType] || "supplier";
});

// 表单验证规则
const rules = reactive({
  name: [
    {
      required: true,
      message: "请输入名称",
      trigger: ["blur", "change"],
    },
  ],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: ["blur", "change"],
    },
  ],
});

// 监听 props.generalAdditionShow 的变化
watch(() => props.generalAdditionShow, (newValue) => {
  if (newValue) {
    // 打开弹窗时重置表单
    resetForm();
  }
});

// 监听 props.additionType 的变化
watch(() => props.additionType, (newVal) => {
  console.log("additionType changed:", newVal);
});

// 方法
/**
 * 关闭弹出层
 */
const closeGeneralAddition = () => {
  emit("close");
  promptShow.value = false;
  resetForm();
};

/**
 * 重置表单
 */
const resetForm = () => {
  formData.name = "";
  formData.categoryName = "";
  formData.contact_person = "";
  formData.categoryId = "";
  formData.phone = "";
  formData.address = "";
  formData.initialPayable = 0;
  // 重置表单验证状态
  if (uForm.value) {
    (uForm.value as any).resetFields();
  }
};

/**
 * 表单验证
 * @returns Promise<boolean> - 验证结果
 */
const validateForm = () => {
  return new Promise((resolve, reject) => {
    if (uForm.value) {
      (uForm.value as any).validate().then((valid: boolean) => {
        if (valid) {
          resolve(true);
        } else {
          uni.showToast({
            title: "请填写必填项",
            icon: "none",
          });
          reject(false);
        }
      }).catch((err: any) => {
        console.error("表单验证失败:", err);
        reject(false);
      });
    } else {
      reject(new Error("uForm ref is not available."));
    }
  });
};

/**
 * 确认提交
 */
const save = () => {
  validateForm()
    .then(() => {
      if (formData.initialPayable > 0) {
        //打开提示框
        promptShow.value = true;
      } else {
        handleConfirm();
      }
    })
    .catch((err) => {
      console.log("表单验证失败:", err);
    });
};

/**
 * 打开分类选择
 */
const openCategory = () => {
  CategoryShow.value = true;
};

/**
 * 更新商品类别显示状态
 * @param newMessage - 新的显示状态
 */
const updateProductCategoryShow = (newMessage: boolean) => {
  CategoryShow.value = newMessage;
};

/**
 * 更新商品类别信息
 * @param newMessage - 新的商品类别信息
 */
const updateProductCategory = (newMessage: { id: string; name: string }) => {
  console.log("选择分类:", newMessage);
  formData.categoryId = newMessage.id;
  formData.categoryName = newMessage.name;
};

/**
 * 处理确认保存
 */
const handleConfirm = async () => {
  if (props.additionType === 0) {
    try {
      const res: any = await addSupplier(formData);
      console.log(res);
      if (res.code === 0) {
        uni.showToast({
          title: "新增成功",
          icon: "success",
        });
        emit("confirm", res.data);
      } else {
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    } catch (err: any) {
      console.error("新增供应商失败:", err);
      uni.showToast({
        title: err.msg || err.message || "新增失败",
        icon: "none",
      });
    } finally {
      closeGeneralAddition();
    }
  } else if (props.additionType === 1) {
    try {
      const res: any = await saveCustomer(formData);
      if (res.code === 0) {
        uni.showToast({
          title: "新增成功",
          icon: "success",
        });
        emit("confirm", res.data);
      } else {
        uni.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    } catch (err: any) {
      console.error("新增客户失败:", err);
      uni.showToast({
        title: err.msg || err.message || "新增失败",
        icon: "none",
      });
    } finally {
      closeGeneralAddition();
    }
  }
};

/**
 * 处理取消保存
 */
const handleCancel = () => {
  promptShow.value = false;
};

// 生命周期钩子
onMounted(() => {
  // 设置表单验证规则
  if (uForm.value) {
    (uForm.value as any).setRules(rules);
  }
});
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    min-height: 600rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.header {
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-btn {
        color: #666;
        font-size: 32rpx;
        padding: 0 20rpx;
    }

    .confirm-btn {
        color: #2979ff;
    }

    .header-title {
        font-size: 30rpx;
        font-weight: 700;
        color: #333;
    }
}

.divider {
    height: 1rpx;
    background-color: #eee;
    margin: 0 40rpx;
}

.form-content {
    flex: 1;
    padding: 0 40rpx;
    overflow-y: auto;
}
</style>