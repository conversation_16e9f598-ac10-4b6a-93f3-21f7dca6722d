<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="5" @search-result="handleSearchResult" />

    <!-- 零售订单列表 -->
    <scroll-view scroll-y class="retail-list" :style="{ height: scrollViewHeight }" @scrolltolower="loadMore">
            <!-- 空状态显示 -->
      <EmptyState
        v-if="retailOrdersList.length === 0 && !loading"
        icon="📋"
        text="暂无订单数据"
        tip="请尝试其他搜索条件"
        :show-action="true"
        action-text="刷新"
        @action="getList"
      />
      <view class="retail-item attribute_font" v-for="(item, index) in retailOrdersList" :key="index"
        @click="jumpToView(item)">
        <view class="orange-tag" v-if="isSelect">退货完成</view>
        <view class="red-tag" v-if="isSelect">零售出库</view>
        <view class="item-content">
          <view class="info-row">
            <text class="label">客户</text>
            <text class="value">：{{ item.customer_name }}</text>
          </view>
          <view class="info-row">
            <text class="label">商品</text>
            <text class="value">：{{ item.short_desc }}</text>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">操作员</text>
              <text class="value">：{{ item.handler_name }}</text>
            </view>
            <view>
              <text class="sub-label">数量</text>
              <text class="value">：{{ item.item_count }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">金额合计</text>
              <text class="value">：{{ $toFixed(item.total_amount) }}</text>
            </view>
            <view>
              <text class="sub-label">收款优惠</text>
              <text class="value">：{{ $toFixed(item.discount) }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-row-item">
              <text class="label">收款金额</text>
              <text class="value">：{{ $toFixed(item.total_sale_price) }}</text>
            </view>
          </view>
        </view>
        <view class="info-bottom">
          <view class="info-row" style="margin-bottom: 0">
            <text class="label">单据日期</text>
            <text class="value">：{{ item.document_date }}</text>
          </view>
          <view class="info-row" style="margin-bottom: 0" v-if="isSelect">
            <text class="label">单据编号</text>
            <text class="value">：{{ item.order_no }}</text>
          </view>
          <button class="share-btn" v-if="!isSelect">
            分享
          </button>
        </view>
      </view>
      <!-- 加载更多/没有更多 -->
      <u-loadmore :status="loadStatus" loading-text="正在加载..." nomore-text="没有更多数据了" :icon="true" />
    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="isShowAddBtn" :jumpToId="6" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import eventBus from "@/utils/eventBus";
import { getRetailOrders, getRetailOrdersDetail } from '@/api/retailOrders';

const retailOrdersList = ref<any[]>([]);
const pageNum = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loading = ref(false);
const loadStatus = ref('loadmore'); // loadmore/loading/nomore
const scrollViewHeight = ref("500px");
const isSelect = ref(false); //是否为选择订单
const customer = ref(''); //客户id 
const isShowAddBtn = ref(true); //是否显示新增按钮

// 处理搜索结果
const handleSearchResult = (newVal: any) => {
  console.log('搜索结果:', newVal);

  if (newVal === null || newVal === undefined) {
    // 搜索为空，重新获取所有数据
    resetData();
  } else if (Array.isArray(newVal)) {
    // 搜索组件返回的是数组格式（res.data）
    retailOrdersList.value = newVal;

    // 搜索模式下禁用加载更多
    hasMore.value = false;
    loadStatus.value = 'nomore';

    // 如果搜索结果为空，显示提示
    if (retailOrdersList.value.length === 0) {
      console.log('搜索无结果');
      uni.showToast({
        title: '未找到匹配的零售订单',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: `搜索到${retailOrdersList.value.length}条结果`,
        icon: 'none'
      });
    }
  } else if (newVal && typeof newVal === 'object') {
    // 处理其他可能的对象格式
    if (newVal.results && Array.isArray(newVal.results)) {
      // 标准的分页搜索结果格式 {results: [], count: 0}
      retailOrdersList.value = newVal.results;
    } else {
      // 其他格式，尝试重置
      console.warn('未知的搜索结果格式:', newVal);
      retailOrdersList.value = [];
    }

    // 搜索模式下禁用加载更多
    hasMore.value = false;
    loadStatus.value = 'nomore';
  } else {
    // 其他情况，重新获取数据
    resetData();
  }

  console.log(retailOrdersList.value);
};

// 重置数据并重新加载
const resetData = () => {
  pageNum.value = 1;
  hasMore.value = true;
  loadStatus.value = 'loadmore';
  getList();
};

const initScrollViewHeight = () => {
  try {
    const info = uni.getSystemInfoSync();
    const screenWidth = info.screenWidth;
    const navBarHeight = 44;
    const searchHeight = (screenWidth * 80) / 750;
    const inputHeight = (screenWidth * 90) / 750;
    const totalHeight = navBarHeight + searchHeight + inputHeight;
    const scrollHeight = info.windowHeight - totalHeight;
    scrollViewHeight.value = `${scrollHeight}px`;
  } catch (e) {
    console.error("获取系统信息失败：", e);
  }
};

const getList = () => {
  if (loading.value || !hasMore.value) return;
  loading.value = true;
  loadStatus.value = 'loading';

  const params = {
    page: pageNum.value,
    page_size: pageSize.value,
    document_type: 'retail'
  };

  getRetailOrders(params)
    .then(res => {
      if (res.code === 0) {
        const list = res.data.results || [];

        if (pageNum.value === 1) {
          retailOrdersList.value = list;
        } else {
          retailOrdersList.value = retailOrdersList.value.concat(list);
        }

        // 判断是否还有更多数据
        if (retailOrdersList.value.length < (res.data.count || 0)) {
          hasMore.value = true;
          loadStatus.value = 'loadmore';
          pageNum.value++;
        } else {
          hasMore.value = false;
          loadStatus.value = 'nomore';
        }
      } else {
        uni.showToast({
          title: res.msg || '获取数据失败',
          icon: 'none'
        });
        loadStatus.value = 'loadmore';
      }
      loading.value = false;
    })
    .catch(e => {
      console.error('获取零售订单列表失败:', e);
      uni.showToast({ title: '加载失败', icon: 'none' });
      loadStatus.value = 'loadmore';
      loading.value = false;
    });
};

const loadMore = () => {
  if (hasMore.value && !loading.value) {
    getList();
  }
};

const jumpToView = (item: any) => {
  getRetailOrdersDetail(item.id).then((res) => {
    if (res.code == 0) {
      //如果是选择订单，则跳转至选择商品页面
      if (isSelect.value) {
        eventBus.$emit('selectRetailOrder', res.data.sales_out);
        uni.navigateTo({
          url: '/components/productSelection?data=' + JSON.stringify(res.data.sales_out.items) + "&type=7"
        });
        return;
        // uni.navigateBack();
        // return;
      }

      uni.navigateTo({
        url: '/pages/retailOrders/editRetailOrders/editRetailOrders?data=' + JSON.stringify(res.data.sales_out)
      })
    }
  })
};

onLoad((options: any) => {
  console.log(options);
  if (options.customer) {
    customer.value = options.customer;
  }
  if (options.isSelect) {
    isSelect.value = options.isSelect;
    isShowAddBtn.value = false;
  }
});

onMounted(() => {
  initScrollViewHeight();
  getList();
});

onShow(() => {
  resetData();
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.retail-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.retail-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.item-content {
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 400rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 100rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.sub-value {
  color: #333;
  margin-right: 20rpx;
}

.info-bottom {
  display: flex;
  flex-direction: column;

  .info-row {
    padding: 10rpx 0;
  }
}

.share-btn {
  color: #4080e8;
  font-size: 28rpx;
  text-align: right;
  padding: 10rpx 0;
  background: none;
  border: none;
  line-height: 1;
  margin: 0;
}

.share-btn::after {
  border: none;
}

.status-order-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}
</style>
