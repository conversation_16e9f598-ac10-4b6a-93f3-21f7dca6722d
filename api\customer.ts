import request from '@/utils/request.ts';
import store from '@/store';

// 定义客户数据接口
interface CustomerData {
  id?: string; // 客户ID，可选，因为新增时可能没有
  name: string; // 客户名称
  contact_person?: string; // 联系人
  phone_number?: string; // 电话号码
  address?: string; // 地址
  remark?: string; // 备注
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取客户列表
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getCustomerList = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/customers/`, data);
};

/**
 * 获取客户详情
 * @param {string} id - 客户ID
 * @returns {Promise<any>}
 */
export const getCustomerDetail = (id: string): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/customers/${id}/`);
};

/**
 * 新增客户
 * @param {CustomerData} data - 客户数据
 * @returns {Promise<any>}
 */
export const saveCustomer = (data: CustomerData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/customers/`, data);
};

/**
 * 更新客户
 * @param {CustomerData} data - 客户数据
 * @returns {Promise<any>}
 */
export const updateCustomer = (data: CustomerData): Promise<any> => {
  return request.put(`/ztx/${getLedgerName()}/customers/${data.id}/`, data);
};

/**
 * 删除客户
 * @param {string} id - 客户ID
 * @returns {Promise<any>}
 */
export const deleteCustomer = (id: string): Promise<any> => {
  return request.delete(`/ztx/${getLedgerName()}/customers/${id}/`);
};

/**
 * 搜索客户
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchCustomer = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/customers/search/`, data);
};
