<template>
	<view>
		<view class="container">
			<view>
				<!-- 搜索区域 -->
				<search :searchType="0" @search-result="handleSearchResult" />
				<!-- 展示列表区域 -->
				<goodsList :searchResults="searchResults" :type="9" v-model:isGoodsManageRefresh="isGoodsManageRefresh"
					:includeInactive="true" />
			</view>
			<!-- 底部按钮区域 -->
			<view class="inputArea">
				<Input style="height: 100%;" :isShowCamera="true" />
			</view>
		</view>
		<view>
			<!-- 对话悬浮以及添加商品按钮 -->
			<interactive :isShowAddBtn="true" :jumpToId="1" />
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import Input from '@/components/input/input.vue';
import search from '../../components/search.vue';
import goodsList from '../../components/goodsList.vue';
import interactive from '../../components/interactive.vue';

const searchResults = ref<any[]>([]);
const isGoodsManageRefresh = ref(false);
const needRefresh = ref(false); // 添加标记

onShow(() => {
  isGoodsManageRefresh.value = true;
});

const handleSearchResult = (results: any) => {
  searchResults.value = results;
};
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	background-color: #f1f1f1;
}
</style>
