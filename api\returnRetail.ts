import request from '@/utils/request.ts';
import http from '@/utils/http.ts';
import store from '@/store';
import pageDataManager, { PageDataManagerCallbacks } from '@/utils/pagedata.ts';

// 定义零售退货商品项接口
interface ReturnRetailItem {
  goods_id: string; // 商品ID
  quantity: number; // 数量
  price: number; // 单价
  total: number; // 总价
  // 根据实际API响应添加更多属性
  [key: string]: any;
}

// 定义零售退货数据接口
interface ReturnRetailData {
  order_type?: string; // 订单类型
  customer: string; // 客户ID
  customer_name?: string; // 客户名称
  return_date?: string; // 退货日期
  warehouse: string; // 仓库ID
  warehouse_name?: string; // 仓库名称
  items: ReturnRetailItem[]; // 商品列表
  total_amount?: number; // 总金额
  discount?: number; // 折扣金额
  discountRate?: number; // 折扣率
  actual_amount?: number; // 实际金额
  payment_method?: string; // 支付方式ID
  payment_method_name?: string; // 支付方式名称
  remark?: string; // 备注
  is_commit?: number; // 是否提交
  is_draft?: boolean; // 是否草稿
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

/**
 * 获取零售退货列表
 * @param {object} param - 查询参数
 * @param {PageDataManagerCallbacks} cbs - 回调函数
 * @returns {ReturnType<typeof pageDataManager>}
 */
export const getReturnRetailList = (param: object, cbs: PageDataManagerCallbacks = {}) => {
  return pageDataManager(`/ztx/${getLedgerName()}/salesreturns/`, 1, 20, param, cbs);
};

/**
 * 搜索零售退货
 * @param {object} data - 搜索参数
 * @returns {Promise<any>}
 */
export const searchReturnRetail = (data: object): Promise<any> => {
  return http.get(`/ztx/${getLedgerName()}/salesreturns/search/`, data);
};

/**
 * 获取零售退货详情
 * @param {string} id - 零售退货ID
 * @returns {Promise<any>}
 */
export const getReturnRetailDetail = (id: string): Promise<any> => {
  return http.get(`/ztx/${getLedgerName()}/salesreturns/${id}/`);
};

/**
 * 新增零售退货
 * @param {ReturnRetailData} data - 零售退货数据
 * @returns {Promise<any>}
 */
export const addReturnRetail = (data: ReturnRetailData): Promise<any> => {
  console.log('发送零售退货数据:', data);

  // 构建完整的请求数据，确保必填字段不为空
  const requestData: ReturnRetailData = {
    order_type: data.order_type || 'retail',
    customer: data.customer || '',
    customer_name: data.customer_name || '',
    return_date: data.return_date || new Date().toISOString().split('T')[0],
    warehouse: data.warehouse || '',
    warehouse_name: data.warehouse_name || '',
    items: data.items || [],
    total_amount: Number(data.total_amount) || 0,
    discount: Number(data.discount) || 0,
    discountRate: Number(data.discountRate) || 0,
    actual_amount: Number(data.actual_amount) || 0,
    payment_method: data.payment_method || '',
    payment_method_name: data.payment_method_name || '',
    remark: data.remark || '',
    is_commit: data.is_commit || 0,
    is_draft: data.is_draft || false,
    ...data, // 保留其他字段
  };

  // 验证必填字段
  const requiredFields: Array<keyof ReturnRetailData> = ['customer', 'warehouse', 'items'];
  const missingFields = requiredFields.filter(field => {
    if (field === 'items') {
      return !requestData.items || requestData.items.length === 0;
    }
    return !requestData[field];
  });

  if (missingFields.length > 0) {
    console.error('缺少必填字段:', missingFields);
    return Promise.reject(new Error(`缺少必填字段: ${missingFields.join(', ')}`));
  }

  console.log('最终发送数据:', requestData);
  return http.post(`/ztx/${getLedgerName()}/salesreturns/`, requestData);
};
