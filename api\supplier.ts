import request from '@/utils/request.ts';
import store from '@/store';

// 定义供应商数据接口
interface SupplierData {
  id?: string; // 供应商ID，可选，因为新增时可能没有
  name: string; // 供应商名称
  contact_person?: string; // 联系人
  phone_number?: string; // 电话号码
  address?: string; // 地址
  remark?: string; // 备注
  [key: string]: any; // 允许其他属性
}

// 获取账套名称的辅助函数
const getLedgerName = (): string => {
  return store.state.user.ledger_name;
};

console.log('当前账套名称：', getLedgerName());

/**
 * 获取供应商信息
 * @param {object} data - 查询参数
 * @returns {Promise<any>}
 */
export const getSupplier = (data: object): Promise<any> => {
  return request.get(`/ztx/${getLedgerName()}/suppliers/`, data);
};

/**
 * 新增供应商信息
 * @param {SupplierData} data - 供应商数据
 * @returns {Promise<any>}
 */
export const addSupplier = (data: SupplierData): Promise<any> => {
  return request.post(`/ztx/${getLedgerName()}/suppliers/`, data);
};
