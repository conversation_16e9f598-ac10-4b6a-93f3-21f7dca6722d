<template>
	<view class="container">
		<u-overlay v-model:show="localShow" @click="localShow = false">
			<view class="warp" v-if="show">
				<view class="rect" @tap.stop>
					<!-- 标题 -->
					<view class="Title">
						<!-- 标题内容 -->
						<text class="TitleContent">本次对话记录</text>
						<!-- 关闭图标 -->
						<view class="closeIcon" @click="cancel">
							<i-close-small theme="outline" size="24" fill="#ff0000" />
						</view>
					</view>
					<!-- 内容 -->
					<view class="content">
						<!-- 一组聊天记录 -->
						<view class="content-item" v-for="(item, index) in dialogRecords" :key="index">
							<!-- 用户发送记录 -->
							<view class="airRecord_warp">
								<image class="record-image" src="/static/img/用户.png" mode=""></image>
								<view class="airRecord">
									<text>{{item.userRecord}}</text>
									<view class="timeRecord">
										<view class="time">
											今日 14:01
										</view>
										<view class="me">
											我
										</view>
									</view>
								</view>
							</view>
							<!-- ai回复记录 -->
							<view class="userSendRecord_warp">
								<image class="record-image" src="/static/img/aiAssistant.png" mode=""></image>
								<view class="userSendRecord">
									<text>{{item.aiRecord}}</text>
									<view class="timeRecord">
										<view class="time">
											今日 14:02
										</view>
										<view class="me">
											仓小助
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</u-overlay>
	</view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
  show: boolean;
}

const props = defineProps<Props>();

const localShow = ref(props.show); //是否展示对话记录框

const emit = defineEmits(['update:show']);

watch(() => props.show, (newVal) => {
  localShow.value = newVal;
});

const cancel = () => {
  localShow.value = false;
  emit('update:show', false);
};
</script>

<style lang="scss">
	.container {
		height: 100%;
		width: 100%;
	}

	.warp {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
	}

	.rect {
		width: 80%;
		height: 80%;
		background-color: #fff;
		border-radius: 30rpx;
		border: 1px #333 solid;
		padding: 20rpx;
		border: 2px #50565f solid;
	}

	.Title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		padding-bottom: 10rpx;
		border-bottom: 1px #ccc solid;

		.TitleContent {
			width: 100%;
			font-size: 35rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-left: 40rpx;
		}

		.closeIcon {
			border-radius: 50%;
			width: 40rpx;
			border: 2px red solid;
			height: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.content {
		height: 90%;
		overflow-y: scroll;
	}

	.airRecord {
		width: 55%;
		background-color: #f1f4fa;
		padding: 10rpx 20rpx;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		font-size: 28rpx;
	}

	.timeRecord {
		display: flex;
		flex-direction: row-reverse;
		align-items: center;
		font-size: 20rpx;
		color: #999;
		margin-top: 10rpx;

		.me {
			margin-right: 20rpx;
		}
	}

	.userSendRecord {
		width: 55%;
		background-color: #f5f1ff;
		padding: 10rpx 20rpx;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		font-size: 28rpx;
	}
	
	.airRecord_warp{
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		.record-image{
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			margin-right: 20rpx;
		}
	}
	
	.userSendRecord_warp{
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		flex-direction: row-reverse;
		.record-image{
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			margin-left: 20rpx;
		}
	}
</style>