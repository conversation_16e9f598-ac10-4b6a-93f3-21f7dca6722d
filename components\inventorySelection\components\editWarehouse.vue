<template>
    <view>
        <u-popup :show="editShow" mode="bottom" round="8" @close="onClose">
            <view class="edit-warehouse-popup">
                <view class="header">
                    <view class="cancel" @click="onClose">取消</view>
                    <view class="title">{{ title }}</view>
                    <view class="confirm" @click="onConfirm">确定</view>
                </view>
                <view class="form">
                    <view class="form-item">
                        <view class="label"> 仓库名称<text class="required required-text">*</text> </view>
                        <u-input v-model="form.name" class="input" placeholder="请输入仓库名称" border="none" clearable
                            inputAlign="right" />
                    </view>
                    <view class="form-item">
                        <view class="label">该仓库为父仓</view>
                        <view class="switch-wrapper">
                            <u-switch v-model="form.is_parent" active-color="#2388fa" inactive-color="#e5eefd"
                                :width="56" :height="28" :border-radius="28" class="custom-switch" />
                        </view>
                    </view>
                    <template v-if="!form.is_parent">
                        <view class="form-item">
                            <view class="label">选择上级目录</view>
                            <u-input class="input" v-model="form.parentName" placeholder="选择上级目录" disabled
                                disabledColor="color: #fff" border="none" inputAlign="right" @tap="selectWarehouse" />
                            <scroll-view class="unitArea_content" scroll-y="true" :style="{ 'max-height': '220rpx' }"
                                v-if="isSelectParentShow">
                                <view class="unitArea_item" v-for="(item, index) in parentWarehouse" :key="index"
                                    :data-item="item" @tap="selectParent(item)">
                                    <text class="warehouseName warehouse-name-text">{{ item.name }}</text>
                                </view>
                            </scroll-view>
                        </view>
                        <view class="form-item">
                            <view class="label">备注</view>
                            <u-input v-model="form.remark" class="input" placeholder="请输入备注" border="none" clearable
                                inputAlign="right" />
                        </view>
                    </template>
                </view>
                <view v-if="editType == 1" class="delete-btn-wrapper">
                    <button class="delete-btn" @click="onDelete">删除</button>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { addWarehouses, editWarehouses, delWarehouses } from '@/api/warehouse';

interface WarehouseItem {
  id?: number;
  name: string;
  is_parent: boolean;
  parent?: number | string;
  parentName?: string;
  remark: string;
  children?: WarehouseItem[];
}

const props = defineProps({
  editShow: {
    type: Boolean,
    default: false,
  },
  editType: {
    /*
              0:新增
              1:编辑
          */
    type: Number,
    default: 0,
  },
  editWarehouseInfo: {
    type: Object as () => WarehouseItem,
    default: () => ({}),
  },
  editWarehouseParentInfo: {
    type: Object as () => WarehouseItem,
    default: () => ({}),
  },
  warehouseList: {
    type: Array as () => WarehouseItem[],
    default: () => [],
  },
});

const emit = defineEmits(['close', 'addWarehouse', 'editWarehouse', 'delWarehouse']);

const title = ref("新增仓库");
const isSelectParentShow = ref(false); //是否显示上级目录选择
const parentWarehouse = ref<WarehouseItem[]>([]); //上级目录
const form = reactive<WarehouseItem>({
  name: "",
  is_parent: true,
  parent: '',
  parentName: '',
  remark: "",
});
const currentWarehouse = ref<number | string | null>(null);

watch(() => props.editType, (newVal) => {
  if (newVal === 0) {
    title.value = "新增仓库";
  } else if (newVal === 1) {
    title.value = "编辑仓库";
  }
});

watch(() => props.editShow, (newValue) => {
  if (newValue) {
    if (props.editType === 0) {
      resetForm();
    } else {
      // 深拷贝，避免直接修改props
      console.log(props.editWarehouseInfo);

      Object.assign(form, JSON.parse(JSON.stringify(props.editWarehouseInfo)));
    }
  } else {
    isSelectParentShow.value = false;
  }
});

watch(() => props.warehouseList, (newValue) => {
  // 过滤出父仓库（is_parent为true的仓库）
  parentWarehouse.value = newValue.filter(item => item.is_parent === true);

  // 只有在编辑模式下才排除当前编辑的仓库
  if (props.editType === 1 && props.editWarehouseInfo.id) {
    parentWarehouse.value = parentWarehouse.value.filter(item => item.id !== props.editWarehouseInfo.id);
  }
});

watch(() => form.is_parent, (newVal) => {
  if (newVal) {
    form.parent = '';
    form.parentName = '';
  }
});

const onClose = () => {
  emit("close");
};

const onConfirm = () => {
  console.log(form);
  if (props.editType == 0) {
    addWarehouses(form).then((res: any) => {
      if (res.code == 0) {
        uni.showToast({
          title: '新增成功',
          icon: 'success',
        });
        emit("addWarehouse", res.data);
      } else {
        uni.showToast({
          title: '新增失败',
          icon: 'none',
        });
      }
    }).finally(() => {
      emit("close");
      resetForm();
    });
  } else if (props.editType == 1) {
    editWarehouses(form).then((res: any) => {
      if (res.code == 0) {
        uni.showToast({
          title: res.msg,
          icon: 'success',
        });
        // 传递原始的parent值，而不是this.currentWarehouse
        emit("editWarehouse", res.data, props.editWarehouseInfo.parent);
      } else {
        uni.showToast({
          title: res.msg,
          icon: 'none',
        });
      }
    }).catch((err) => {
      console.log(err);
      uni.showToast({
        title: '编辑失败',
        icon: 'none',
      });
    }).finally(() => {
      emit("close");
      resetForm();
    });
  }
};

// 选择上级目录
const selectWarehouse = () => {
  isSelectParentShow.value = !isSelectParentShow.value;
  // 只有在编辑模式下才排除当前编辑的仓库
  if (isSelectParentShow.value && props.editType === 1 && props.editWarehouseInfo.id) {
    parentWarehouse.value = props.warehouseList
      .filter(item => item.is_parent === true)
      .filter(item => item.id !== props.editWarehouseInfo.id);
  } else if (isSelectParentShow.value) {
    // 新增模式下，显示所有父仓库
    parentWarehouse.value = props.warehouseList.filter(item => item.is_parent === true);
  }
  console.log(isSelectParentShow.value);
};
//选择父级仓库
const selectParent = (item: WarehouseItem) => {
  console.log(item);
  currentWarehouse.value = form.parent;
  form.parent = item.id;
  form.parentName = item.name;
  isSelectParentShow.value = false;
};
const onDelete = () => {
  delWarehouses(form).then((res: any) => {
    if (res.code == 0) {
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      });
      emit("delWarehouse", form);
      emit("close");
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
      });
    }
  }).catch((err) => {
    console.log(err);
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    });
  }).finally(() => {
    emit("close");
  });
};

//重置表单数据
const resetForm = () => {
  form.name = "";
  form.is_parent = true;
  form.parent = '';
  form.parentName = '';
  form.remark = "";
};
</script>
<style lang="scss" scoped>
.edit-warehouse-popup {
    background: #fff;
    height: 800rpx;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx 10rpx 30rpx;
    border-bottom: 1px solid #eee;

    .cancel {
        color: #333;
        font-size: 30rpx;
    }

    .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #222;
    }

    .confirm {
        color: #3982cf;
        font-size: 30rpx;
    }
}

.form {
    padding: 20rpx 30rpx 0 30rpx;

    .form-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #eee;
        padding: 18rpx 0;
        position: relative;

        .label {
            width: 180rpx;
            color: #333;
            font-size: 28rpx;
            display: flex;
            align-items: center;

            .required {
                color: #ff4d4f;
                margin-left: 4rpx;
            }

            .required-text {
                // Styles for required-text
            }
        }

        .input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 28rpx;
            color: #666;
            outline: none;
            padding: 0;

            &::placeholder {
                color: #ccc;
            }
        }

        .select-btn {
            position: absolute;
            right: 10rpx;
            top: 50%;
            transform: translateY(-50%);
            z-index: 2;
        }
    }
}

.switch-wrapper {
    position: relative;
    display: inline-block;
    vertical-align: middle;

    .switch-corner-icon {
        position: absolute;
        right: -8rpx;
        top: -8rpx;
        background: #e5f1ff;
        border-radius: 50%;
        padding: 2rpx;
        z-index: 2;
    }
}

.custom-switch {
    // 可根据需要微调
}

.delete-btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;
}

.delete-btn {
    background: #e34c4c;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 0rpx 60rpx;
    font-size: 28rpx;
    font-weight: bold;
    letter-spacing: 2rpx;
}

.unitArea_content {
    position: absolute;
    width: 200rpx;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    z-index: 999;
    max-height: 220rpx;
    overflow-y: auto;
}

.unitArea_item {
    padding: 24rpx 32rpx;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;
    font-size: 28rpx;
    color: #333;
    transition: background-color 0.2s;

    &:last-child {
        border-bottom: none;
    }

    &:active {
        background-color: #f5f5f5;
    }

    &:hover {
        background-color: #f8f9fa;
    }

    .warehouseName {
        display: block;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .warehouse-name-text {
        // Styles for warehouse-name-text
    }
}
</style>
